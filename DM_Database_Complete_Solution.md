# DM Database Complete Solution Guide

## 🚨 Problem: "无效的表或视图名" Errors

You're encountering "invalid table or view name" errors in DM database. This indicates fundamental compatibility issues that need to be addressed step by step.

## 🎯 **Step-by-Step Solution**

### **Step 1: Test Basic DM Compatibility**

First, run the most minimal test to verify your DM database setup:

**File: `dm_minimal_test.sql`**
```sql
CREATE TABLE simple_test (
  id BIGINT NOT NULL PRIMARY KEY,
  name VARCHAR(100)
);

INSERT INTO simple_test (id, name) VALUES (1, 'test');
SELECT * FROM simple_test;
DROP TABLE simple_test;
```

### **Step 2: If Step 1 Fails - Check DM Database Setup**

If the minimal test fails, the issue is with your DM database environment:

1. **Check DM Database Version**:
   ```sql
   SELECT VERSION();
   ```

2. **Check User Permissions**:
   ```sql
   -- Ensure you have CREATE TABLE permissions
   SHOW GRANTS;
   ```

3. **Check Schema/Database Context**:
   ```sql
   -- Make sure you're in the right database
   SELECT DATABASE();
   ```

### **Step 3: If Step 1 Passes - Run Extended Test**

**File: `dm_basic_test.sql`**
```sql
-- Test 1: Simple table without IDENTITY
CREATE TABLE test_simple (
  id INT NOT NULL,
  name VARCHAR(50)
);

INSERT INTO test_simple (id, name) VALUES (1, 'test');
SELECT * FROM test_simple;
DROP TABLE test_simple;

-- Test 2: Table with quoted names
CREATE TABLE "test_quoted" (
  "id" INT NOT NULL,
  "name" VARCHAR(50)
);

INSERT INTO "test_quoted" ("id", "name") VALUES (1, 'test');
SELECT * FROM "test_quoted";
DROP TABLE "test_quoted";
```

### **Step 4: Use Conservative Schema**

If both tests pass, use the conservative conversion:

**File: `dump-web_spup_test-dm-conservative.sql`**

This version uses:
- ✅ **No IDENTITY** - Removed auto-increment for compatibility
- ✅ **Basic Data Types** - INT instead of TINYINT/SMALLINT
- ✅ **DATETIME** instead of TIMESTAMP(6)
- ✅ **No Comments** - Removed for maximum compatibility
- ✅ **No Constraints** - Simplified PRIMARY KEY handling

## 🔧 **Available Conversion Files**

| File | Compatibility Level | Use Case |
|------|-------------------|----------|
| `dm_minimal_test.sql` | **Highest** | Test basic DM functionality |
| `dm_basic_test.sql` | **High** | Test extended DM features |
| `dump-web_spup_test-dm-conservative.sql` | **High** | Production schema (no auto-increment) |
| `dump-web_spup_test-dm-specific.sql` | **Medium** | With IDENTITY support |
| `dump-web_spup_test-dm-final.sql` | **Lower** | Full featured (may not work) |

## 🚀 **Recommended Approach**

### **Option A: Conservative Approach (Recommended)**

1. **Test**: Run `dm_minimal_test.sql`
2. **Verify**: Run `dm_basic_test.sql`
3. **Deploy**: Use `dump-web_spup_test-dm-conservative.sql`
4. **Add Auto-increment Later**: Manually add sequences if needed

### **Option B: Troubleshooting Approach**

If you want to identify the exact issue:

1. **Start with minimal test**
2. **Gradually add complexity**
3. **Identify what breaks**
4. **Use appropriate conversion level**

## 🔍 **Common DM Database Issues**

### **Issue 1: Case Sensitivity**
```sql
-- Try quoted identifiers if case matters
CREATE TABLE "MyTable" ("ID" INT);
```

### **Issue 2: Reserved Words**
```sql
-- Some words might be reserved in DM
-- Use quotes around table/column names if needed
CREATE TABLE "order" ("id" INT);
```

### **Issue 3: Data Type Support**
```sql
-- DM might not support all MySQL data types
-- Use basic types: INT, BIGINT, VARCHAR, DATETIME, DATE
```

### **Issue 4: Schema Context**
```sql
-- Make sure you're in the right schema
USE your_schema_name;
-- Or specify schema explicitly
CREATE TABLE your_schema.table_name (...);
```

## 🛠 **Manual Fixes for Existing Files**

If you want to fix an existing SQL file manually:

### **Fix 1: Remove IDENTITY Parameters**
```sql
-- Change from:
id BIGINT IDENTITY(1,1) NOT NULL

-- To:
id BIGINT NOT NULL
```

### **Fix 2: Simplify Data Types**
```sql
-- Change from:
deleted TINYINT DEFAULT NULL

-- To:
deleted INT DEFAULT NULL
```

### **Fix 3: Remove TIMESTAMP Precision**
```sql
-- Change from:
create_time TIMESTAMP(6) DEFAULT NULL

-- To:
create_time DATETIME DEFAULT NULL
```

### **Fix 4: Add Schema Prefix (if needed)**
```sql
-- If tables need schema prefix:
CREATE TABLE web_spup_test.activity_info (...);
```

## 📋 **Debugging Steps**

1. **Check DM Database Logs** for detailed error messages
2. **Test Individual Tables** - Create one table at a time
3. **Use Quoted Identifiers** if case sensitivity is an issue
4. **Check DM Documentation** for version-specific syntax
5. **Verify Character Encoding** - Ensure UTF-8 support

## ✅ **Success Indicators**

- ✅ Minimal test runs without errors
- ✅ Basic test completes successfully  
- ✅ Tables are created in DM database
- ✅ INSERT/SELECT operations work
- ✅ Chinese characters display correctly

## 🎯 **Next Steps After Success**

1. **Add Primary Keys**: Manually add PRIMARY KEY constraints
2. **Add Indexes**: Create indexes for performance
3. **Add Sequences**: For auto-increment functionality if needed
4. **Test Application**: Verify application connectivity
5. **Import Data**: Load actual data from MySQL

The conservative approach should resolve your "invalid table or view name" errors and get your schema working in DM database!
