# 达梦数据库故障排除完整指南

## 🚨 **当前问题**

错误信息：`无效的表或视图名[DM_SYNTAX_TEST]`

这个错误表明达梦数据库在表名识别上存在问题，可能的原因包括：

1. **表名命名规则限制**
2. **大小写敏感性问题**
3. **用户权限问题**
4. **数据库配置问题**
5. **模式/用户上下文问题**

## 🔍 **逐步诊断方案**

### **第一步: 环境检查**
```sql
-- 运行文件: dm_environment_check.sql
-- 检查数据库版本、用户权限、当前模式等
SOURCE dm_environment_check.sql;
```

**检查要点:**
- ✅ 数据库版本是否支持
- ✅ 用户是否有CREATE TABLE权限
- ✅ 当前模式/用户上下文
- ✅ 表空间状态

### **第二步: 基础SQL测试**
```sql
-- 运行文件: dm_minimal_single_test.sql
-- 测试最基础的表创建功能
SOURCE dm_minimal_single_test.sql;
```

**测试内容:**
- ✅ 最简单的表创建
- ✅ 主键约束
- ✅ 基本数据类型

### **第三步: 表名格式测试**
```sql
-- 运行文件: dm_table_name_test.sql
-- 测试不同表名格式的兼容性
SOURCE dm_table_name_test.sql;
```

**测试范围:**
- ✅ 简单表名
- ✅ 大小写变化
- ✅ 下划线表名
- ✅ 带引号表名

### **第四步: 逐步功能测试**
```sql
-- 运行文件: dm_step_by_step_test.sql
-- 逐步测试各种数据类型和功能
SOURCE dm_step_by_step_test.sql;
```

### **第五步: 目标表测试**
```sql
-- 运行文件: dm_art_center_simple.sql
-- 测试实际的业务表结构
SOURCE dm_art_center_simple.sql;
```

## 🛠 **常见问题解决方案**

### **问题1: 表名大小写敏感**

**症状**: 表名无法识别
**解决方案**:
```sql
-- 尝试不同的大小写组合
CREATE TABLE TESTTABLE (id INT);  -- 全大写
CREATE TABLE testtable (id INT);  -- 全小写
CREATE TABLE "TestTable" (id INT); -- 带引号
```

### **问题2: 用户权限不足**

**症状**: 权限相关错误
**解决方案**:
```sql
-- 检查权限
SELECT * FROM USER_SYS_PRIVS WHERE PRIVILEGE LIKE '%CREATE%';

-- 如果权限不足，联系DBA授权
-- GRANT CREATE TABLE TO your_username;
```

### **问题3: 模式上下文问题**

**症状**: 表创建在错误的模式中
**解决方案**:
```sql
-- 检查当前模式
SELECT SYS_CONTEXT('USERENV', 'CURRENT_SCHEMA') AS current_schema;

-- 明确指定模式
CREATE TABLE your_schema.table_name (id INT);
```

### **问题4: 表名命名规则**

**症状**: 特定表名格式不被接受
**解决方案**:
```sql
-- 使用简单的表名
CREATE TABLE simple_name (id INT);

-- 避免特殊字符和保留字
-- 使用下划线分隔单词
```

### **问题5: 数据库配置问题**

**症状**: 基本功能都不工作
**解决方案**:
```sql
-- 检查数据库状态
SELECT STATUS FROM V$INSTANCE;

-- 检查表空间
SELECT TABLESPACE_NAME, STATUS FROM USER_TABLESPACES;
```

## 📋 **诊断检查清单**

运行以下检查，记录每个步骤的结果：

- [ ] **环境检查** - `dm_environment_check.sql`
  - [ ] 数据库版本: ___________
  - [ ] 当前用户: ___________
  - [ ] 当前模式: ___________
  - [ ] CREATE权限: ___________

- [ ] **基础测试** - `dm_minimal_single_test.sql`
  - [ ] 简单表创建: ___________
  - [ ] 主键约束: ___________
  - [ ] 数据插入: ___________

- [ ] **表名测试** - `dm_table_name_test.sql`
  - [ ] 简单表名: ___________
  - [ ] 大写表名: ___________
  - [ ] 下划线表名: ___________
  - [ ] 引号表名: ___________

- [ ] **功能测试** - `dm_step_by_step_test.sql`
  - [ ] BIGINT类型: ___________
  - [ ] DATETIME类型: ___________
  - [ ] 保留字处理: ___________

- [ ] **业务表测试** - `dm_art_center_simple.sql`
  - [ ] 重命名方案: ___________
  - [ ] 双引号方案: ___________

## 🎯 **根据诊断结果的解决方案**

### **如果基础测试失败:**
- 检查数据库连接和权限
- 联系DBA确认数据库配置
- 确认达梦数据库版本兼容性

### **如果表名测试失败:**
- 使用简单的表名格式
- 避免特殊字符和数字开头
- 统一使用小写或大写

### **如果功能测试失败:**
- 检查数据类型支持
- 简化表结构
- 逐个测试每个功能

### **如果业务表测试失败:**
- 使用重命名方案避免保留字
- 简化列定义
- 分步创建表结构

## 📞 **获取帮助**

如果所有测试都失败，请提供以下信息：

1. **达梦数据库版本**: `SELECT VERSION();`
2. **错误详细信息**: 完整的错误消息
3. **用户权限**: `SELECT * FROM USER_SYS_PRIVS;`
4. **当前环境**: 操作系统、安装方式等
5. **测试结果**: 每个测试文件的执行结果

这将帮助进一步诊断和解决问题。
