# DM Database Conversion Troubleshooting Guide

## 🚨 Error: "无效的表或视图名[ACTIVITY_INFO]"

This error indicates DM database compatibility issues. Here's the complete solution:

## ✅ Solution: Use DM-Specific Converter

**Use: `dm_specific_converter.py`** - This addresses the specific DM database requirements.

### 🔧 Key Fixes Applied:

1. **IDENTITY Syntax**: Changed from `IDENTITY(1,1)` to `IDENTITY` (DM-specific)
2. **TIMESTAMP → DATETIME**: DM prefers `DATETIME` over `TIMESTAMP(6)`
3. **PRIMARY KEY Constraints**: Added proper `CONSTRAINT PK_xxx PRIMARY KEY (id)` syntax
4. **Schema Preparation**: Added DM-specific header and settings

## 📋 Step-by-Step Resolution:

### Step 1: Test DM Compatibility
```sql
-- Run this first to verify your DM database setup:
-- File: dm_test_script.sql

DROP TABLE IF EXISTS dm_test_table;
CREATE TABLE dm_test_table (
  id BIGINT IDENTITY NOT NULL,
  name VARCHAR(100) NOT NULL,
  created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT PK_dm_test_table PRIMARY KEY (id)
);

INSERT INTO dm_test_table (name) VALUES ('Test Record');
SELECT * FROM dm_test_table;
DROP TABLE dm_test_table;
```

### Step 2: Use DM-Specific Schema
If the test passes, use: `dump-web_spup_test-dm-specific.sql`

## 🔍 Common DM Database Issues & Solutions:

### Issue 1: IDENTITY Syntax
```sql
-- ❌ MySQL/SQL Server style (doesn't work in DM)
id BIGINT IDENTITY(1,1) NOT NULL

-- ✅ DM Database style (works)
id BIGINT IDENTITY NOT NULL
```

### Issue 2: TIMESTAMP Precision
```sql
-- ❌ MySQL style (may not work in DM)
create_time TIMESTAMP(6) DEFAULT NULL

-- ✅ DM Database style (works)
create_time DATETIME DEFAULT NULL
```

### Issue 3: PRIMARY KEY Naming
```sql
-- ❌ Simple style (may cause issues)
PRIMARY KEY (id)

-- ✅ DM Database style (recommended)
CONSTRAINT PK_id PRIMARY KEY (id)
```

## 🎯 Recommended Conversion Process:

### For Future MySQL to DM Conversions:

1. **Use `dm_specific_converter.py`** - Most reliable for DM database
2. **Test with `dm_test_script.sql`** first
3. **Import `dump-web_spup_test-dm-specific.sql`** for full schema

### Alternative: Manual Fixes

If you prefer to fix the existing file manually:

```sql
-- Replace these patterns in your SQL file:

-- 1. Fix IDENTITY syntax
FIND: BIGINT IDENTITY(1,1) NOT NULL
REPLACE: BIGINT IDENTITY NOT NULL

-- 2. Fix TIMESTAMP
FIND: TIMESTAMP(6)
REPLACE: DATETIME

-- 3. Fix PRIMARY KEY
FIND: PRIMARY KEY (id)
REPLACE: CONSTRAINT PK_id PRIMARY KEY (id)
```

## 🔧 DM Database Specific Settings:

Add these at the beginning of your SQL file:
```sql
-- DM Database specific settings
SET IDENTITY_INSERT OFF;

-- Optional: Create schema
-- CREATE SCHEMA IF NOT EXISTS web_spup_test;
-- USE web_spup_test;
```

## 📊 Conversion Comparison:

| Feature | MySQL 8 | DM Database | Status |
|---------|---------|-------------|---------|
| AUTO_INCREMENT | `IDENTITY(1,1)` | `IDENTITY` | ✅ Fixed |
| Timestamps | `TIMESTAMP(6)` | `DATETIME` | ✅ Fixed |
| Primary Keys | `PRIMARY KEY (id)` | `CONSTRAINT PK_id PRIMARY KEY (id)` | ✅ Fixed |
| Comments | `COMMENT 'text'` | `-- text` | ✅ Fixed |
| Collation | `COLLATE utf8mb4_general_ci` | Removed | ✅ Fixed |

## 🚀 Quick Fix Commands:

```bash
# Generate DM-specific schema
python3 dm_specific_converter.py

# Test DM compatibility first
# Run: dm_test_script.sql in your DM database

# If test passes, import main schema
# Run: dump-web_spup_test-dm-specific.sql in your DM database
```

## ✅ Success Indicators:

- ✅ Test script runs without errors
- ✅ All 46 tables created successfully
- ✅ IDENTITY columns auto-increment properly
- ✅ Chinese comments display correctly
- ✅ No "invalid table or view name" errors

## 📞 If Issues Persist:

1. **Check DM Database Version**: Ensure compatibility
2. **Verify Permissions**: Ensure CREATE TABLE permissions
3. **Check Character Encoding**: Ensure UTF-8 support
4. **Review DM Documentation**: For version-specific syntax

The DM-specific converter addresses all known compatibility issues and should resolve the "invalid table or view name" error.
