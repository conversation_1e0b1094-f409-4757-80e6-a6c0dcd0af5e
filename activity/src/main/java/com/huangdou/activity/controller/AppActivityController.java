package com.huangdou.activity.controller;

import com.huangdou.activity.dto.ActivityListParam;
import com.huangdou.activity.dto.AppActivityEntryRuleDTO;
import com.huangdou.activity.service.IAppActivityEntryRuleService;
import com.huangdou.activity.service.IAppActivityEntryUserService;
import com.huangdou.activity.service.IAppActivityService;
import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.activity.AppActivity;
import com.spup.db.entity.activity.AppActivityEntryUser;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "活动管理")
@RestController
@RequestMapping(value = "/activity")
public class AppActivityController {
    @Resource
    private IAppActivityService iAppActivityService;
    @Resource
    private IAppActivityEntryRuleService iAppActivityEntryRuleService;
    @Resource
    private IAppActivityEntryUserService iAppActivityEntryUserService;

    @ApiOperation(value = "查询活动")
    @GetMapping(value="/list")
    public CommonResult<?> list (ActivityListParam param, HttpServletRequest req) throws UnsupportedEncodingException {
        String unionid = (String)req.getSession().getAttribute("unionid");
        //String openid = (String)req.getSession().getAttribute("openid");
        Page<AppActivity> activityList = iAppActivityService.getActivityList(param);

        List<AppActivity> returnActivityList  = activityList.toList().stream().filter(
                (AppActivity activity) ->
                        iAppActivityEntryRuleService.getRuleByActivityId(activity.getId(),unionid)!=null).collect(Collectors.toList());

        return CommonResult.succeeded(returnActivityList);
    }

    @ApiOperation(value = "查看活动")
    @GetMapping(value="/viewActivity/{id}")
    public CommonResult<?> viewActivity (@PathVariable Long id) {
        return CommonResult.succeeded(iAppActivityService.view(id));
    }

    @ApiOperation(value = "查询报名情况")
    @GetMapping(value="/viewEntry/{activityId}")
    public CommonResult<?> viewEntry (@PathVariable long activityId, HttpServletRequest req) {
        String unionid = (String)req.getSession().getAttribute("unionid");

        return CommonResult.succeeded(iAppActivityEntryRuleService.getRuleByActivityId(activityId,unionid));
    }

    @ApiOperation(value = "报名")
    @PostMapping(value="/entry")
    public CommonResult<?> entry (@RequestBody AppActivityEntryUser user, HttpServletRequest req) {
        String unionid = (String)req.getSession().getAttribute("unionid");

        AppActivityEntryRuleDTO ruleByActivityId = iAppActivityEntryRuleService.getRuleByActivityId(user.getActivityId(), unionid);
        if(ruleByActivityId == null){
            return CommonResult.failed(1000,"报名异常");
        } else {
            if(ruleByActivityId.getMyEntry()==null){
                if(ruleByActivityId.getEntryRemind()>0) {
                    return CommonResult.succeeded(iAppActivityEntryUserService.save(user, unionid));
                } else {
                    return CommonResult.failed(1001,"报名名额不足");
                }
            } else {
                return CommonResult.failed(1009,"您已报名，无须重复报名！");
            }
        }

    }

    @ApiOperation(value = "我的活动")
    @GetMapping(value="/myEntry")
    public CommonResult<?> myEntry (HttpServletRequest req) {
        String unionid = (String)req.getSession().getAttribute("unionid");

        return CommonResult.succeeded(iAppActivityService.getMyActivityList(unionid));
    }

}
