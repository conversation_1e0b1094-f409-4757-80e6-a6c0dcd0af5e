package com.huangdou.activity.controller;


import com.huangdou.activity.service.ActivityRoundService;
import com.spup.db.entity.activity.Activity;
import com.spup.db.entity.activity.ActivityRound;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "base controller" )
@RestController
@RequestMapping(value="/")
public abstract class BaseController {
    @Resource(name = "ActivityRoundServiceImpl")
    private ActivityRoundService actRoundService;

    protected void updateActivStatus(List<Activity> activList) {
        actRoundService.updateActivStatus(activList);
    }

    protected void updateActRoundStatus(List<ActivityRound> actRoundList) {
       actRoundService.updateActRoundStatus(actRoundList);
    }

    protected Activity.ActStatusEnum getActStatus(String activityId) {
        return actRoundService.getFreshedActStatus(activityId);
    }

    protected ActivityRound.ActRoundStatusEnum getActRoundStatus(String actRoundId) {
        return actRoundService.getFreshActRoundStatus(actRoundId);
    }
}



