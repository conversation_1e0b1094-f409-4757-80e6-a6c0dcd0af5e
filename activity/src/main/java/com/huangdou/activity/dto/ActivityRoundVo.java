package com.huangdou.activity.dto;


import com.spup.db.entity.activity.ActivityRound;

public class ActivityRoundVo {
    
    private ActivityRound actRound;
    private boolean greyButton;
    private boolean visible;
    private String buttonInfo;

    public ActivityRound getActRound() {
        return actRound;
    }
    public void setActRound(ActivityRound actRound) {
        this.actRound = actRound;
        ActivityRound.ActRoundStatusEnum status = actRound.getStatus();
        if (status == ActivityRound.ActRoundStatusEnum.SUBMITTING) {
            this.greyButton = false;
        } else {
            this.greyButton = true;
        }

        if (greyButton) {
            if(status == ActivityRound.ActRoundStatusEnum.WAITING && actRound.getActRoundSubmitNumber() >= actRound.getActRoundMaxSubmitNum()){
                this.buttonInfo = "名额已满";
            }
            if(status == ActivityRound.ActRoundStatusEnum.WAITING && actRound.getActRoundSubmitNumber() < actRound.getActRoundMaxSubmitNum()){
                this.buttonInfo = "报名已结束";
            }
            if(status == ActivityRound.ActRoundStatusEnum.RUNNING){
                this.buttonInfo = "活动已开始";
            }
            if(status == ActivityRound.ActRoundStatusEnum.READY){
                this.buttonInfo = "报名未开始";
            }
            if(status == ActivityRound.ActRoundStatusEnum.SUBMITPAUSED){
                this.buttonInfo = "报名已结束";
            }
            if(status == ActivityRound.ActRoundStatusEnum.CLOSED){
                this.buttonInfo = "活动已结束";
            }
            if(status == ActivityRound.ActRoundStatusEnum.DEPRECATED){
                this.buttonInfo = "活动已下线";
            }

        } else {
            this.buttonInfo = "报名";
        }

        if (status == ActivityRound.ActRoundStatusEnum.SUBMITTING
                || status == ActivityRound.ActRoundStatusEnum.WAITING
                || status == ActivityRound.ActRoundStatusEnum.RUNNING) {
            this.visible = true;
        } else {
            this.visible = false;
        }
    }
    public boolean isGreyButton() {
        return greyButton;
    }
    public String getButtonInfo() {

        return buttonInfo;
    }
    public boolean isVisible() {
        return visible;
    }

}
