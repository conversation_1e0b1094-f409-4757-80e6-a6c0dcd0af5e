package com.huangdou.activity.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.huangdou.activity.dto.ActivityRoundCheckinDTO;
import com.huangdou.activity.dto.ActivityRoundCheckinVo;
import com.huangdou.activity.service.ActivityCreateOrderService;
import com.huangdou.activity.service.ActivityRoundService;
import com.huangdou.commons.api.CommonResult;
import com.spup.db.dao.activity.ActivityDao;
import com.spup.db.dao.activity.ActivityRoundDao;
import com.spup.db.dao.activity.ActivitySubmitCustomerDao;
import com.spup.db.entity.activity.Activity;
import com.spup.db.entity.activity.ActivityRound;
import com.spup.db.entity.activity.ActivitySubmitCustomer;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service("ActivityRoundServiceImpl")
public class ActivityRoundServiceImpl implements ActivityRoundService {
    @Resource
    protected ActivityDao actDao;
    @Resource
    protected ActivityRoundDao actRoundDao;
    @Resource
    protected ActivitySubmitCustomerDao actCustomerDao;
    @Resource
    protected ObjectMapper objectMapper;
    @Resource
    protected ActivityCreateOrderService orderService;

    @Value("${spring.profiles.active}")
    protected String profile;

    @Override
    public Activity.ActStatusEnum getFreshedActStatus(String activId) {
        Optional<Activity> actOpt = actDao.getByActivityId(activId);
        if (!actOpt.isPresent()) {
            return null;
        }
        Activity act = actOpt.get();
        Activity.ActStatusEnum result = act.getStatus();
        if (result == Activity.ActStatusEnum.DEPRECATED || result == Activity.ActStatusEnum.PREVIEW ) {
            return result;
        }
        if (act.getStartDateTime().isAfter(LocalDateTime.now())) {
            result = Activity.ActStatusEnum.READY;
        }
        if (act.getStartDateTime().isBefore(LocalDateTime.now())) {
            result = Activity.ActStatusEnum.RUNNING;
        }
        if (act.getEndDateTime().isBefore(LocalDateTime.now())) {
            result = Activity.ActStatusEnum.CLOSED;
        }
        return result;
    }

    @Override
    public void updateActivStatus(List<Activity> activList) {
        activList.parallelStream().forEach(act -> {
            act.setStatus(getFreshedActStatus(act.getActivityId()));
            actDao.save(act);
        });
    }

    @Override
    public ActivityRound.ActRoundStatusEnum getFreshActRoundStatus(String actRoundId) {
        Optional<ActivityRound> actRoundOpt = actRoundDao.findByActRoundId(actRoundId);
        if (!actRoundOpt.isPresent()) {
            return null;
        }
        ActivityRound actRound = actRoundOpt.get();
        LocalDateTime submitStartDateTime = actRound.getActRoundSubmitStartDateTime();
        LocalDateTime submitEndDateTime = actRound.getActRoundSubmitEndDateTime();
        LocalDateTime startDateTime = actRound.getActRoundStartDateTime();
        LocalDateTime endDateTime = actRound.getActRoundEndDateTime();
        LocalDateTime now = LocalDateTime.now();
        ActivityRound.ActRoundStatusEnum result = actRound.getStatus();
        if (result == ActivityRound.ActRoundStatusEnum.DEPRECATED) {
            return result;
        }
        if (submitStartDateTime.isAfter(now)) {
            result = ActivityRound.ActRoundStatusEnum.READY;
        }
        if (submitStartDateTime.isBefore(now)) {
            if (result != ActivityRound.ActRoundStatusEnum.SUBMITPAUSED) {
                result = ActivityRound.ActRoundStatusEnum.SUBMITTING;
            }
        }
        if (submitEndDateTime.isBefore(now) ||
                actRound.getActRoundSubmitNumber()>=actRound.getActRoundMaxSubmitNum()) {
            result = ActivityRound.ActRoundStatusEnum.WAITING;
        }
        if (startDateTime.isBefore(now)) {
            result = ActivityRound.ActRoundStatusEnum.RUNNING;
        }
        if (endDateTime.isBefore(now)) {
            result = ActivityRound.ActRoundStatusEnum.CLOSED;
        }
        return result;
    }

    @Override
    public void updateActRoundStatus(List<ActivityRound> actRoundList) {
        actRoundList.parallelStream()
            .forEach(round -> actRoundDao.updateStatus(getFreshActRoundStatus(round.getActRoundId()),round.getId()));
    }

    @Override
    public List<ActivityRoundCheckinDTO> getActRoundCheckInDto(String unionid) {
        // TODO MapStruct to mapp dto
        List<String> availableSubRecordList = actCustomerDao.findByUnionid(unionid).stream()
                .filter(this::canCheckin )
                .map(ActivitySubmitCustomer::getActRoundId)
                .distinct()
                .collect(Collectors.toList());

        List<ActivityRoundCheckinDTO> result = availableSubRecordList.stream()
                .map(roundId -> {
                    ActivityRoundCheckinDTO checkinDTO = new ActivityRoundCheckinDTO();
                    Optional<ActivityRound> actRoundOpt = actRoundDao.findByActRoundId(roundId);
                    if(!actRoundOpt.isPresent()){
                        return null;
                    }
                    ActivityRound round = actRoundOpt.get();
                    Optional<Activity> actOpt = actDao.getByActivityId(actRoundOpt.get().getActivityId());
                    if(!actOpt.isPresent()){
                        return null;
                    }
                    Activity activity = actOpt.get();
                    if(activity.getStatus() == Activity.ActStatusEnum.DEPRECATED){
                        return null;
                    }
                    checkinDTO.setActivity(activity);
                    List<ActivitySubmitCustomer> submitCustomerList;
                    submitCustomerList = actCustomerDao.findActivitySubmitCustomerByActRoundIdAndUnionid(roundId, unionid);
                    submitCustomerList = submitCustomerList.stream()
                            .filter(this::canCheckin )
                                .collect(Collectors.toList());
                    checkinDTO.setStatus(submitCustomerList.get(0).getStatus());

                    ActivityRoundCheckinVo vo = new ActivityRoundCheckinVo();
                    vo.setActRound(round);
                    vo.setCheckInStatus(checkinDTO.getStatus());
                    checkinDTO.setRoundVo(vo);
                    return checkinDTO;
                })
                .filter(Objects::nonNull)
                .filter(actRoundCheckinDto ->
                        canActRoundCheckin(actRoundCheckinDto.getRoundVo().getActRound()))
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public CommonResult<String> submit(List<ActivitySubmitCustomer> activityUser, String unionid, ActivityRound actRound)
        throws JsonProcessingException{
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'submit'");
    }
    
    @Override
    public boolean canCustomerSubmitActRound(String roundId, String unionid) {
        List<ActivitySubmitCustomer> subCustomerList =
                actCustomerDao.findActivitySubmitCustomerByActRoundIdAndUnionidAndType(
                        roundId, unionid, ActivitySubmitCustomer.SubmitCustomerTypeEnum.ADULT);
        subCustomerList = subCustomerList.stream().
                filter(subCustomer -> subCustomer.getStatus()!=ActivitySubmitCustomer.SubmitCustomerStatusEnum.CANCELLED)
                .collect(Collectors.toList());
        return subCustomerList.isEmpty();
    }

    @Override
    public boolean canActRoundCancel(List<ActivitySubmitCustomer> subRecordList, ActivityRound actRound) {
        if(actRound.getStatus() ==  ActivityRound.ActRoundStatusEnum.DEPRECATED
            || actRound.getStatus() == ActivityRound.ActRoundStatusEnum.CLOSED
            || actRound.getStatus() == ActivityRound.ActRoundStatusEnum.RUNNING ){
            return false;
        }
        LocalDateTime actRoundSubmitEndDateTime = actRound.getActRoundSubmitEndDateTime();
        if (actRoundSubmitEndDateTime.isBefore(LocalDateTime.now())){
            return false;
        }
        return subRecordList.stream()
                .allMatch(customer ->
                        customer.getStatus() == ActivitySubmitCustomer.SubmitCustomerStatusEnum.SUBMITTED);
    }

    @Override
    public boolean canActRoundCheckin(ActivityRound actRound) {
        boolean result = false;
        updateActRoundStatus(Collections.singletonList(actRound));
        ActivityRound.ActRoundStatusEnum status = actRound.getStatus();
        if ( status == ActivityRound.ActRoundStatusEnum.WAITING
                || status == ActivityRound.ActRoundStatusEnum.SUBMITTING
                || status == ActivityRound.ActRoundStatusEnum.RUNNING
                || status == ActivityRound.ActRoundStatusEnum.SUBMITPAUSED) {
            result = true;
        }
        return result;
    }

    @Override
    public CommonResult<ActivitySubmitCustomer> checkInAction(String actRoundId, String unionid) {
        List<ActivitySubmitCustomer> subCustomerList =
                actCustomerDao.findActivitySubmitCustomerByActRoundIdAndUnionid(
                        actRoundId, unionid);
        subCustomerList = subCustomerList.stream()
            .filter(subCustomer -> subCustomer.getStatus()!= ActivitySubmitCustomer.SubmitCustomerStatusEnum.CANCELLED)
            .filter(subCustomer -> subCustomer.getStatus()!= ActivitySubmitCustomer.SubmitCustomerStatusEnum.CHECKEDIN)
            .collect(Collectors.toList());
        Optional<ActivityRound> actRoundOpt = actRoundDao.findByActRoundId(actRoundId);
        if(!canActRoundCheckin(actRoundOpt.get())){
            return CommonResult.failed("未找到待签到记录");
        }
        subCustomerList.forEach(subCustomer -> {
            subCustomer.setCheckInDateTime(LocalDateTime.now());
            subCustomer.setStatus(ActivitySubmitCustomer.SubmitCustomerStatusEnum.CHECKEDIN);
            actCustomerDao.save(subCustomer);
        });

        return CommonResult.succeeded(null);
    }
    @Override
    public CommonResult<String> cancelActRound(
            List<ActivitySubmitCustomer> subRecordList,
            ActivityRound actRound) 
        throws JsonProcessingException {
        
        subRecordList = subRecordList.stream()
                        .filter(subRecord ->
                                subRecord.getStatus() == ActivitySubmitCustomer.SubmitCustomerStatusEnum.SUBMITTED)
                        .collect(Collectors.toList());

        if(!canActRoundCancel(subRecordList,actRound)){
            return CommonResult.failed("预约已无法取消");
        }

        // if (subRecordList.stream().anyMatch(
        //     subRecord -> subRecord.getStatus() == 
        //         ActivitySubmitCustomer.SubmitCustomerStatusEnum.CHECKEDIN)){
        //     return CommonResult.failed("签到后不可取消");
        // }

        subRecordList.stream()
                .forEach( subRecord -> {
                    //取消预约记录
                    subRecord.setStatus(ActivitySubmitCustomer.SubmitCustomerStatusEnum.CANCELLED);
                    actCustomerDao.save(subRecord);
                });
        int addSubmitNumber = getCancelRecordSize(subRecordList, actRound);
        actRoundDao.updateSubmitNumber(addSubmitNumber,actRound.getId(),addSubmitNumber,addSubmitNumber);

        //远程取消订单票务记录
        String otaOrderId = subRecordList.get(0).getCreateBy();
        String unionid = subRecordList.get(0).getUnionid();

        boolean needTicket = true;
        if(needTicket){
            orderService.cancelFromActivity(otaOrderId,unionid);
        }

        return CommonResult.succeeded("活动预约已取消");
    }

    private Integer getCancelRecordSize(List<ActivitySubmitCustomer> subRecordList, ActivityRound actRound ) {
        int result = 0;
        if (actRound.getType() == ActivityRound.ActRoundTypeEnum.CHILD) {
            result = (int) subRecordList.stream()
                .filter(customer -> 
                    customer.getType() == ActivitySubmitCustomer.SubmitCustomerTypeEnum.CHILD).count();
            result = result * -1;
            //result = -1;
        } else { //NORMAL,NOLIMIT
            result = subRecordList.size();
            result = result * -1;
        }

        return result;
    }

    private boolean canCheckin(ActivitySubmitCustomer subCustomer) {
        return (subCustomer.getStatus() == ActivitySubmitCustomer.SubmitCustomerStatusEnum.SUBMITTED)
                || (subCustomer.getStatus() == ActivitySubmitCustomer.SubmitCustomerStatusEnum.CHECKEDIN);
    }

    protected ActivitySubmitCustomer.SubmitCustomerTypeEnum parseCustomerTypeByAge (Integer age){
        if( age < 18 ){
            return ActivitySubmitCustomer.SubmitCustomerTypeEnum.CHILD;
        }
        return ActivitySubmitCustomer.SubmitCustomerTypeEnum.ADULT;
    }
}
