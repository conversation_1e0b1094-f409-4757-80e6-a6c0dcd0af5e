package com.huangdou.activity.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.activity.ActivityRound;
import com.spup.db.entity.activity.ActivitySubmitCustomer;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Service
public class ActivityRoundServiceNormalImpl extends ActivityRoundServiceImpl {

    @Override
    @Transactional
    public CommonResult<String> submit(List<ActivitySubmitCustomer> activityUserList,
                                       String unionid,
                                       ActivityRound actRound) throws JsonProcessingException {

        String submitId =  LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmssSSS"));

        ActivitySubmitCustomer activityUser = activityUserList.get(0);
        activityUser.setType(parseCustomerTypeByAge(activityUser.getAge()));
        activityUser.setUnionid(unionid);
        activityUser.setSubmitId(submitId);
        activityUser.setStatus(ActivitySubmitCustomer.SubmitCustomerStatusEnum.SUBMITTED);
        if (activityUser.getType() != ActivitySubmitCustomer.SubmitCustomerTypeEnum.ADULT) {
            return CommonResult.failed("未成年人无法报名");
        }
        if (actRound.getActRoundSubmitNumber() >= actRound.getActRoundMaxSubmitNum()) {
            return CommonResult.failed("名额不足");
        }
        if (!canCustomerSubmitActRound(actRound.getActRoundId(), unionid)) {
            return CommonResult.failed("请勿重复报名");
        }

        // TODO
        // getCancelRecordSize()
        int addSubmitNumber = activityUserList.size();
        int update = actRoundDao.updateSubmitNumber(addSubmitNumber, actRound.getId(), addSubmitNumber,addSubmitNumber);
        if( update == 0 ){
            return CommonResult.failed("报名失败");
        }
        actCustomerDao.save(activityUser);
        boolean needTicket = true;
        if(needTicket){
            String orderNo = orderService.saveFromActivity(actRound,activityUserList,unionid);
            activityUserList.stream().forEach(customer -> {
                Optional<ActivitySubmitCustomer> customerOptional = actCustomerDao.findById(customer.getId());
                if(customerOptional.isPresent()){
                    ActivitySubmitCustomer customer1 = customerOptional.get();
                    customer1.setCreateBy(orderNo);
                    actCustomerDao.save(customer1);
                }

            });
        }

        return CommonResult.succeeded("报名成功");
    }
}
