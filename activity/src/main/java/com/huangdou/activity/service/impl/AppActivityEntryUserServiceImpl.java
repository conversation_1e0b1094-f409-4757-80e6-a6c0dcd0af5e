package com.huangdou.activity.service.impl;

import com.huangdou.activity.service.IAppActivityEntryUserService;

import com.spup.db.dao.activity.AppActivityEntryUserDao;
import com.spup.db.entity.activity.AppActivityEntryUser;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AppActivityEntryUserServiceImpl  implements IAppActivityEntryUserService {
    @Resource
    private AppActivityEntryUserDao appActivityEntryUserDao;

    @Override
    public int getActivityEntryInfo(long activityId){
        List<AppActivityEntryUser> byActivityId = appActivityEntryUserDao.findByActivityId(activityId);
        return byActivityId.size();
    }

    public AppActivityEntryUser getMyEntryInfo(long activityId,String unionid){
        List<AppActivityEntryUser> appActivityEntryUsers = appActivityEntryUserDao.findByActivityIdAndUserUnionid(activityId,unionid);
        if(CollectionUtils.isEmpty(appActivityEntryUsers)){
            return null;
        }
        return appActivityEntryUsers.get(0);
    }

    @Override
    public AppActivityEntryUser save(AppActivityEntryUser user, String unionid) {
        user.setUserUnionid(unionid);
        AppActivityEntryUser entryUser = appActivityEntryUserDao.save(user);
        return entryUser;
    }
}
