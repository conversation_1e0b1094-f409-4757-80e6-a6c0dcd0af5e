package com.huangdou.activity.service.impl;

import com.huangdou.activity.dto.ActivityListParam;
import com.huangdou.activity.service.IAppActivityService;
import com.spup.db.dao.activity.AppActivityDao;
import com.spup.db.dao.activity.AppActivityEntryRuleDao;
import com.spup.db.dao.activity.AppActivityEntryUserDao;
import com.spup.db.entity.activity.AppActivity;
import com.spup.db.entity.activity.AppActivityEntryUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AppActivityServiceImpl implements IAppActivityService {
    @Resource
    private AppActivityDao appActivityDao;
    @Resource
    private AppActivityEntryUserDao appActivityEntryUserDao;
    @Resource
    private AppActivityEntryRuleDao appActivityEntryRuleDao;
    @Override
    public Page<AppActivity> getActivityList(ActivityListParam listParam) {


        Pageable pageable = PageRequest.of(listParam.getPageNum()-1, listParam.getPageSize());
       /* if(!StringUtils.isEmpty(listParam.getStartTime())){
            try {
                criteria.andStartTimeGreaterThanOrEqualTo(DateTimeUtil.getDateTime(listParam.getStartTime(),DateTimeUtil.PATTERN_2).getTime());
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        if(!StringUtils.isEmpty(listParam.getEndTime())){
            try {
                criteria.andStartTimeLessThanOrEqualTo(DateTimeUtil.getDateTime(listParam.getEndTime(),DateTimeUtil.PATTERN_2).getTime());
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
         example.setOrderByClause(" start_time desc");
        */
        Page<AppActivity> appActivities = appActivityDao.findAll(getQuery(listParam),pageable);

        return appActivities;
    }


    private Specification<AppActivity> getQuery(ActivityListParam condition) {
        return (Specification<AppActivity>) (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();

            if (StringUtils.hasLength(condition.getTitle())) {
                predicate.getExpressions().add(criteriaBuilder.like(root.get("title"), "%" + condition.getTitle() + "%"));
                // 查询条件等等
            }
            Order sort = criteriaBuilder.asc(root.get("startTime"));
            return query.orderBy(sort).where(predicate).getRestriction();
        };
    }


    @Override
    public AppActivity view(long id) {
        Optional<AppActivity> activityById = appActivityDao.findById(id);
        if(activityById.isPresent()){
            return activityById.get();
        }
        return null;
    }

    @Override
    public List<AppActivity> getMyActivityList(String unionid) {

        List<AppActivityEntryUser> appActivityEntryUsers = appActivityEntryUserDao.findByUserUnionid(unionid);


        List<Long> activityIds = appActivityEntryUsers.stream().map((AppActivityEntryUser user) -> {
            return user.getActivityId();
        }).collect(Collectors.toList());


        List<AppActivity> appActivities = appActivityDao.findByIdIn(activityIds);

        return appActivities;
    }


}
