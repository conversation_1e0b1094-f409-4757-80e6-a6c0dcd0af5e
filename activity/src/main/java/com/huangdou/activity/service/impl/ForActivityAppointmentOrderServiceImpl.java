package com.huangdou.activity.service.impl;


import com.huangdou.activity.service.ForActivityAppointmentOrderService;
import com.huangdou.activity.service.ForActivityBatchService;
import com.huangdou.commons.api.CommonResult;

import com.spup.db.dao.appointment.AppAppointmentOrderDao;
import com.spup.db.dao.appointment.AppAppointmentSuborderDao;
import com.spup.db.entity.appointment.AppAppointmentOrder;
import com.spup.db.entity.appointment.AppAppointmentSuborder;
import com.spup.enums.OrderStatusEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
public class ForActivityAppointmentOrderServiceImpl implements ForActivityAppointmentOrderService {
    @Resource
    private AppAppointmentOrderDao appAppointmentOrderDao;
    @Resource
    private AppAppointmentSuborderDao appAppointmentSuborderDao;
    @Resource
    private ForActivityBatchService batchService;
    @Override
    public CommonResult<?> cancel(String orderNo, String unionid) {
        Optional<AppAppointmentOrder> orderOptional =  appAppointmentOrderDao.getByOrderNo(orderNo);
        if(!orderOptional.isPresent()){
            return CommonResult.failed("订单不存在");
        }
        AppAppointmentOrder order = orderOptional.get();
        if(order.getOrderStatus().shortValue() != OrderStatusEnum.SUCCESS.getCode()){
            return CommonResult.failed("订单状态异常");
        }
        if(!order.getOwnerUnionid().equals(unionid)){
            return CommonResult.failed("无权操作");
        }
        order.setOrderStatus(OrderStatusEnum.CANCELED.getCode());

        appAppointmentOrderDao.save(order);

        List<AppAppointmentSuborder> suborders = appAppointmentSuborderDao.findByOrderNo(orderNo);
        suborders.forEach( sub -> {
            if(sub.getSuborderStatus().shortValue() == OrderStatusEnum.SUCCESS.getCode()) {
                sub.setSuborderStatus(OrderStatusEnum.CANCELED.getCode());
                appAppointmentSuborderDao.save(sub);
                //batchService.updateRemaining(order.getBatchNo(), (byte)1, 1);
            }
        });

        return CommonResult.succeeded("");
    }
}
