package com.huangdou.activity.service.impl;

import com.huangdou.activity.service.ForActivityBatchService;
import com.spup.db.dao.appointment.AppBatchDao;
import com.spup.db.entity.appointment.AppBatch;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ForActivityBatchServiceImpl implements ForActivityBatchService {

    @Resource
    private AppBatchDao appBatchDao;

    @Override
    public AppBatch getByNo(String batchNo, Byte batchCategory){
        Optional<AppBatch> byBatchNoAndBatchCategory = appBatchDao.getByBatchNoAndBatchCategory(batchNo, batchCategory);
        if(!byBatchNoAndBatchCategory.isPresent()){
            return  null;
        }
        return byBatchNoAndBatchCategory.get();
    }

    @Override
    public Map<String,List<AppBatch>>  getListByDate(Byte category,String startDate,String endDate) {
        List<AppBatch> batches = appBatchDao.findByBatchCategoryAndBatchDateBetween(category,startDate,endDate);
        batches.sort(Comparator.comparing(AppBatch::getBatchStartTime));
        Map<String,List<AppBatch>> batchsMap = new LinkedHashMap<>();
        for (AppBatch batch : batches) {
            String date = batch.getBatchDate();
            List<AppBatch> batchList = batchsMap.get(date);
            if(batchList == null){
                batchList = new ArrayList<>();
                batchsMap.put(date,batchList);
            }
            batchList.add(batch);
        }
        return batchsMap ;
    }

    @Override
    public List<AppBatch> getListByDate(Byte category, String date) {
        //appBatchExample.setOrderByClause( " batch_date asc, batch_start_time asc " );
        List<AppBatch> batches = appBatchDao.findByBatchCategoryAndBatchDateBetween(category,date,date);

        return batches;
    }

    @Override
    public List<AppBatch> getListByDate(String exhibitionNo , Byte category, String date) {
        List<AppBatch> listByDate = getListByDate(category, date);
        listByDate = listByDate.stream().filter(appBatch ->
                appBatch.getBatchRemark().equals(exhibitionNo)).collect(Collectors.toList());
        return listByDate;
    }

    @Override
    public AppBatch save(AppBatch batch) {
        return appBatchDao.save(batch);
    }

    @Override
    public AppBatch update(AppBatch batch) {
        return appBatchDao.save(batch);
    }

    @Override
    public AppBatch updateRemaining(String batchNo, Byte batchCategory , int updateNum) {
        AppBatch batch2 = getByNo(batchNo, batchCategory);
        if(batch2 == null){
            return null;
        }
        batch2.setTicketRemaining(batch2.getTicketRemaining()+updateNum>batch2.getTicketTotal()?batch2.getTicketTotal():(batch2.getTicketRemaining()+updateNum));
        return update(batch2);
    }
}
