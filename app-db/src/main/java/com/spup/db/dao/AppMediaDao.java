package com.spup.db.dao;

import com.spup.db.entity.AppMedia;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * @Description  app_media Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppMediaDao  extends JpaRepository<AppMedia, Long> , JpaSpecificationExecutor<AppMedia> {
    Optional<AppMedia> findByMd5Sum(String md5);
}
