package com.spup.db.dao;

import com.spup.db.entity.AppVisitGuide;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * @Description  app_visit_guide Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppVisitGuideDao  extends JpaRepository<AppVisitGuide, Long> , JpaSpecificationExecutor<AppVisitGuide> {


}
