package com.spup.db.dao;

import com.spup.db.entity.ArtCenterInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;


@Repository
public interface ArtCenterInfoDao extends JpaRepository<ArtCenterInfo, Long> , JpaSpecificationExecutor<ArtCenterInfo> {
    Optional<ArtCenterInfo> findBySection(ArtCenterInfo.SectionEnum section);

}
