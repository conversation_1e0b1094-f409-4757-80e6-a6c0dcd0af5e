package com.spup.db.dao;

import com.spup.db.entity.appointment.BlackList;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BlackListDao extends JpaRepository<BlackList, Long>, JpaSpecificationExecutor<BlackList> {
    List<BlackList> findByStatus(BlackList.StatusEnum statusEnum);
    List<BlackList> findByUnionid(String unionid);
    Optional<BlackList> getByUnionidAndStatusAndCategory(String unionid, BlackList.StatusEnum statusEnum , BlackList.CategoryEnum categoryEnum);

}
