package com.spup.db.dao;

import com.spup.db.entity.CommQuestionnaireAnswer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description  comm_questionnaire_answer Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface CommQuestionnaireAnswerDao  extends JpaRepository<CommQuestionnaireAnswer, Long> , JpaSpecificationExecutor<CommQuestionnaireAnswer> {
    List<CommQuestionnaireAnswer> findByUnionidAndQuestionnaireId(String unionid,Long questionnaireId);
    List<CommQuestionnaireAnswer> findByQuestionnaireId(Long questionnaireI);
}
