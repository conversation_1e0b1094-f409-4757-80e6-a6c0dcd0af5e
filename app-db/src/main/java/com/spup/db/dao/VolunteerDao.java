package com.spup.db.dao;

import com.spup.db.entity.Volunteer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VolunteerDao extends JpaRepository<Volunteer, Long>, JpaSpecificationExecutor<Volunteer> {
    List<Volunteer> findByCategory(Volunteer.VolunteerCategoryEnum category);
    Optional<Volunteer> getByVolunteerId(String volunteerId);
}
