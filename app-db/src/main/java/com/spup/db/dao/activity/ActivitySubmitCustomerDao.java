package com.spup.db.dao.activity;


import com.spup.db.entity.activity.ActivitySubmitCustomer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActivitySubmitCustomerDao  extends JpaRepository<ActivitySubmitCustomer, Long> , JpaSpecificationExecutor<ActivitySubmitCustomer> {
    List<ActivitySubmitCustomer>
        findActivitySubmitCustomerByActRoundIdAndUnionid(String actRoundId, String unionid);
    List<ActivitySubmitCustomer>
        findActivitySubmitCustomerByActRoundIdAndUnionidAndType(String actRoundId, String unionid, ActivitySubmitCustomer.SubmitCustomerTypeEnum type);

    List<ActivitySubmitCustomer> findByUnionid(String unionid);
    List<ActivitySubmitCustomer> findByActRoundId(String actRoundId);
    List<ActivitySubmitCustomer> findByActRoundIdAndSubmitIdAndUnionid(String actRoundId, String submitId, String unionid);
}
