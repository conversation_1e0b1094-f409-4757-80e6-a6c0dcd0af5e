package com.spup.db.dao.activity;

import com.spup.db.entity.activity.AppActivity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description  app_activity Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppActivityDao  extends JpaRepository<AppActivity, Long> , JpaSpecificationExecutor<AppActivity> {
    List<AppActivity> findByIdIn(List<Long> ids);
    List<AppActivity> findByTitleLikeOrderByIdDesc(String title);
}
