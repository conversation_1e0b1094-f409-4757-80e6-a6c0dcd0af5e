package com.spup.db.dao.activity;

import com.spup.db.entity.activity.AppActivityEntryRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description  app_activity_entry_rule Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppActivityEntryRuleDao  extends JpaRepository<AppActivityEntryRule, Long> , JpaSpecificationExecutor<AppActivityEntryRule> {
    List<AppActivityEntryRule> findByActivityId(Long activityId);

}
