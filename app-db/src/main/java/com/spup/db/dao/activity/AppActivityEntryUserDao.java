package com.spup.db.dao.activity;

import com.spup.db.entity.activity.AppActivityEntryUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description  app_activity_entry_user Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppActivityEntryUserDao  extends JpaRepository<AppActivityEntryUser, Long> , JpaSpecificationExecutor<AppActivityEntryUser> {
    List<AppActivityEntryUser> findByActivityId(Long activityId);
    List<AppActivityEntryUser> findByActivityIdAndUserUnionid(Long activityId,String userUnionid);
    List<AppActivityEntryUser> findByUserUnionid(String userUnionid);

}
