package com.spup.db.dao.appointment;

import com.spup.db.entity.appointment.AppAppointmentOffline;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * @Description  app_appointment_offline Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppAppointmentOfflineDao  extends JpaRepository<AppAppointmentOffline, Long> , JpaSpecificationExecutor<AppAppointmentOffline> {


}
