package com.spup.db.dao.appointment;

import com.spup.db.entity.appointment.AppAppointmentOrderTemporaryExhibition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @Description  app_appointment_order_temporary_exhibition Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppAppointmentOrderTemporaryExhibitionDao  extends JpaRepository<AppAppointmentOrderTemporaryExhibition, Long> , JpaSpecificationExecutor<AppAppointmentOrderTemporaryExhibition> {
    List<AppAppointmentOrderTemporaryExhibition> findByOwnerUnionid(String unionid);
    Optional<AppAppointmentOrderTemporaryExhibition> getByOrderNo(String orderNo);
}
