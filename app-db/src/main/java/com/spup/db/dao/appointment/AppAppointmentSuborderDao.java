package com.spup.db.dao.appointment;

import com.spup.db.entity.appointment.AppAppointmentSuborder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AppAppointmentSuborderDao  extends JpaRepository<AppAppointmentSuborder, Long> , JpaSpecificationExecutor<AppAppointmentSuborder> {
    List<AppAppointmentSuborder> findByBatchDate(String batchDate);
    List<AppAppointmentSuborder> findByOnwerUnionid(String unionid);
    List<AppAppointmentSuborder> findByOrderNo(String orderNo);
    Optional<AppAppointmentSuborder> findBySuborderNo(String suborderNo);
    List<AppAppointmentSuborder> findByBatchDateBetween(String startDate,String endDate);

    int countBySuborderStatus(Short suborderStatus);
    int countBySuborderStatusAndBatchDateBetween(Short suborderStatus, String start,String end);
}
