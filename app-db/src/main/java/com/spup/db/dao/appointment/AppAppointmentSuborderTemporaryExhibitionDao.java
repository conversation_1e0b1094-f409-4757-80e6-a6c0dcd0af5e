package com.spup.db.dao.appointment;

import com.spup.db.entity.appointment.AppAppointmentSuborderTemporaryExhibition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @Description  app_appointment_suborder_temporary_exhibition Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppAppointmentSuborderTemporaryExhibitionDao  extends JpaRepository<AppAppointmentSuborderTemporaryExhibition, Long> , JpaSpecificationExecutor<AppAppointmentSuborderTemporaryExhibition> {

    Optional<AppAppointmentSuborderTemporaryExhibition> getBySuborderNo(String suborderNo);
    List<AppAppointmentSuborderTemporaryExhibition> findByOnwerUnionid(String unionid);
    List<AppAppointmentSuborderTemporaryExhibition> findByOrderNo(String orderNo);
}
