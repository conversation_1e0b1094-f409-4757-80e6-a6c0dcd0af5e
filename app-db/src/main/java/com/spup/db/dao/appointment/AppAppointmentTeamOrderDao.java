package com.spup.db.dao.appointment;

import com.spup.db.entity.appointment.AppAppointmentTeamOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @Description  app_appointment_team_order Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppAppointmentTeamOrderDao  extends JpaRepository<AppAppointmentTeamOrder, Long> , JpaSpecificationExecutor<AppAppointmentTeamOrder> {
    List<AppAppointmentTeamOrder> findByOwnerUnionid(String ownerUnionid);
    Optional<AppAppointmentTeamOrder> findByOrderNo(String orderNo);
    List<AppAppointmentTeamOrder> findByBatchDateBetween(String start, String end);
}
