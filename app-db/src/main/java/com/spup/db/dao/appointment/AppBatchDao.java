package com.spup.db.dao.appointment;

import com.spup.db.entity.appointment.AppBatch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * @Description  app_batch Dao 
 * <AUTHOR>  
 * @Date 2023-12-14 
 */

@Repository
public interface AppBatchDao  extends JpaRepository<AppBatch, Long> , JpaSpecificationExecutor<AppBatch> {
    Optional<AppBatch> getByBatchNoAndBatchCategory(String batchNo, byte category);
    List<AppBatch> findByBatchCategoryAndBatchDateBetween(byte category, String start, String end);

    List<AppBatch> findByBatchDateLessThan(String start);
    List<AppBatch> findByBatchDateAndBatchEndTimeLessThanEqual(String start,String hhmm);


    @Transactional
    @Modifying
    @Query("update AppBatch set ticketRemaining = ticketRemaining+ ?1 where id = ?2 and ticketRemaining+ ?3 <= ticketTotal and ticketRemaining+ ?4 >=0 ")
    int updateSubmitNumber(int addSubmitNumber, Long id, int addSubmitNumberCp,int addSubmitNumberCp2);
}