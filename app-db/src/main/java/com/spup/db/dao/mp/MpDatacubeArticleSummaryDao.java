package com.spup.db.dao.mp;

import com.spup.db.entity.mp.MpDatacubeArticleSummary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface MpDatacubeArticleSummaryDao extends JpaRepository<MpDatacubeArticleSummary, Long>
                                    , JpaSpecificationExecutor<MpDatacubeArticleSummary> {
    Optional<MpDatacubeArticleSummary> getByMsgId(String msgId);
    Long countBySendDateBetween(LocalDate start,LocalDate end);
    List<MpDatacubeArticleSummary> findBySendDateBetween(LocalDate start,LocalDate end);
}
