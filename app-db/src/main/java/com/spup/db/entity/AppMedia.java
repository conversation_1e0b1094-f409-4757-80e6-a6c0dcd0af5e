package com.spup.db.entity;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.Proxy;
import org.hibernate.annotations.Comment;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import org.springframework.util.ClassUtils;
import java.time.LocalDateTime;

@SQLDelete(sql = "UPDATE app_media SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_media")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppMedia implements Serializable {

	private static final long serialVersionUID = 523017645072980705L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "media_name")
	@Comment("媒体文件名（不带后缀名）")
	private String mediaName;

	@Column(name = "media_type")
	@Comment("媒体文件类型")
	private String mediaType;

	@Column(name = "media_size")
	@Comment("文件大小")
	private Long mediaSize;

	@Column(name = "media_src")
	@Comment("文件存储路径")
	private String mediaSrc;

	@Column(name = "media_status")
	@Comment("已删除，正常，已隐藏")
	private Byte mediaStatus;

	@Column(name = "md5_sum")
	@Comment("文件校验，避免重复存储。")
	private String md5Sum;

	@Column(name = "media_showname")
	@Comment("显示名称，相同文件可显示名称不同")
	private String mediaShowname;

	@Column(name = "create_by")
	@Comment("创建者")
	private String createBy;

	@Column(name = "create_time")
	@Comment("记录创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

	@Column(name = "update_by")
	@Comment("修改人")
	private String updateBy;

	@Column(name = "update_time")
	@Comment("记录修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

	@Column(name = "deleted")
	private Byte deleted;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
