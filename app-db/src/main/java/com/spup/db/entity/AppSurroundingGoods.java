package com.spup.db.entity;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.Proxy;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import org.springframework.util.ClassUtils;
import java.time.LocalDateTime;

@SQLDelete(sql = "UPDATE app_surrounding_goods SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_surrounding_goods")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppSurroundingGoods implements Serializable {

	private static final long serialVersionUID = 5994609567169516039L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "goods_no")
	@Comment("商品编号")
	private String goodsNo;

	@Column(name = "goods_name")
	@Comment("商品名称")
	private String goodsName;

	@Column(name = "goods_conver_picture")
	private String goodsConverPicture;

	@Column(name = "goods_price")
	@Comment("商品价格")
	private Double goodsPrice;

	@Column(name = "goods_category")
	@Comment("商品分类")
	private String goodsCategory;

	@Column(name = "goods_introduce")
	@Comment("商品介绍")
	private String goodsIntroduce;

	@Column(name = "goods_status")
	@Comment("商品状态：1上架；2下架；4其他")
	private Byte goodsStatus;

	@Column(name = "create_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

	@Column(name = "create_by")
	private String createBy;

	@Column(name = "update_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

	@Column(name = "update_by")
	private String updateBy;

	@Column(name = "deleted")
	private Byte deleted;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
