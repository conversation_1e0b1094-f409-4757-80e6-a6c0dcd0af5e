package com.spup.db.entity;

import org.hibernate.annotations.*;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDate;
import java.time.LocalTime;


@Entity
@Table( name ="round_config" )
@SQLDelete(sql = "UPDATE round_config SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class RoundConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String exhibitionId;
    @Enumerated(EnumType.STRING)
    private RoundStatusEnum roundStatus;
    private LocalDate roundDate;

    private String roundId;
    private LocalTime startTime;
    private LocalTime endTime;
    private int deleted = 0;

    public enum RoundStatusEnum {
        open,
        close;
    }
}
