package com.spup.db.entity.appointment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import java.io.Serializable;
import org.hibernate.annotations.Proxy;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.springframework.util.ClassUtils;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

@SQLDelete(sql = "UPDATE app_appointment_analysis SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table ( name ="app_appointment_analysis" )
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppAppointmentAnalysis  implements Serializable {

	private static final long serialVersionUID =  7805754593407967872L;

   	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

   	@Column(name = "analysis_date" )
	private String analysisDate;

   	@Column(name = "ticket_reserve_total")
	@Comment("门票预约总数")
	private Integer ticketReserveTotal;

   	@Column(name = "ticket_reserve_refund_active")
	@Comment("主动退票数")
	private Integer ticketReserveRefundActive;

   	@Column(name = "ticket_reserve_refund_passive")
	@Comment("被动退票数")
	private Integer ticketReserveRefundPassive;

   	@Column(name = "ticket_reserve_am")
	@Comment("上午预约数")
	private Integer ticketReserveAm;

   	@Column(name = "ticket_reserve_pm")
	@Comment("下午预约数")
	private Integer ticketReservePm;

   	@Column(name = "ticket_checkin_total")
	@Comment("门票核销总数")
	private Integer ticketCheckinTotal;

   	@Column(name = "ticket_checkin_am")
	@Comment("上午核销数")
	private Integer ticketCheckinAm;

   	@Column(name = "ticket_checkin_pm" )
	@Comment("下午核销数")
	private Integer ticketCheckinPm;

   	@Column(name = "item_reserve_total")
	@Comment("飞阅浦东展现预约总数")
	private Integer itemReserveTotal;

   	@Column(name = "item_reserve_am")
	@Comment("飞阅浦东上午预约数")
	private Integer itemReserveAm;

   	@Column(name = "item_reserve_pm")
	@Comment("飞阅浦东下午预约数")
	private Integer itemReservePm;

   	@Column(name = "item_reserve_refund_active")
	@Comment("飞阅浦东主动退票数")
	private Integer itemReserveRefundActive;

   	@Column(name = "item_reserve_refund_passive")
	@Comment("飞阅浦东被动退票数")
	private Integer itemReserveRefundPassive;

   	@Column(name = "item_checkin_total")
	@Comment("飞阅浦东核销数")
	private Integer itemCheckinTotal;

   	@Column(name = "item_checkin_am")
	@Comment("飞阅浦东上午核销数")
	private Integer itemCheckinAm;

   	@Column(name = "item_checkin_pm")
	@Comment("飞阅浦东下午核销数")
	private Integer itemCheckinPm;

   	@Column(name = "create_time")
	private LocalDateTime createTime;

   	@Column(name = "create_by")
	private String createBy;

   	@Column(name = "update_time")
	private LocalDateTime updateTime;

   	@Column(name = "update_by")
	private String updateBy;

   	@Column(name = "deleted")
	private Byte deleted;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
