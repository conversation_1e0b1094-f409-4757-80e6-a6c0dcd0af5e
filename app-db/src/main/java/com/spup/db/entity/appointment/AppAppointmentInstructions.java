package com.spup.db.entity.appointment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import java.io.Serializable;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

// @Proxy(lazy = false)
@Entity
@Table(name = "app_appointment_instructions")
// @EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppAppointmentInstructions implements Serializable {

	private static final long serialVersionUID = 576769161749723413L;

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "audience_notice")
	@Comment("观众须知")
	private String audienceNotice;

	@Column(name = "visiting_instructions")
	@Comment("参观须知")
	private String visitingInstructions;

	@Column(name = "admission_notice")
	@Comment("入馆须知")
	private String admissionNotice;

	@Column(name = "create_time")
	@Comment("生成时间")
	private LocalDateTime createTime;

	@Column(name = "create_by")
	@Comment("创建者unionid")
	private String createBy;

	@Column(name = "update_time")
	@Comment("数据更新时间")
	private LocalDateTime updateTime;

	@Column(name = "update_by")
	@Comment("更新者unionid")
	private String updateBy;

	/*
	 * @PrePersist
	 * public void prePersist() {
	 * setCreateTime(LocalDateTime.now());
	 * setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() +
	 * "." + Thread.currentThread().getStackTrace()[1].getMethodName());
	 * setUpdateTime(LocalDateTime.now());
	 * setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() +
	 * "." + Thread.currentThread().getStackTrace()[1].getMethodName());
	 * }
	 * 
	 * @PreUpdate
	 * public void preUpdate() {
	 * setUpdateTime(LocalDateTime.now());
	 * setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() +
	 * "." + Thread.currentThread().getStackTrace()[1].getMethodName());
	 * }
	 */
}
