package com.spup.db.entity.appointment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import java.io.Serializable;
import org.hibernate.annotations.Proxy;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import org.springframework.util.ClassUtils;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

@SQLDelete(sql = "UPDATE app_appointment_offline SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_appointment_offline")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppAppointmentOffline implements Serializable {

	private static final long serialVersionUID = 4491606273386178155L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "appoint_date")
	private String appointDate;

	@Column(name = "appoint_batch_no")
	private String appointBatchNo;

	@Column(name = "appoint_batch_start_time")
	private String appointBatchStartTime;

	@Column(name = "appoint_batch_end_time")
	private String appointBatchEndTime;

	@Column(name = "persons_num")
	private Integer personsNum;

	@Column(name = "visit_info")
	private String visitInfo;

	@Column(name = "create_time")
	private LocalDateTime createTime;

	@Column(name = "create_by")
	private String createBy;

	@Column(name = "update_time")
	private LocalDateTime updateTime;

	@Column(name = "update_by")
	private String updateBy;

	@Column(name = "deleted")
	private Byte deleted;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
