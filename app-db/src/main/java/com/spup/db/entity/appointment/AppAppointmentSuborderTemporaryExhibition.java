package com.spup.db.entity.appointment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import java.io.Serializable;
import org.hibernate.annotations.Proxy;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import org.springframework.util.ClassUtils;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

@SQLDelete(sql = "UPDATE app_appointment_suborder_temporary_exhibition SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_appointment_suborder_temporary_exhibition")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppAppointmentSuborderTemporaryExhibition implements Serializable {

	private static final long serialVersionUID = 5167894000072729999L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "order_no")
	private String orderNo;

	@Column(name = "suborder_no")
	private String suborderNo;

	@Column(name = "batch_no")
	private String batchNo;

	@Column(name = "batch_date")
	private String batchDate;

	@Column(name = "batch_start_time")
	private String batchStartTime;

	@Column(name = "batch_end_time")
	private String batchEndTime;

	@Column(name = "onwer_unionid")
	private String onwerUnionid;

	@Column(name = "contacts_name")
	private String contactsName;

	@Column(name = "contacts_phone")
	private String contactsPhone;

	@Column(name = "contacts_idcard_category")
	private Byte contactsIdcardCategory;

	@Column(name = "contacts_idcard_no")
	private String contactsIdcardNo;

	/**
	 * 子订单状态-无符号int，状态可以为负数，表明异常状态；1：待支付，2: 待服务，4: 服务中，8: 已完成。 -1：售后中； -2: 已退款； -4:
	 * 已取消；
	 */
	@Column(name = "suborder_status")
	private Short suborderStatus;

	/**
	 * 创建者unionid-可为用户、店员、店长、系统等
	 */
	@Column(name = "create_by")
	private String createBy;

	@Column(name = "create_time")
	private LocalDateTime createTime;

	/**
	 * 创建者unionid-可为用户、店员、店长、系统等
	 */
	@Column(name = "update_by")
	private String updateBy;

	@Column(name = "update_time")
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除，默认为0未删除，1已删除
	 */
	@Column(name = "deleted")
	private Byte deleted;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
