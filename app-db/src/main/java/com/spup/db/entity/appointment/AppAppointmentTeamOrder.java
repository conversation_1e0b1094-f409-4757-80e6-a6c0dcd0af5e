package com.spup.db.entity.appointment;

import com.fasterxml.jackson.databind.node.ObjectNode;
import io.hypersistence.utils.hibernate.type.json.JsonStringType;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Proxy;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.Where;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.springframework.util.ClassUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Enumerated;
import javax.persistence.EnumType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

@SQLDelete(sql = "UPDATE app_appointment_team_order SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_appointment_team_order")
@EntityListeners(JaxbEntityListener.class)
@TypeDef(name = "jsonString", typeClass = JsonStringType.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppAppointmentTeamOrder implements Serializable {

	private static final long serialVersionUID = 857987724297346622L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "exhibition_no")
	private String exhibitionNo;

	@Column(name = "order_no")
	@Comment("订单编号")
	private String orderNo;

	@Column(name = "batch_no")
	@Comment("场次编号")
	private String batchNo;

	@Column(name = "batch_date")
	@Comment("场次日期")
	private String batchDate;

	@Column(name = "batch_start_time")
	@Comment("场次开始时间")
	private String batchStartTime;

	@Column(name = "batch_end_time")
	@Comment("场次结束时间")
	private String batchEndTime;

	@Column(name = "owner_unionid")
	@Comment("用户小程序唯一id")
	private String ownerUnionid;

	@Column(name = "ower_unit")
	private String owerUnit;

	@Column(name = "ower_unit_code")
	private String owerUnitCode;

	@Column(name = "owner_name")
	@Comment("订单所有者姓名")
	private String ownerName;

	@Column(name = "owner_phone")
	@Comment("订单所有者手机号")
	private String ownerPhone;

	@Column(name = "visitors_num")
	private Integer visitorsNum;

	@Column(name = "order_status")
	@Comment("订单状态")
	private Short orderStatus;

	@Column(name = "order_category")
	@Comment("订单分类，可能会有团体订单等其他的")
	private Byte orderCategory;

	@Column(name = "order_remark")
	private String orderRemark;

	@Column(name = "create_time")
	@Comment("订单生成时间")
	private LocalDateTime createTime;

	@Column(name = "create_by")
	@Comment("创建者unionid-可为用户、店员、店长、系统等")
	private String createBy;

	@Column(name = "update_time")
	@Comment("数据更新时间")
	private LocalDateTime updateTime;

	@Column(name = "update_by")
	@Comment("更新者unionid")
	private String updateBy;

	@Column(name = "deleted")
	@Comment("逻辑删除，默认为0未删除，1已删除")
	private Byte deleted;

	@Type(type = "jsonString")
	@Column(name = "supply_info")
	private ObjectNode supplyInfo;

	@Column(name = "method")
	@Enumerated(EnumType.STRING)
	private MethodEnum method = MethodEnum.ONLINE;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	public enum MethodEnum {
		ONLINE,
		OFFLINE;
	}
}
