package com.spup.db.entity.appointment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import java.io.Serializable;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Proxy;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.springframework.util.ClassUtils;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@SQLDelete(sql = "UPDATE app_batch SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_batch")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppBatch implements Serializable {

	private static final long serialVersionUID = 2190184228482487399L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@Column(name = "batch_no")
	@Comment("场次编号")
	private String batchNo;
	@Column(name = "batch_date")
	@Comment("场次日期")
	private String batchDate;
	@Column(name = "batch_start_time")
	@Comment("场次开始时间")
	private String batchStartTime;
	@Column(name = "batch_end_time")
	@Comment("场次结束时间")
	private String batchEndTime;
	@Column(name = "ticket_total")
	@Comment("总计发放票数")
	private Integer ticketTotal;
	@Column(name = "ticket_remaining")
	@Comment("剩余票数")
	private Integer ticketRemaining;
	@Column(name = "batch_status")
	@Comment("场次状态")
	private Byte batchStatus;
	@Column(name = "batch_remark")
	@Comment("场次备注说明，便于后期管理")
	private String batchRemark;
	@Column(name = "batch_category")
	@Comment("场次分类")
	private Byte batchCategory;
	@Column(name = "create_by")
	@Comment("系统创建-给相关类名")
	private String createBy;
	@Column(name = "create_time")
	@Comment("记录创建时间")
	private LocalDateTime createTime;
	@Column(name = "update_by")
	@Comment("创建者unionid-可为用户、管理员、系统等")
	private String updateBy;
	@Column(name = "update_time")
	@Comment("记录修改时间")
	private LocalDateTime updateTime;
	@Column(name = "deleted", nullable = false)
	@Comment("逻辑删除，默认为0未删除，1已删除")
	private Byte deleted = (byte)0;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
