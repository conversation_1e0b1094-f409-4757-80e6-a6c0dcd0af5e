package com.spup.db.entity.appointment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import java.io.Serializable;
import org.hibernate.annotations.Proxy;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import org.springframework.util.ClassUtils;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

@SQLDelete(sql = "UPDATE app_batch_set SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_batch_set")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppBatchSet implements Serializable {

	private static final long serialVersionUID = 6327447934729808895L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "batch_effect_start_time")
	@Comment("场次生效开始日期")
	private LocalDateTime batchEffectStartTime;

	@Column(name = "batch_effect_end_time")
	@Comment("场次生效结束日期（空表示永久）")
	private LocalDateTime batchEffectEndTime;

	@Column(name = "batch_category")
	@Comment("场次类型")
	private Byte batchCategory;

	@Column(name = "status")
	@Comment("规则当前状态")
	private Byte status;

	@Column(name = "create_time")
	private LocalDateTime createTime;

	@Column(name = "create_by")
	private String createBy;

	@Column(name = "update_time")
	private LocalDateTime updateTime;

	@Column(name = "update_by")
	private String updateBy;

	@Column(name = "deleted")
	private Byte deleted;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
