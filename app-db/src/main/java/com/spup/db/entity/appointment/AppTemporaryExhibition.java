package com.spup.db.entity.appointment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.hibernate.annotations.Proxy;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import org.springframework.util.ClassUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

@SQLDelete(sql = "UPDATE app_temporary_exhibition SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_temporary_exhibition")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppTemporaryExhibition implements Serializable {

	private static final long serialVersionUID = 2849138952812320755L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "exhibition_no")
	private String exhibitionNo;

	@Column(name = "exhibition_title")
	private String exhibitionTitle;

	@Column(name = "exhibition_content")
	private String exhibitionContent;

	@Column(name = "thumbnail_pic")
	private String thumbnailPic;

	@Column(name = "detail_banner_pic")
	private String detailBannerPic;

	@Column(name = "exhibition_start_date")
	private LocalDate exhibitionStartDate;

	@Column(name = "exhibition_end_date")
	private LocalDate exhibitionEndDate;

	@Column(name = "exhibition_address")
	private String exhibitionAddress;

	@Column(name = "status")
	private Byte status;

	@Column(name = "create_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

	@Column(name = "create_by")
	private String createBy;

	@Column(name = "update_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

	@Column(name = "update_by")
	private String updateBy;

	@Column(name = "deleted")
	private Byte deleted;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
