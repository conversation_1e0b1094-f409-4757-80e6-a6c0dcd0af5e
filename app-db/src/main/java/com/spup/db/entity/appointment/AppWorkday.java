package com.spup.db.entity.appointment;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.Table;
import java.io.Serializable;

import org.hibernate.annotations.Proxy;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;

import lombok.Getter;
import lombok.Setter;
@Proxy(lazy = false)
@Entity
@Table(name = "app_workday")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppWorkday implements Serializable {

	private static final long serialVersionUID = 2152712482691686505L;

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "day")
	private String day;

	@Column(name = "is_workday")
	private Integer isWorkday;

   	@Column(name = "day_remark" )
	private String dayRemark;

	@Column(name = "config", length = 500)
	@Type(type = "jsonString")
	private String config;
	@PrePersist
	public void prePersist() {
	}
}
