package com.spup.db.entity.authority;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import org.hibernate.annotations.Proxy;
import org.hibernate.annotations.Comment;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import org.springframework.util.ClassUtils;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@SQLDelete(sql = "UPDATE app_manage_role SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_manage_role")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppManageRole implements Serializable {

	private static final long serialVersionUID = 6068518491387656519L;

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "role_code")
	private Integer roleCode;

	@Column(name = "role_name")
	private String roleName;

	@Column(name = "menu_code")
	private String menuCode;

	@Column(name = "create_by")
	@Comment("创建者open_id")
	private String createBy;

	@Column(name = "create_time")
	private LocalDateTime createTime;

	@Column(name = "update_by")
	@Comment("修改人open_id")
	private String updateBy;

	@Column(name = "update_time")
	private LocalDateTime updateTime;

	@Column(name = "deleted")
	@Comment("逻辑删除，默认为0未删除，1已删除")
	private Byte deleted;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
