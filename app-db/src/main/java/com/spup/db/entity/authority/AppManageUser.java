package com.spup.db.entity.authority;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import org.hibernate.annotations.Proxy;
import org.hibernate.annotations.Comment;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import org.springframework.util.ClassUtils;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@SQLDelete(sql = "UPDATE app_manage_user SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@Proxy(lazy = false)
@Entity
@Table(name = "app_manage_user")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppManageUser implements Serializable {

	private static final long serialVersionUID = 4885459733465871734L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "open_id")
	@Comment("登录用户open_id")
	private String openId;

	@Column(name = "unionid")
	@Comment("登录用户统一识别码")
	private String unionid;
	@Column(name = "name")
	@Comment("用户昵称")
	private String name;
	@Column(name = "role_code")
	@Comment("角色，可配置多个角色，采用位运算")
	private Long roleCode;
	@Column(name = "mobile")
	@Comment("手机号")
	private String mobile;
	@Column(name = "menu_code")
	@Comment("菜单，以逗号分隔")
	private String menuCode;
	@Column(name = "remark")
	@Comment("描述")
	private String remark;
	@Column(name = "status")
	@Comment("状态：1启用；2禁用；4其他")
	private Byte status;
	@Column(name = "create_by")
	@Comment("系统创建-给相关类名")
	private String createBy;
	@Column(name = "create_time")
	@Comment("记录创建时间")
	private LocalDateTime createTime;
	@Column(name = "update_by")
	@Comment("创建者unionid-可为用户、管理员、系统等")
	private String updateBy;
	@Column(name = "update_time")
	@Comment("记录修改时间")
	private LocalDateTime updateTime;
	private Byte deleted;

	@PrePersist
	public void prePersist() {
		setCreateTime(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateTime(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
