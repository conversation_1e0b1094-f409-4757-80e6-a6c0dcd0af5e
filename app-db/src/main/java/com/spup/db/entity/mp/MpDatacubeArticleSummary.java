package com.spup.db.entity.mp;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.springframework.util.ClassUtils;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "mp_datacube_everday_article_summary")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class MpDatacubeArticleSummary implements Serializable {

	private static final long serialVersionUID = 4840785523714526881L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "send_date")
	private LocalDate sendDate;
	@Column(name = "analysis_date")
	private LocalDate analysisDate;
	@Column(name = "msg_id")
	private String msgId;
	@Column(name = "title")
	private String title;
	@Column(name = "total_read_user")
	private int totalReadUser = 0;
	@Column(name = "total_read_count")
	private int totalReadCount = 0;
	@Column(name = "total_share_user")
	private int totalShareUser = 0;
	@Column(name = "total_share_count")
	private int totalShareCount = 0;
	@Column(name = "add_to_fav_user")
	private Integer addToFavUser = 0;
	@Column(name = "add_to_fav_count")
	private Integer addToFavCount = 0;

	@Column(name = "create_on")
	private LocalDateTime createOn;

	@Column(name = "create_by")
	private String createBy;

	@Column(name = "update_on")
	private LocalDateTime updateOn;

	@Column(name = "update_by")
	private String updateBy;

	@PrePersist
	public void prePersist() {
		setCreateOn(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateOn(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateOn(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
