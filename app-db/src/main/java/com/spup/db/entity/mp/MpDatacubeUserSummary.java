package com.spup.db.entity.mp;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.springframework.util.ClassUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "mp_datacube_everday_user_summary")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class MpDatacubeUserSummary implements Serializable {

	private static final long serialVersionUID = 4840785523714526881L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "analysis_date")
	private LocalDate analysisDate;

	@Column(name = "user_cumulate")
	private Integer userCumulate = 0;

	@Column(name = "new_user")
	private Integer newUser = 0;

	@Column(name = "cancel_user")
	private Integer cancelUser = 0;

	@Column(name = "article_summary_json")
	@Lob
	private String articleSummaryJson;

	@Column(name = "create_on")
	@JsonIgnore
	private LocalDateTime createOn;

	@Column(name = "create_by")
	@JsonIgnore
	private String createBy;

	@Column(name = "update_on")
	@JsonIgnore
	private LocalDateTime updateOn;

	@Column(name = "update_by")
	@JsonIgnore
	private String updateBy;

	private Long visits;
	private Long totalVisits;

	@PrePersist
	public void prePersist() {
		setCreateOn(LocalDateTime.now());
		setCreateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
		setUpdateOn(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}

	@PreUpdate
	public void preUpdate() {
		setUpdateOn(LocalDateTime.now());
		setUpdateBy(ClassUtils.getUserClass(this).getSuperclass().getSimpleName() + "."
				+ Thread.currentThread().getStackTrace()[1].getMethodName());
	}
}
