package com.spup.db.entity.sys;


import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Proxy;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;

import lombok.Getter;
import lombok.Setter;


@Proxy(lazy = false)
@Entity
@Table ( name ="app_config" )
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppConfig implements Serializable {

	private static final long serialVersionUID = 1844318275326105182L;

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "group_no")
	private String groupNo;

	@Column(name = "rule_name")
	private String ruleName;

	@Column(name = "rule_value")
	private String ruleValue;
}
