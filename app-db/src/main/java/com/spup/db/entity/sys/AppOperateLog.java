package com.spup.db.entity.sys;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.Proxy;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.hibernate.annotations.*;
import java.time.LocalDateTime;


@Proxy(lazy = false)
@Entity
@Table ( name ="app_operate_log" )
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class AppOperateLog implements Serializable {

	private static final long serialVersionUID =  7761788481384681895L;

   	@Id 
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

   	@Column(name = "operator" )
	private String operator;

   	@Column(name = "operator_browser" )
	private String operatorBrowser;

   	@Column(name = "operator_ip" )
	private String operatorIp;

   	@Column(name = "operate_time" )
	@JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime operateTime;

   	@Column(name = "operate_url" )
	private String operateUrl;

   	@Column(name = "operate_params" )
	private String operateParams;
}
