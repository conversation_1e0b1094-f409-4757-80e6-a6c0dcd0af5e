package com.spup.enums;

public enum BatchSetStatusEnum {
    CLOSED((byte)0, "已失效"),
    RUNNING((byte)1, "生效中"),
    WAIT_RUNNING((byte)2, "待生效"),
    ; //此写法防止扩充时忘记分号

    private byte code;
    private String name;

    private BatchSetStatusEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static void main(String[] args) {
        byte a = 1;
        System.out.println(a&-1);
    }
}
