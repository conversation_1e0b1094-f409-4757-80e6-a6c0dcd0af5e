package com.spup.enums;

public enum WorkdayEnum {
    REST_DAY((byte)0, "馆休日", new int[]{2}),
    OPEN_DAY((byte)1, "开放日",new int[]{1,3,4,5,6,7}),
    ; //此写法防止扩充时忘记分号

    private byte code;
    private String name;
    private int[] weeks;

    private WorkdayEnum(byte code, String name,int[] weeks) {
        this.code = code;
        this.name = name;
        this.weeks = weeks;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static WorkdayEnum getEnumByWeek(int week){
        WorkdayEnum[] values = WorkdayEnum.values();
        for (int i = 0; i < values.length; i++) {
            WorkdayEnum en = values[i];
            int[] weeks = en.weeks;
            for (int j = 0; j < weeks.length; j++) {
                if (weeks[j]==week){
                    return en;
                }
            }
        }
        return null;
    }
}
