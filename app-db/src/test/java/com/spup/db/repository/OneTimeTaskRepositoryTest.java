package com.spup.db.repository;

import com.spup.db.entity.OneTimeTask;
import com.spup.db.entity.OneTimeTask.TaskStatusEnum;
import com.spup.db.entity.OneTimeTask.TaskTypeEnum;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class OneTimeTaskRepositoryTest {

    @Test
    public void testFindById() {
        // Arrange
        OneTimeTaskRepository repository = Mockito.mock(OneTimeTaskRepository.class);
        String taskId = UUID.randomUUID().toString();
        
        OneTimeTask task = new OneTimeTask();
        task.setId(taskId);
        task.setTaskType(TaskTypeEnum.CREATE_TEAM_BATCH);
        task.setStatus(TaskStatusEnum.PENDING);
        task.setExecuteTime(Instant.now().plusSeconds(3600));
        
        when(repository.findById(taskId)).thenReturn(Optional.of(task));
        
        // Act
        Optional<OneTimeTask> result = repository.findById(taskId);
        
        // Assert
        assertTrue(result.isPresent());
        assertEquals(taskId, result.get().getId());
        assertEquals(TaskTypeEnum.CREATE_TEAM_BATCH, result.get().getTaskType());
        assertEquals(TaskStatusEnum.PENDING, result.get().getStatus());
        
        // Verify
        verify(repository).findById(taskId);
    }
    
    @Test
    public void testFindByIdNotFound() {
        // Arrange
        OneTimeTaskRepository repository = Mockito.mock(OneTimeTaskRepository.class);
        String taskId = UUID.randomUUID().toString();
        
        when(repository.findById(taskId)).thenReturn(Optional.empty());
        
        // Act
        Optional<OneTimeTask> result = repository.findById(taskId);
        
        // Assert
        assertFalse(result.isPresent());
        
        // Verify
        verify(repository).findById(taskId);
    }
    
    @Test
    public void testSave() {
        // Arrange
        OneTimeTaskRepository repository = Mockito.mock(OneTimeTaskRepository.class);
        String taskId = UUID.randomUUID().toString();
        
        OneTimeTask task = new OneTimeTask();
        task.setId(taskId);
        task.setTaskType(TaskTypeEnum.CREATE_TEAM_BATCH);
        task.setStatus(TaskStatusEnum.PENDING);
        task.setExecuteTime(Instant.now().plusSeconds(3600));
        
        when(repository.save(any(OneTimeTask.class))).thenReturn(task);
        
        // Act
        OneTimeTask savedTask = repository.save(task);
        
        // Assert
        assertNotNull(savedTask);
        assertEquals(taskId, savedTask.getId());
        assertEquals(TaskTypeEnum.CREATE_TEAM_BATCH, savedTask.getTaskType());
        assertEquals(TaskStatusEnum.PENDING, savedTask.getStatus());
        
        // Verify
        verify(repository).save(task);
    }
}
