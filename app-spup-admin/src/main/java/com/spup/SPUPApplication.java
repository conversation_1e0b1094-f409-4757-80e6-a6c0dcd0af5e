package com.spup;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import com.spup.config.WeChatProperties;

@SpringBootApplication
@ComponentScan({"com.spup","com.huangdou"})
@EnableJpaRepositories({"com.spup", "com.huangdou"})
@EntityScan({"com.spup","com.huangdou"})
@EnableConfigurationProperties(WeChatProperties.class)
public class SPUPApplication extends SpringBootServletInitializer {
    public static void main(String[] args) {
        SpringApplication.run(SPUPApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(SPUPApplication.class);
    }
}
