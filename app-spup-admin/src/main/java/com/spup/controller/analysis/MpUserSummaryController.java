package com.spup.controller.analysis;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.mp.MpDatacubeArticleSummary;
import com.spup.db.entity.mp.MpDatacubeUserSummary;
import com.spup.dto.DateQueryRequest;
import com.spup.service.analysis.IMpDatacubeArticleSummaryService;
import com.spup.service.analysis.IMpDatacubeUserSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "公众号数据汇总" , position = 6)
@RestController
@RequestMapping(value = "/summary/mp")
public class MpUserSummaryController {

    @Resource
    private IMpDatacubeArticleSummaryService articleSummaryService;
    @Resource
    private IMpDatacubeUserSummaryService userSummaryService;

    @ApiOperation(value = "获取用户统计")
    @PostMapping(value = "/getUserSummary")
    public CommonResult<?> getUserSummary(@RequestBody DateQueryRequest queryParam)  {
        Page<MpDatacubeUserSummary> listByPage = userSummaryService.getListByPage(queryParam);
        return CommonResult.succeeded(listByPage);
    }
    @ApiOperation(value = "获取文章统计")
    @PostMapping(value = "/getArticleSummary")
    public CommonResult<?> getArticleSummary(@RequestBody DateQueryRequest queryParam) {
        Page<MpDatacubeArticleSummary> listByPage = articleSummaryService.getListByPage(queryParam);
        return CommonResult.succeeded(listByPage);
    }


}
