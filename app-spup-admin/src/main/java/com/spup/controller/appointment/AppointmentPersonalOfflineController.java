package com.spup.controller.appointment;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.appointment.AppAppointmentPersonalOffline;
import com.spup.dto.PersonalOfflineListRequest;
import com.spup.service.appointment.IAppAppointmentPersonalOfflineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "个人线下登记")
@RestController
@RequestMapping(value = "/personalOffline")
public class AppointmentPersonalOfflineController {
    @Resource
    private IAppAppointmentPersonalOfflineService iAppAppointmentPersonalOfflineService;

    @ApiOperation(value = "分页查询")
    @PostMapping(value="/list/byPage")
    public CommonResult<?> listByPage (@RequestBody PersonalOfflineListRequest param)  {
        return CommonResult.succeeded(iAppAppointmentPersonalOfflineService.getList(param));
    }

    @ApiOperation(value = "新建")
    @PostMapping(value="/save")
    public CommonResult<?> save (@RequestBody AppAppointmentPersonalOffline personalOffline) {
        AppAppointmentPersonalOffline save = iAppAppointmentPersonalOfflineService.save(personalOffline);
        return CommonResult.succeeded(save);
    }

    @ApiOperation(value = "删除")
    @GetMapping(value="/delete/byId/{id}")
    public CommonResult<?> delete (@PathVariable Long id) {

        return CommonResult.succeeded(iAppAppointmentPersonalOfflineService.delete(id));
    }

    @ApiOperation(value = "更新")
    @PostMapping(value="/update/byId")
    public CommonResult<?> updateActivity (@RequestBody AppAppointmentPersonalOffline param)  {
        if(param.getId()==null){
            return CommonResult.failed("id未设置");
        }
        AppAppointmentPersonalOffline personalOffline = iAppAppointmentPersonalOfflineService.view(param.getId());
        if(personalOffline==null){
            return CommonResult.failed("数据不存在");
        }
        personalOffline.setPersonNum(param.getPersonNum());
        personalOffline.setRemark(param.getRemark());
        personalOffline.setVisitDate(param.getVisitDate());
        personalOffline.setVisitFypdBatch(param.getVisitFypdBatch());
        return CommonResult.succeeded(iAppAppointmentPersonalOfflineService.save(personalOffline));
    }

    @ApiOperation(value = "查看")
    @GetMapping(value="/view/byId/{id}")
    public CommonResult<?> viewActivity (@PathVariable Long id)  {
        return CommonResult.succeeded(iAppAppointmentPersonalOfflineService.view(id));
    }



}
