package com.spup.controller.appointment;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.appointment.AppAppointmentTeamOrder;
import com.spup.dto.AppTeamOrderListRequest;
import com.spup.service.appointment.IAppAppointmentTeamOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(tags = "团队登记")
@RestController
@RequestMapping(value = "/team")
public class AppointmentTeamController {
    @Resource
    private IAppAppointmentTeamOrderService iAppAppointmentTeamOrderService;

    @ApiOperation(value = "分页查询")
    @PostMapping(value="/list/byPage")
    public CommonResult<?> listByPage (@RequestBody AppTeamOrderListRequest param)  {
        return CommonResult.succeeded(iAppAppointmentTeamOrderService.getList(param));
    }

    @ApiOperation(value = "保存")
    @PostMapping(value="/save")
    public CommonResult<?> save (@RequestBody AppAppointmentTeamOrder teamOrder, HttpServletRequest req) {
        String unionid = (String)req.getSession().getAttribute("unionid");
        //teamOrder.setOwnerUnionid(unionid);
        if(teamOrder.getId()!=null) {
            AppAppointmentTeamOrder view = iAppAppointmentTeamOrderService.view(teamOrder.getId());
            teamOrder.setOwnerUnionid(view.getOwnerUnionid());
            teamOrder.setCreateBy(view.getCreateBy());
            teamOrder.setCreateTime(view.getCreateTime());
            teamOrder.setOrderCategory(view.getOrderCategory());
            teamOrder.setExhibitionNo(view.getExhibitionNo());
        } else {
            teamOrder.setOwnerUnionid(unionid);
        }
        AppAppointmentTeamOrder save = iAppAppointmentTeamOrderService.save(teamOrder);
        return CommonResult.succeeded(save);
    }

    @ApiOperation(value = "查看")
    @PostMapping(value="/view/byId/{id}")
    public CommonResult<?> viewActivity (@PathVariable Long id)  {
        return CommonResult.succeeded(iAppAppointmentTeamOrderService.view(id));
    }



}
