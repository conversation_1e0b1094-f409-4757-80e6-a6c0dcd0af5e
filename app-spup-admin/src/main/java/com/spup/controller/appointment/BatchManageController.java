package com.spup.controller.appointment;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.appointment.AppBatch;
import com.spup.dto.AppBatchDTO;
import com.spup.service.appointment.IAppBatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


@Api(tags = "场次名额设定")
@RestController
@RequestMapping(value = "/manage/batch")
public class BatchManageController {
    @Resource
    private IAppBatchService iAppBatchService;

    @ApiOperation(value = "查询场次")
    @GetMapping(value="/query/{batchCategory}/{queryDate}")
    public CommonResult<List<AppBatch>> query (@PathVariable Byte batchCategory,
                                       @PathVariable String queryDate,
                                       HttpServletRequest req, HttpServletResponse res)  {
        return CommonResult.succeeded(iAppBatchService.getListByDate(batchCategory,queryDate));
    }

    @ApiOperation(value = "名额保存")
    @PostMapping(value="/save")
    public CommonResult<String> save (@RequestBody List<AppBatchDTO> batchDTOList, HttpServletRequest req, HttpServletResponse res)  {
        String openid = (String)req.getSession().getAttribute("openid");
        for (int i = 0; i < batchDTOList.size(); i++) {
            AppBatchDTO appBatchDTO = batchDTOList.get(i);
            AppBatch batch = iAppBatchService.getByNo(appBatchDTO.getBatchNo(), appBatchDTO.getBatchCategory());
            Integer ticketTotal = batch.getTicketTotal();
            Integer ticketRemaining = batch.getTicketRemaining();
            if(appBatchDTO.getTicketTotal()<ticketTotal){
                return CommonResult.failed("名额不可小于原来的名额数");
            }
            batch.setTicketTotal(appBatchDTO.getTicketTotal());
            batch.setTicketRemaining(ticketRemaining + appBatchDTO.getTicketTotal() - ticketTotal);
            batch.setUpdateBy(openid);
            iAppBatchService.save(batch);
        }
        return CommonResult.succeeded("");
    }
}
