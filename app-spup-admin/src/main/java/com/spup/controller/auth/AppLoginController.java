package com.spup.controller.auth;



import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.huangdou.commons.api.CommonResult;
import com.huangdou.commons.utils.JWTUtil;
import com.spup.db.entity.authority.AppManageUser;
import com.spup.service.IAppManageUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


@Api(tags = "登录")
@RestController
@RequestMapping(value = "/login")
public class AppLoginController {
    private static final Logger logger = LoggerFactory.getLogger(AppLoginController.class);

    @Resource
    private JWTUtil jwtUtil;
    @Resource
    private RestTemplate restTemplate;

    @Value("${wx.open.appId}")
    private String openAppid;
    @Value("${wx.open.secret}")
    private String openSecret;

    @Resource
    private IAppManageUserService iAppManageUserService;
    @Resource
    private ObjectMapper objectMapper;

    @ApiOperation(value = "登录")
    @GetMapping(value="/jscode2session/{code}")
    public CommonResult<?> jscode2session (@PathVariable  String code, HttpServletRequest req, HttpServletResponse res) throws IOException {
        logger.info("pad-admin entry into jscode2session,code: {}",code);

        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + openAppid + "&secret=" + openSecret + "&js_code=" + code + "&grant_type=authorization_code";
        //String responseStr = HttpRequestUtil.httpGet();
        ResponseEntity<String> responseEntity  = restTemplate.getForEntity(url, String.class);
        String responseStr = responseEntity.getBody();

        ObjectNode jsonObject = (ObjectNode)objectMapper.readTree(responseStr);
        if(jsonObject.get("errcode")==null){

            String openid = jsonObject.get("openid").textValue();
            String unionid = jsonObject.get("unionid").textValue();
            logger.info("用户登录openid："+openid+",unionid："+unionid);

            AppManageUser userByOpenId = iAppManageUserService.getUserByUnionid(unionid);
            if(userByOpenId==null){
                return CommonResult.failed(1000,"用户不存在");
            }
            ObjectNode returnObj = objectMapper.createObjectNode();
            returnObj.put("openid",openid);
            returnObj.put("unionid",unionid);
            returnObj.put("token",jwtUtil.getToken(unionid,openid,780));
            returnObj.put("validTime",780);
            returnObj.set("manageUser", objectMapper.valueToTree(userByOpenId));
            return CommonResult.succeeded(returnObj);
        } else {
            Integer errcode = jsonObject.get("errcode").intValue();
            logger.info("用户登录失败：{}",errcode);
            return CommonResult.failed(1000,errcode+":"+jsonObject.get("errmsg").textValue());
        }
    }

    @ApiOperation(value = "扫码获取openid")
    @GetMapping(value="/jscode2openid/{code}")
    public CommonResult<?> jscode2openid (@PathVariable  String code) throws IOException {
        logger.info("pad-admin entry into jscode2session,code: {}",code);

        // String responseStr = HttpRequestUtil.httpGet("https://api.weixin.qq.com/sns/oauth2/access_token?appid="+ openAppid +"&secret="+ openSecret +"&code="+code+"&grant_type=authorization_code");
        // logger.info("getToken: {}",responseStr);

        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + openAppid + "&secret=" + openSecret + "&js_code=" + code + "&grant_type=authorization_code";
        //String responseStr = HttpRequestUtil.httpGet();
        ResponseEntity<String> responseEntity  = restTemplate.getForEntity(url, String.class);
        String responseStr = responseEntity.getBody();
        ObjectNode jsonObject = (ObjectNode)objectMapper.readTree(responseStr);
        if(jsonObject.get("errcode")==null){
            String openid = jsonObject.get("openid").textValue();
            String unionid = jsonObject.get("unionid").textValue();
            ObjectNode returnObj = objectMapper.createObjectNode();
            returnObj.put("openid",openid);
            returnObj.put("unionid",unionid);
            return CommonResult.succeeded(returnObj);
        } else {
            Integer errcode = jsonObject.get("errcode").intValue();
            return CommonResult.failed(1000,errcode+":"+jsonObject.get("errmsg").textValue());
        }
    }


}
