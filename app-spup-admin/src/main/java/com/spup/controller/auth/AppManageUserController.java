package com.spup.controller.auth;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.authority.AppManageUser;
import com.spup.dto.ManagerUserListRequest;
import com.spup.dto.auth.ManageUserDto;
import com.spup.service.IAppManageUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;

@Api(tags = "统一认证管理", position = 8)
@RestController
@RequestMapping(value = "/manageUser")
public class AppManageUserController {
    @Resource
    private IAppManageUserService iAppManageUserService;

    @ApiOperation(value = "查询列表（分页）")
    @GetMapping(value = "/listByPage")
    public CommonResult<?> listByPage(ManagerUserListRequest param) throws UnsupportedEncodingException {
        return CommonResult.succeeded(iAppManageUserService.getListByPage(param));
    }

    @ApiOperation(value = "创建后台管理员", notes = "创建用户时，只传输用户基本信息即可。menuCode，采用英文逗号分隔")
    @PostMapping(value = "/createManageUser")
    public CommonResult<?> createManageUser(@RequestBody ManageUserDto dto) throws UnsupportedEncodingException {
        AppManageUser manageUserInDb = iAppManageUserService.getUserByUnionid(dto.getUnionid());
        if (manageUserInDb != null) {
            return CommonResult.failed("用户已存在");
        }
        AppManageUser manageUser = new AppManageUser();
        BeanUtils.copyProperties(dto, manageUser);
        AppManageUser result = iAppManageUserService.create(manageUser);
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "删除后台管理员")
    @GetMapping(value = "/deleteManageUser/{id}")
    public CommonResult<?> deleteManageUser(@PathVariable Long id) {
        return CommonResult.succeeded(iAppManageUserService.delete(id));
    }

    @ApiOperation(value = "更新后台管理员", notes = "id为必传字段，此接口支持编辑接口，启用，禁用（status：1启用；2禁用）")
    @PostMapping(value = "/updateManageUser")
    public CommonResult<?> updateManageUser(@RequestBody AppManageUser appManageUser) {
        return CommonResult.succeeded(iAppManageUserService.update(appManageUser));
    }

    @ApiOperation(value = "查看后台管理员")
    @GetMapping(value = "/viewManageUser/{id}")
    public CommonResult<?> viewManageUser(@PathVariable Long id) throws UnsupportedEncodingException {

        return CommonResult.succeeded(iAppManageUserService.view(id));
    }

}
