package com.spup.controller.my;

import com.huangdou.commons.api.CommonResult;
import com.spup.mp.entity.datacube.UserSummary;
import com.spup.mp.service.IMpDatacubeSerivce;
import com.spup.service.analysis.IMpDatacubeUserSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Api(tags = "手动执行公众号数据" )
@RestController
@RequestMapping(value="/my")
public class MpDataController {
    @Resource
    protected IMpDatacubeSerivce iMpDatacubeSerivce;
    @Resource
    private IMpDatacubeUserSummaryService userSummaryService;
    @ApiOperation(value = "图片上传")
    @PostMapping(value = "/user/{queryDate}")
    public CommonResult<?> getUserCumulate(@PathVariable String queryDate) throws Exception {
        LocalDate date = LocalDate.parse(queryDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Integer userCumulate = iMpDatacubeSerivce.getUserCumulate(date);
        List<UserSummary> userSummary1 = iMpDatacubeSerivce.getUserSummary(date);
        System.out.println(userSummary1);
        userSummaryService.save(date,userCumulate,userSummary1);
        return CommonResult.succeeded("ok");
    }
}
