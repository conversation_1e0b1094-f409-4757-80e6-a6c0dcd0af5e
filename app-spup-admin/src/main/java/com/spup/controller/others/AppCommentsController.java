package com.spup.controller.others;

import com.huangdou.commons.api.CommonResult;
import com.spup.dto.DateQueryRequest;
import com.spup.service.IAppCommentsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "留言管理")
@RestController
@RequestMapping("/comment")
public class AppCommentsController {
    @Autowired
    private IAppCommentsService iAppCommentsService;

    @ApiOperation(value = "留言")
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    public CommonResult<?> list(@RequestBody DateQueryRequest queryRequest)  {

        return CommonResult.succeeded(iAppCommentsService.getPageList(queryRequest));
    }
}
