package com.spup.controller.others;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.appointment.BlackList;
import com.spup.dto.CustomerListRequest;
import com.spup.service.BlackListService;
import com.spup.service.IAppCustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "客户管理")
@RestController
@RequestMapping(value = "/customer")
public class AppCustomerController {
    @Resource
    private IAppCustomerService iAppCustomerService;
    @Resource
    private BlackListService blackListService;

    @ApiOperation(value = "查询客户信息")
    @GetMapping(value="/listByPage")
    public CommonResult<?> listByPage (CustomerListRequest param)  {
        return CommonResult.succeeded(iAppCustomerService.getPageList(param));
    }

    @ApiOperation(value = "查询客户详细信息（含预约数据）")
    @PostMapping(value="/view/{unionid}")
    public CommonResult<?> view (@PathVariable String unionid)  {

        return CommonResult.succeeded(iAppCustomerService.get(unionid));
    }

    @ApiOperation(value = "添加黑名单")
    @PostMapping(value="/blackList/add/{unionid}/{categoryEnum}")
    public CommonResult<?> addBlack (@PathVariable String unionid,
                                     @PathVariable BlackList.CategoryEnum categoryEnum)  {
        return CommonResult.succeeded(blackListService.addBlackList(unionid,categoryEnum));
    }

    @ApiOperation(value = "取消黑名单")
    @PostMapping(value="/blackList/remove/{id}")
    public CommonResult<?> removeBlack (@PathVariable Long id)  {
        return CommonResult.succeeded(blackListService.removeBlackList(id));
    }

    @ApiOperation(value = "黑名单列表")
    @PostMapping(value="/blackList/list")
    public CommonResult<?> listBlack ()  {
        return CommonResult.succeeded(blackListService.findInEffect());
    }
}
