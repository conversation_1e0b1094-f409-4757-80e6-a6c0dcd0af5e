package com.spup.controller.others;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.AppSurroundingGoods;
import com.spup.dto.GoodsListRequest;
import com.spup.service.IAppSurroundingGoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(tags = "文创商品管理")
@RestController
@RequestMapping(value = "/goods")
public class AppGoodsController {
    @Resource
    private IAppSurroundingGoodsService iAppSurroundingGoodsService;


    @ApiOperation(value = "查询文创商品")
    @GetMapping(value="/listByPage")
    public CommonResult<?> listByPage (GoodsListRequest param) {
        return CommonResult.succeeded(iAppSurroundingGoodsService.getListByPage(param));
    }

    @ApiOperation(value = "创建文创商品")
    @PostMapping(value="/createGoods")
    public CommonResult<?> createActivity (@RequestBody AppSurroundingGoods goods, HttpServletRequest req){
        String unionid = (String)req.getSession().getAttribute("unionid");
        iAppSurroundingGoodsService.create(goods,unionid);
        return CommonResult.succeeded(goods);
    }

    @ApiOperation(value = "删除文创商品")
    @GetMapping(value="/deleteGoods/{id}")
    public CommonResult<?> deleteActivity (@PathVariable Long id, HttpServletRequest req){
        String openid = (String)req.getSession().getAttribute("openid");
        return CommonResult.succeeded(iAppSurroundingGoodsService.delete(id,openid));
    }

    @ApiOperation(value = "更新文创商品",notes="id为必传字段，此接口支持编辑接口，上架，下架（goodsStatus:1上架；2下架）")
    @PostMapping(value="/updateGoods")
    public CommonResult<?> updateActivity (@RequestBody AppSurroundingGoods goods, HttpServletRequest req) {
        String openid = (String)req.getSession().getAttribute("openid");

        return CommonResult.succeeded(iAppSurroundingGoodsService.update(goods,openid));
    }

    @ApiOperation(value = "查看文创商品")
    @GetMapping(value="/viewGoods/{id}")
    public CommonResult<?> viewManageUser (@PathVariable Long id)  {

        return CommonResult.succeeded(iAppSurroundingGoodsService.view(id));
    }

}
