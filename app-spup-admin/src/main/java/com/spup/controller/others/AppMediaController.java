package com.spup.controller.others;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.AppMedia;
import com.spup.service.IAppMediaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Api(tags = "媒体对象管理")
@RestController
@RequestMapping(value = "/media")
public class AppMediaController {
    @Resource
    private IAppMediaService iAppMediaService;

    @ApiOperation(value = "上传文件")
    @ApiParam(name = "user",value="{}")
    @PostMapping(value = "/upload")
    public CommonResult<?> upload(MultipartFile file, HttpServletRequest request) throws IOException {
        String md5 = DigestUtils.md5DigestAsHex(file.getInputStream());

        AppMedia old_media = iAppMediaService.getMediaByMd5(md5);
        if(old_media!=null){
            return  CommonResult.succeeded(old_media);
        }

        String openid = (String)request.getSession().getAttribute("openid");

        return iAppMediaService.saveMedia(md5,file,openid);
    }


}
