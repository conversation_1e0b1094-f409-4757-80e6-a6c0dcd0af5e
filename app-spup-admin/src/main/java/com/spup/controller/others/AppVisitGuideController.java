package com.spup.controller.others;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.AppVisitGuide;
import com.spup.dto.VisitGuideListRequest;
import com.spup.service.IAppVisitGuideService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;

@Api(tags = "线上导览管理", position = 8)
@RestController
@RequestMapping(value = "/visitGuide")
public class AppVisitGuideController {
    @Resource
    private IAppVisitGuideService iAppVisitGuideService;

    @Resource
    private ObjectMapper objectMapper;

    @ApiOperation(value = "查询列表（分页）")
    @GetMapping(value = "/listByPage")
    public CommonResult<?> listByPage(VisitGuideListRequest param) throws UnsupportedEncodingException {
        return CommonResult.succeeded(iAppVisitGuideService.getListByPage(param));
    }

    @ApiOperation(value = "创建线上导览点位", notes = "showImgs与show_voices采用jsonArray字符串格式")
    @PostMapping(value = "/createSpot")
    public CommonResult<?> createSpot(@RequestBody AppVisitGuide appVisitGuide, HttpServletRequest req)
            throws UnsupportedEncodingException {
        String openid = (String) req.getSession().getAttribute("openid");
        iAppVisitGuideService.create(appVisitGuide, openid);
        return CommonResult.succeeded(appVisitGuide);
    }

    @ApiOperation(value = "删除线上导览点位")
    @GetMapping(value = "/deleteSpot/{id}")
    public CommonResult<?> deleteSpot(@PathVariable Long id, HttpServletRequest req)
            throws UnsupportedEncodingException {
        String openid = (String) req.getSession().getAttribute("openid");
        return CommonResult.succeeded(iAppVisitGuideService.delete(id, openid));
    }

    @ApiOperation(value = "更新线上导览点位", notes = "id为必传字段")
    @PostMapping(value = "/updateSpot")
    public CommonResult<?> updateSpot(@RequestBody AppVisitGuide appVisitGuide, HttpServletRequest req)
            throws UnsupportedEncodingException {
        String openid = (String) req.getSession().getAttribute("openid");

        return CommonResult.succeeded(iAppVisitGuideService.update(appVisitGuide, openid));
    }

    @ApiOperation(value = "查看线上导览点位")
    @GetMapping(value = "/viewSpot/{id}")
    public CommonResult<?> viewSpot(@PathVariable Long id, HttpServletRequest reqServletRequest) throws IOException {

        AppVisitGuide view = iAppVisitGuideService.view(id);

        ObjectNode node = (ObjectNode) objectMapper.valueToTree(view);

        /*
         * if(StringUtils.hasLength(view.getShowImgs())){
         * node.put("showImgs",objectMapper.readTree(view.getShowImgs()));
         * }
         * if(StringUtils.hasLength(view.getShowVoices())){
         * node.put("showVoices",objectMapper.readTree(view.getShowVoices()));
         * }
         */
        if (StringUtils.hasLength(view.getTips())) {
            node.set("tips", objectMapper.readTree(view.getTips()));
        }
        return CommonResult.succeeded(node);
    }

}
