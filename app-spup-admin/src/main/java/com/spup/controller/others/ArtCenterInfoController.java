package com.spup.controller.others;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.ArtCenterInfo;
import com.spup.service.ArtCenterInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Optional;

@Api(tags = "场馆信息管理")
@RestController
@RequestMapping("/info")
public class ArtCenterInfoController {
    @Resource
    private ArtCenterInfoService artCenterInfoService;

    @ApiOperation(value = "获取信息")
    @RequestMapping(value = "/get/{section}",method = RequestMethod.POST)
    public CommonResult<?> get(@PathVariable ArtCenterInfo.SectionEnum section)  {
        Optional<ArtCenterInfo> infoOpt = artCenterInfoService.findBySection(section);
        ArtCenterInfo info = null;
        if(infoOpt.isPresent()){
            info = infoOpt.get();
        }
        return CommonResult.succeeded(info);
    }

    @ApiOperation(value = "保存信息")
    @RequestMapping(value = "/save/{section}",method = RequestMethod.POST)
    public CommonResult<?> save(@PathVariable ArtCenterInfo.SectionEnum section,
                                @RequestBody ArtCenterInfo info)  {
        info.setSection(section);
        return CommonResult.succeeded(artCenterInfoService.save(info));
    }
}
