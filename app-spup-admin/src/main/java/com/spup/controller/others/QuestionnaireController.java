package com.spup.controller.others;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.CommQuestionnaireAnswer;
import com.spup.dto.QuestionnaireListRequest;
import com.spup.service.ICommQuestionnaireAnswerService;
import com.spup.service.ICommQuestionnaireService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;

@Api(tags = "问卷管理" , position = 6)
@Controller
@RequestMapping(value = "/questionnaire")
public class QuestionnaireController {
    @Resource
    private ICommQuestionnaireService iCommQuestionnaireService;
    @Resource
    private ICommQuestionnaireAnswerService iCommQuestionnaireAnswerService;
    @Resource
    private ObjectMapper objectMapper;

    @ApiOperation(value = "问卷列表(分页)")
    @PostMapping(value="/listByPage")
    @ResponseBody
    public CommonResult<?> listByPage (@RequestBody QuestionnaireListRequest param, HttpServletRequest req, HttpServletResponse res) throws UnsupportedEncodingException {
        return CommonResult.succeeded(iCommQuestionnaireService.getPageList(param));
    }

    @ApiOperation(value = "查看问卷填报（分页）")
    @GetMapping(value={"/viewAnswer/"})
    @ResponseBody
    public CommonResult<?> viewAnswer (@PathVariable Long questionnaireId,QuestionnaireListRequest param) {
        if(questionnaireId==null){
            questionnaireId = 1L;
        }
        if(param.getQuestionnaireId()==null){
            param.setQuestionnaireId(questionnaireId);
        }
        Page<CommQuestionnaireAnswer> pageList = iCommQuestionnaireAnswerService.getPageList(param);
        return CommonResult.succeeded(pageList);
    }
    @ApiOperation(value = "查看问卷填报（分页）")
    @PostMapping(value={"/viewAnswer/{questionnaireId}"})
    @ResponseBody
    public CommonResult<?> viewAnswer2 (@PathVariable Long questionnaireId, @RequestBody QuestionnaireListRequest param) {
        if(questionnaireId==null){
            questionnaireId = 1L;
        }
        if(param.getQuestionnaireId()==null){
            param.setQuestionnaireId(questionnaireId);
        }
        Page<CommQuestionnaireAnswer> pageList = iCommQuestionnaireAnswerService.getPageList(param);
        return CommonResult.succeeded(pageList);
    }

    @ApiOperation(value = "查看问卷填报（分页）")
    @GetMapping(value={"/viewAnswer"})
    @ResponseBody
    public CommonResult<?> viewAnswer2 (@RequestBody QuestionnaireListRequest param) {
        Page<CommQuestionnaireAnswer> pageList = iCommQuestionnaireAnswerService.getPageList(param);
        return CommonResult.succeeded(pageList);
    }
    @ApiOperation(value = "查看问卷填报详情")
    @GetMapping(value="/viewAnswerDetail/{answerId}")
    @ResponseBody
    public CommonResult<?> viewAnswerDetail (@PathVariable long answerId) throws JsonProcessingException {

        return CommonResult.succeeded(iCommQuestionnaireAnswerService.getAnswerDetail(answerId));
    }

   /* @ApiOperation(value = "导出问卷填报")
    @GetMapping(value={"/exportAnswer/{questionnaireId}","/exportAnswer"})
    public void exportAnswer (@PathVariable(value = "questionnaireId" , required = false) Long questionnaireId, QuestionnaireListRequest param, HttpServletRequest req, HttpServletResponse response) throws IOException {

        if(questionnaireId==null){
            questionnaireId = 1L;
        }
        CommQuestionnaire questionnaire = iCommQuestionnaireService.getQuestionnaireById(questionnaireId);

        Calendar now = Calendar.getInstance();
        String filename = questionnaire.getTitle()+"_"+DateTimeUtil.getTime(DateTimeUtil.PATTERN_7,now)+".xls";
        //设置下载头部文件信息
        response.setHeader("Content-type", "application/vnd.ms-excel");
        String agent = req.getHeader("USER-AGENT");
        agent = agent.toLowerCase();
        if (agent.indexOf("msie") > 0) {
            filename = URLEncoder.encode(filename, "UTF-8");
        } else {
            filename = new String(filename.getBytes("UTF-8"), "ISO8859-1");
        }
        response.addHeader("Content-Disposition", "attachment;filename=" + filename);

        //创建sheet，相当于一个excelsheel
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet("sheet1");
        //得到excel标题内容
        param.setPageNum(1);
        param.setPageSize(50000);
        List<CommQuestionnaireAnswer> answers = iCommQuestionnaireAnswerService.getAllAnswer(questionnaireId, param);

        List<String> titleList = new ArrayList<>();
        titleList.add("序号");
        titleList.add("提交答卷时间");


        ArrayNode optionsArray = (ArrayNode)objectMapper.readTree(questionnaire.getOptions());
        for (int i = 0; i < optionsArray.size(); i++) {
            ObjectNode option = (ObjectNode)optionsArray.get(i);
            titleList.add(option.get("sortCode").textValue()+"、"+option.get("title").textValue());
        }

        int len = titleList.size();
        //设置单元格样式
        CellStyle cellStyle = workbook.createCellStyle(); //标题样式
        cellStyle.setAlignment(HorizontalAlignment.CENTER);//水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        //设置字体
        Font headerFont = workbook.createFont();    //标题字体
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 11);
        cellStyle.setFont(headerFont);
        short width = 20, height = 25 * 20;
        sheet.setDefaultColumnWidth(width);
        //创建第一行，用来放标题
        Row header = sheet.createRow(0);
        for (int i = 0; i < len; i++) { //设置标题
            String title = titleList.get(i);
            Cell cell = header.createCell(i);
            cell.setCellValue(title);
            cell.setCellStyle(cellStyle);
        }
        header.setHeight(height);
        //设置内容样式
        CellStyle contentStyle = workbook.createCellStyle(); //内容样式
        contentStyle.setAlignment(HorizontalAlignment.LEFT);//水平居中
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //contentStyle.setWrapText(true);

        CellStyle textareaContentStyle = workbook.createCellStyle(); //内容样式
        textareaContentStyle.setAlignment(HorizontalAlignment.LEFT);//水平居中
        textareaContentStyle.setWrapText(true);
        textareaContentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //得到controller传过来导出的数据，并填充到每一行中
        int varCount = answers.size();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        for (int i = 0; i < varCount; i++) {
            Row infoRow = sheet.createRow(i + 1);
            CommQuestionnaireAnswer answer = answers.get(i);
            //序号
            Cell cell = infoRow.createCell(0);
            cell.setCellValue(i + 1);
            cell.setCellStyle(contentStyle);
            //提交时间
            cell = infoRow.createCell(1);
            cell.setCellValue(answer.getCreateTime().format(formatter));
            cell.setCellStyle(contentStyle);

            ArrayNode array = (ArrayNode)objectMapper.readTree(answer.getAnswer());

            for (int j = 0; j < array.size(); j++) {
                ObjectNode obj = (ObjectNode)array.get(j);
                String type = obj.get("type").textValue();
                String selectValues = "";
                if ("1".equals(type) || "2".equals(type)) {
                    ArrayNode selects = (ArrayNode)obj.get("options");
                    for (int k = 0; k < selects.size(); k++) {
                        ObjectNode option = (ObjectNode)selects.get(k);
                        if ( option.get("select").intValue() == 1) {
                            selectValues += "┋" + option.get("title").textValue();
                            if (option.get("input") != null) {
                                selectValues += "〖" + option.get("input").textValue() + "〗";
                            }
                        }
                    }
                    if (selectValues.length() > 0) {
                        selectValues = selectValues.substring(1);
                    }
                    cell = infoRow.createCell(j + 2);
                    cell.setCellValue(selectValues);
                    cell.setCellStyle(contentStyle);
                } else {
                    selectValues = obj.get("input").textValue();
                    cell = infoRow.createCell(j + 2);
                    cell.setCellValue(selectValues);
                    cell.setCellStyle(textareaContentStyle);
                }

            }
        }
        workbook.write(response.getOutputStream());
    }
*/


}
