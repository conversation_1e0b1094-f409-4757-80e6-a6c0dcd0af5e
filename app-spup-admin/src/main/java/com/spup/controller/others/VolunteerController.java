package com.spup.controller.others;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.Volunteer;
import com.spup.poi.ExcelReader;
import com.spup.service.IUploadService;
import com.spup.service.VolunteerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

@Api(tags = "志愿者管理")
@RestController
@RequestMapping(value = "/volunteer")
public class VolunteerController {
    @Resource
    private VolunteerService volunteerService;
    @Resource
    private IUploadService iUploadService;

    @ApiOperation(value = "志愿者导入")
    @PostMapping(value = "/import/{category}")
    public CommonResult<?> uploadElement(@PathVariable Volunteer.VolunteerCategoryEnum category, MultipartFile file) throws IOException {
        //验证
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException();
        }
        String fileType = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        if (!("xls".equalsIgnoreCase(fileType)
                || "xlsx".equalsIgnoreCase(fileType))) {
            return CommonResult.failed("不支持的格式");
        }
        //解析
        File file1 = saveMultipartFile(file, System.currentTimeMillis() +"."+ fileType);
        ExcelReader reader = new ExcelReader(file1);
        reader.readColumnComment();
        reader.readRowData();
        file1.delete();

        //UID	志愿者编号	姓名	性别	年龄	服务时长	电话	手机	邮件	所在区县
        List<Map<String, String>> rowList = reader.getRowList();
        //存储
        for (Map<String, String> rowDataMap: rowList) {
            String volunteerId = rowDataMap.get("2");
            if(!StringUtils.hasLength(volunteerId)){
                continue;
            }
            Volunteer v = new Volunteer();
            v.setUid(rowDataMap.get("1"));
            v.setVolunteerId(volunteerId);
            v.setName(rowDataMap.get("3"));
            v.setGender(rowDataMap.get("4"));
            v.setAge(Integer.valueOf(rowDataMap.get("5")));
            v.setServiceDuration(Double.valueOf(rowDataMap.get("6")));
            v.setFixedTel(rowDataMap.get("7"));
            v.setPhone(rowDataMap.get("8"));
            v.setEmail(rowDataMap.get("9"));
            v.setArea(rowDataMap.get("10"));
            v.setUnit(rowDataMap.get("11"));
            v.setSkill(rowDataMap.get("12"));
            v.setCategory(category);
            volunteerService.save(v);
        }

        return CommonResult.succeeded("");
    }
    private  File saveMultipartFile(MultipartFile file, String localFilePath) throws IOException {
        File localFile = new File(localFilePath);
        InputStream input=null;
        FileOutputStream output=null;
        try {
            input = file.getInputStream();
            output = new FileOutputStream(localFile);
            FileCopyUtils.copy(input, output);
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if(input!=null) {
                input.close();
            }
            if(output!=null) {
                output.close();
            }
        }
        return localFile;
    }
    @ApiOperation(value = "志愿者列表")
    @PostMapping(value = "/list/{category}")
    public CommonResult<?> list(@PathVariable Volunteer.VolunteerCategoryEnum category) {
        return CommonResult.succeeded(volunteerService.list(category));
    }

}
