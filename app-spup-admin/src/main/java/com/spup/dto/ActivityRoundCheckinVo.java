package com.spup.dto;


import com.spup.db.entity.activity.ActivityRound;
import com.spup.db.entity.activity.ActivitySubmitCustomer;

public class ActivityRoundCheckinVo {
    
    private ActivityRound actRound;
    private boolean greyButton;
    private boolean visible;
    private ActivitySubmitCustomer.SubmitCustomerStatusEnum checkInStatus;
    private String buttonInfo;

    public ActivityRound getActRound() {
        return actRound;
    }
    public void setActRound(ActivityRound actRound) {
        this.actRound = actRound;
    }
    public boolean isGreyButton() {
        if (actRound.getStatus() == ActivityRound.ActRoundStatusEnum.SUBMITTING
                || actRound.getStatus() == ActivityRound.ActRoundStatusEnum.WAITING
                || actRound.getStatus() == ActivityRound.ActRoundStatusEnum.SUBMITPAUSED) {
            if (checkInStatus == ActivitySubmitCustomer.SubmitCustomerStatusEnum.CHECKEDIN){
                this.greyButton = true;
            } else {
                this.greyButton = false;
            }
        } else {
            this.greyButton = true;
        }
        return greyButton;
    }
    public String getButtonInfo() {
        if (checkInStatus == ActivitySubmitCustomer.SubmitCustomerStatusEnum.CHECKEDIN) {
            this.buttonInfo = "已签";
        } else {
            this.buttonInfo = "签到";
        }
        return this.buttonInfo;
    }
    public boolean isVisible() {
        if (actRound.getStatus() == ActivityRound.ActRoundStatusEnum.SUBMITTING
                || actRound.getStatus() == ActivityRound.ActRoundStatusEnum.WAITING
                || actRound.getStatus() == ActivityRound.ActRoundStatusEnum.RUNNING) {
            this.visible = true;
        } else {
            this.visible = false;
        }
        return visible;
    }

    public ActivitySubmitCustomer.SubmitCustomerStatusEnum getCheckInStatus() {
        return checkInStatus;
    }

    public void setCheckInStatus(ActivitySubmitCustomer.SubmitCustomerStatusEnum checkInStatus) {
        this.checkInStatus = checkInStatus;
    }
}
