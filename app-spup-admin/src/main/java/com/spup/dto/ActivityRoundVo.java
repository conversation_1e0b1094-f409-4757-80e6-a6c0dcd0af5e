package com.spup.dto;


import com.spup.db.entity.activity.ActivityRound;

public class ActivityRoundVo {
    
    private ActivityRound actRound;
    private boolean greyButton;
    private boolean visible;
    private String buttonInfo;

    public ActivityRound getActRound() {
        return actRound;
    }
    public void setActRound(ActivityRound actRound) {
        this.actRound = actRound;
    }
    public boolean isGreyButton() {
        return greyButton;
    }
    public void setGreyButton(ActivityRound.ActRoundStatusEnum status) {
        if (status == ActivityRound.ActRoundStatusEnum.SUBMITTING) {
            this.greyButton = false;
        } else {
            this.greyButton = true;
        }
    }
    public String getButtonInfo() {
        return buttonInfo;
    }
    public void setButtonInfo(boolean greyButton) {
        if (greyButton) {
            this.buttonInfo = "已满";
        } else {
            this.buttonInfo = "报名";
        }
    }
    public boolean isVisible() {
        return visible;
    }
    public void setVisible(ActivityRound.ActRoundStatusEnum status) {
        if (status == ActivityRound.ActRoundStatusEnum.SUBMITTING
         || status == ActivityRound.ActRoundStatusEnum.WAITING
         || status == ActivityRound.ActRoundStatusEnum.RUNNING) {
            this.visible = true;
        } else {
            this.visible = false;
        }  
    }
}
