package com.spup.dto;





import com.spup.db.entity.appointment.AppAppointmentItemSuborder;
import com.spup.db.entity.appointment.AppAppointmentOrder;

import java.util.List;

public class AppAppointmentItemOrderResponse extends AppAppointmentOrder {
    private List<AppAppointmentItemSuborder> suborders;

    public List<AppAppointmentItemSuborder> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppAppointmentItemSuborder> suborders) {
        this.suborders = suborders;
    }
}
