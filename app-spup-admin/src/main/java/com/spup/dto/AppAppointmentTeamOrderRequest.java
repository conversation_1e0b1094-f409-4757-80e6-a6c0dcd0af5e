package com.spup.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("团体预约对象")
public class AppAppointmentTeamOrderRequest {
    @ApiModelProperty("场次编号，非id，如202301180930")
    private String batchNo;
    private Integer visitorsNum;
    private String owerUnit;
    private String ownerName;
    private String ownerPhone;

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Integer getVisitorsNum() {
        return visitorsNum;
    }

    public void setVisitorsNum(Integer visitorsNum) {
        this.visitorsNum = visitorsNum;
    }

    public String getOwerUnit() {
        return owerUnit;
    }

    public void setOwerUnit(String owerUnit) {
        this.owerUnit = owerUnit;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerPhone() {
        return ownerPhone;
    }

    public void setOwnerPhone(String ownerPhone) {
        this.ownerPhone = ownerPhone;
    }
}
