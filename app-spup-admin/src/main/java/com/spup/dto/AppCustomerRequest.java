package com.spup.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("客户对象")
public class AppCustomerRequest {
    private String unionid;
    private String realName;
    private String phone;

    @ApiModelProperty("证件类型，1身份证")
    private Byte cardCategory = 1;

    private String cardNo;
    @ApiModelProperty("性别，1男2女")
    private Byte userGender = 1;

    private String job;

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Byte getCardCategory() {
        return cardCategory;
    }

    public void setCardCategory(Byte cardCategory) {
        this.cardCategory = cardCategory;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public Byte getUserGender() {
        return userGender;
    }

    public void setUserGender(Byte userGender) {
        this.userGender = userGender;
    }
}
