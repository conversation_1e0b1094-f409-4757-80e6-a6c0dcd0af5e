package com.spup.dto;


import com.huangdou.commons.api.PageInfo;
import com.spup.db.entity.appointment.AppAppointmentTeamOrder;

import java.time.LocalDate;

public class AppTeamOrderListRequest extends PageInfo {
    private String exhibitionNo;
    private String teamName;
    private LocalDate startDate;
    private LocalDate endDate;
    private Short orderStatus;
    private AppAppointmentTeamOrder.MethodEnum method;

    public String getExhibitionNo() {
        return exhibitionNo;
    }

    public void setExhibitionNo(String exhibitionNo) {
        this.exhibitionNo = exhibitionNo;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Short getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Short orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public AppAppointmentTeamOrder.MethodEnum getMethod() {
        return method;
    }

    public void setMethod(AppAppointmentTeamOrder.MethodEnum method) {
        this.method = method;
    }
}
