package com.spup.dto;


import com.spup.db.entity.appointment.AppAppointmentOrder;
import com.spup.db.entity.appointment.AppAppointmentTeamOrder;
import com.spup.db.entity.authority.AppCustomer;

import lombok.Data;

import java.util.List;

@Data
public class CustomerDTO {
    private AppCustomer customer;
    private List<AppAppointmentOrder> orders;
    private List<AppAppointmentTeamOrder> teamOrders;
}
