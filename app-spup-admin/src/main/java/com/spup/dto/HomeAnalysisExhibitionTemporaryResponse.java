package com.spup.dto;

public class HomeAnalysisExhibitionTemporaryResponse {
    private long ticketTotal;
    private long onlineTicketTotal;
    private long onlineCheckinTotal;
    private long offlineTicketTotal;
    private long teamTotal;
    private long teamPersonsTotal;
    private long onlineTeamTicketTotal;
    private long onlineTeamPersonsTotal;
    private long onlineTeamCheckinTotal;
    private long onlineTeamCheckinPersonsTotal;
    private long offlineTeamTicketTotal;
    private long offlineTeamPersonsTotal;

    @Override
    public String toString() {
        return "HomeAnalysisExhibitionTemporaryResponse{" +
                "ticketTotal=" + ticketTotal +
                ", onlineTicketTotal=" + onlineTicketTotal +
                ", onlineCheckinTotal=" + onlineCheckinTotal +
                ", offlineTicketTotal=" + offlineTicketTotal +
                ", teamTotal=" + teamTotal +
                ", teamPersonsTotal=" + teamPersonsTotal +
                ", onlineTeamTicketTotal=" + onlineTeamTicketTotal +
                ", onlineTeamPersonsTotal=" + onlineTeamPersonsTotal +
                ", onlineTeamCheckinTotal=" + onlineTeamCheckinTotal +
                ", onlineTeamCheckinPersonsTotal=" + onlineTeamCheckinPersonsTotal +
                ", offlineTeamTicketTotal=" + offlineTeamTicketTotal +
                ", offlineTeamPersonsTotal=" + offlineTeamPersonsTotal +
                '}';
    }

    public long getTicketTotal() {
        return ticketTotal;
    }

    public void setTicketTotal(long ticketTotal) {
        this.ticketTotal = ticketTotal;
    }

    public long getOnlineTicketTotal() {
        return onlineTicketTotal;
    }

    public void setOnlineTicketTotal(long onlineTicketTotal) {
        this.onlineTicketTotal = onlineTicketTotal;
    }

    public long getOnlineCheckinTotal() {
        return onlineCheckinTotal;
    }

    public void setOnlineCheckinTotal(long onlineCheckinTotal) {
        this.onlineCheckinTotal = onlineCheckinTotal;
    }

    public long getOfflineTicketTotal() {
        return offlineTicketTotal;
    }

    public void setOfflineTicketTotal(long offlineTicketTotal) {
        this.offlineTicketTotal = offlineTicketTotal;
    }

    public long getTeamTotal() {
        return teamTotal;
    }

    public void setTeamTotal(long teamTotal) {
        this.teamTotal = teamTotal;
    }

    public long getTeamPersonsTotal() {
        return teamPersonsTotal;
    }

    public void setTeamPersonsTotal(long teamPersonsTotal) {
        this.teamPersonsTotal = teamPersonsTotal;
    }

    public long getOnlineTeamTicketTotal() {
        return onlineTeamTicketTotal;
    }

    public void setOnlineTeamTicketTotal(long onlineTeamTicketTotal) {
        this.onlineTeamTicketTotal = onlineTeamTicketTotal;
    }

    public long getOnlineTeamPersonsTotal() {
        return onlineTeamPersonsTotal;
    }

    public void setOnlineTeamPersonsTotal(long onlineTeamPersonsTotal) {
        this.onlineTeamPersonsTotal = onlineTeamPersonsTotal;
    }

    public long getOnlineTeamCheckinTotal() {
        return onlineTeamCheckinTotal;
    }

    public void setOnlineTeamCheckinTotal(long onlineTeamCheckinTotal) {
        this.onlineTeamCheckinTotal = onlineTeamCheckinTotal;
    }

    public long getOnlineTeamCheckinPersonsTotal() {
        return onlineTeamCheckinPersonsTotal;
    }

    public void setOnlineTeamCheckinPersonsTotal(long onlineTeamCheckinPersonsTotal) {
        this.onlineTeamCheckinPersonsTotal = onlineTeamCheckinPersonsTotal;
    }

    public long getOfflineTeamTicketTotal() {
        return offlineTeamTicketTotal;
    }

    public void setOfflineTeamTicketTotal(long offlineTeamTicketTotal) {
        this.offlineTeamTicketTotal = offlineTeamTicketTotal;
    }

    public long getOfflineTeamPersonsTotal() {
        return offlineTeamPersonsTotal;
    }

    public void setOfflineTeamPersonsTotal(long offlineTeamPersonsTotal) {
        this.offlineTeamPersonsTotal = offlineTeamPersonsTotal;
    }
}
