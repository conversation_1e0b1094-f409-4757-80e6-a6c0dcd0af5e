package com.spup.enums;

public enum BatchStatusEnum {
    CLOSED((byte)0, "已关闭"),
    RUNNING((byte)1, "正常"),
    ; //此写法防止扩充时忘记分号

    private byte code;
    private String name;

    private BatchStatusEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static void main(String[] args) {
        byte a = 1;
        System.out.println(a&-1);
    }
}
