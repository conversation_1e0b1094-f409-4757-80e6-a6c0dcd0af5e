package com.spup.interceptor;

import com.auth0.jwt.exceptions.AlgorithmMismatchException;
import com.auth0.jwt.exceptions.SignatureVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huangdou.commons.api.CommonResult;
import com.huangdou.commons.api.ResultCodeEnum;
import com.huangdou.commons.utils.JWTUtil;
import com.spup.db.entity.authority.AppManageUser;
import com.spup.service.IAppManageUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@Component
public class TokenInterceptor implements HandlerInterceptor {
    @Autowired
    private JWTUtil jwtUtil;

    @Resource
    private IAppManageUserService iAppManageUserService;
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler)
            throws Exception {
        // return true;
        String token = request.getHeader("Authorization");
        if (request.getMethod().equals(RequestMethod.OPTIONS.name())) {
            System.out.println("**********options请求");
            return true;
        }
        CommonResult<ResultCodeEnum> commonResult = null;
        try {
            // 1.校验JWT字符串
            DecodedJWT decodedJWT = jwtUtil.decodeToken(token);
            // 2.取出JWT字符串载荷中的随机token，从Redis中获取用户信息

            Claim unionid = decodedJWT.getClaim("unionid");
            Claim openid = decodedJWT.getClaim("openid");
            System.out.println("TokenInterceptor**********************" + unionid.asString());
            String s_unionid = (String) request.getSession().getAttribute("unionid");
            if (!unionid.asString().equals(s_unionid)) {
                request.getSession().setAttribute("unionid", unionid.asString());
                request.getSession().setAttribute("openid", openid.asString());

            }
            AppManageUser manageUser = iAppManageUserService.getUserByUnionid(unionid.asString());
            if (manageUser == null) {
                return false;
            }
            return true;
        } catch (SignatureVerificationException e) {
            System.out.println("无效签名");
            e.printStackTrace();
            commonResult = CommonResult.failed(ResultCodeEnum.GET_TOKEN_KEY_FAILED);
        } catch (TokenExpiredException e) {
            System.out.println("token已经过期");
            e.printStackTrace();
            commonResult = CommonResult.failed(ResultCodeEnum.AUTHORIZED_FAILED);
        } catch (AlgorithmMismatchException e) {
            System.out.println("算法不一致");
            e.printStackTrace();
            commonResult = CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        } catch (Exception e) {
            System.out.println("token无效");
            e.printStackTrace();
            commonResult = CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        }
        String jsonObjectStr = objectMapper.writeValueAsString(commonResult);
        returnJson(response, jsonObjectStr);
        return false;
    }

    @Override
    public void postHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull  Object handler,
            @Nullable ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler,
            @Nullable Exception ex) {

    }

    private void returnJson(HttpServletResponse response, String json) throws Exception {
        PrintWriter writer = null;
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        try {
            writer = response.getWriter();
            writer.print(json);

        } catch (IOException e) {
        } finally {
            if (writer != null)
                writer.close();
        }
    }

    // private boolean isJson(HttpServletRequest request) {
    // if (request.getContentType() != null) {
    // return request.getContentType().equals(MediaType.APPLICATION_JSON_VALUE) ||
    // request.getContentType().equals(MediaType.APPLICATION_JSON_UTF8_VALUE);
    // }

    // return false;
    // }
}
