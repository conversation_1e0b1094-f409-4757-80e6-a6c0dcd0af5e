package com.spup.javaConfig;

import com.huangdou.commons.api.CommonResult;
import com.spup.exception.ValidException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
@Slf4j
@RestControllerAdvice(basePackages="com.spup")
public class ExceptionControllerAdvice {

    @ExceptionHandler(value=MethodArgumentNotValidException.class)
    public CommonResult<?> handleValidException(MethodArgumentNotValidException e) {

        log.error("数据校验出现问题：{}，异常类型：{}",e.getMessage(),e.getClass());

        // ExceptionHandler里面可以获取BindingResult，通过BindingResult对象获取实际的错误信息
        BindingResult bindingResult = e.getBindingResult();
        StringBuffer stringBuffer = new StringBuffer();
        bindingResult.getFieldErrors().forEach( item -> {
            String message = item.getDefaultMessage();
            stringBuffer.append(message + ";");
        });

        return CommonResult.failed(stringBuffer + "");
    }
    @ExceptionHandler(value= ValidException.class)
    public CommonResult<?> handleValidException(Exception e) {
        log.error("请求数据验证失败:", e);
        return CommonResult.failed(e.getMessage());
    }
   @ExceptionHandler(value=Exception.class)
   public CommonResult<?> handleGeneralExceptions(Exception e) {
       log.error("系统异常:", e);
       return CommonResult.failed("系统异常");
   }
    @ExceptionHandler(value=Throwable.class)
    public CommonResult<?> handleException(Throwable throwable) {

        log.error("系统错误:", throwable);
        return CommonResult.failed("系统错误");
    }


}
