package com.spup.javaConfig;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.spup.service.IAppOperateLogService;
import org.apache.catalina.connector.RequestFacade;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Objects;

/**
 * 统一日志处理切面
 * Created by sj
 */
@Aspect
@Component
public class LogAop2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogAop2.class);
    @Autowired
    private IAppOperateLogService iAppOperateLogService;

    @Resource
    private ObjectMapper objectMapper;

   /* @Pointcut("target(com.spup.service.*)")
    public void webLogForService() {
    }
*/
    @Pointcut("execution(public * com.spup.controller..*.*(..))")
    public void webLogForController() {
    }



    @Before("webLogForController() ")
    public void doBefore(JoinPoint joinPoint) {
        // 接收到请求，记录请求内容
        StringBuilder methodParams = new StringBuilder("");
        Arrays.stream(joinPoint.getArgs())
                .filter(o -> !(o instanceof RequestFacade))
                .forEach(o -> {
            try {
                methodParams.append(objectMapper.writeValueAsString(o));
            } catch (JsonProcessingException e) {
                //e.printStackTrace();
            }
        });


        //获取当前请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
         HttpServletRequest request = null;
        if (Objects.isNull(attributes)) {
            throw new NullPointerException();
        } else {
            request = attributes.getRequest();
        }

        String urlStr = request.getRequestURL().toString();
        String openid = (String)request.getSession().getAttribute("openid");
        LOGGER.info("{},{},{},{}",joinPoint.getTarget().getClass().getSimpleName(),urlStr,openid, methodParams.toString());

        try {
            iAppOperateLogService.saveLog(request, methodParams.toString());
        } catch (Exception e){
            e.printStackTrace();
        }
    }


    @AfterReturning(returning = "data", pointcut = "webLogForController()")
    public void doAfterReturning(JoinPoint joinPoint, Object data) throws JsonProcessingException {
        String methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName() ;
        LOGGER.info("return:" + methodName, objectMapper.writeValueAsString(data));
    }

}



