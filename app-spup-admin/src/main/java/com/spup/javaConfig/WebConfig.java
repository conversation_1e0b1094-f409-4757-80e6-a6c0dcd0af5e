package com.spup.javaConfig;

import com.spup.interceptor.TokenInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Bean
    public TokenInterceptor sessionInterceptor() {
        return new TokenInterceptor();
    }

    /**
     * 添加Web项目的拦截器
     */
    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        registry.addInterceptor(sessionInterceptor()).addPathPatterns("/**")
                .excludePathPatterns("/login/**", "/mp/**", "/summary/**")
                .excludePathPatterns("/*.txt", "/swagger*/**", "/webjars/**", "/html/**", "/check/**");
                /*
                * , "/css/**",
                * "/images/**",
                * "/js/**",
                * "/fonts/**"
                */
        // 放行登录页，登陆操作，静态资源
    }

    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        // 解决静态资源无法访问
        registry.addResourceHandler("/html/**")
                .addResourceLocations("classpath:/html/");
        // 解决swagger无法访问
        registry.addResourceHandler("/swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        // 解决swagger的js文件无法访问
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

    }
}