package com.spup.mp.entity.datacube;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.time.LocalDate;

@Data
public class Article {
    private LocalDate sendDate;
    @SerializedName("ref_date")
    private LocalDate refDate;
    @SerializedName("msgid")
    private String msgId;
    @SerializedName("title")
    private String title;
    @SerializedName("total_read_user")
    private int totalReadUser;
    @SerializedName("total_read_count")
    private int totalReadCount;
    @SerializedName("total_share_user")
    private int totalShareUser;
    @SerializedName("total_share_count")
    private int totalShareCount;
    @SerializedName("add_to_fav_user")
    private int addToFavUser;
    @SerializedName("add_to_fav_count")
    private int addToFavCount;
}
