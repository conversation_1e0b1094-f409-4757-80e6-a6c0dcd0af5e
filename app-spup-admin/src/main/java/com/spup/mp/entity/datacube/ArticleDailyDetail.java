package com.spup.mp.entity.datacube;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class ArticleDailyDetail {
    
    @SerializedName("ref_date")
    private String refDate;
    @SerializedName("msgid")
    private String msgId;
    @SerializedName("title")
    private String title;
    @SerializedName("int_page_read_user")
    private Integer intPageReadUser;
    @SerializedName("int_page_read_count")
    private Integer intPageReadCount;
    @SerializedName("ori_page_read_user")
    private Integer oriPageReadUser;
    @SerializedName("ori_page_read_count")
    private Integer oriPageReadCount;
    @SerializedName("share_user")
    private Integer shareUser;
    @SerializedName("share_count")
    private Integer shareCount;
    @SerializedName("add_to_fav_user")
    private Integer addToFavUser;
    @SerializedName("add_to_fav_count")
    private Integer addToFavCount;

}
