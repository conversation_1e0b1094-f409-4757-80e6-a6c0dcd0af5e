package com.spup.mp.service;


import com.spup.mp.entity.datacube.Article;
import com.spup.mp.entity.datacube.ArticleDailyDetail;
import com.spup.mp.entity.datacube.UserSummary;

import java.time.LocalDate;
import java.util.List;

public interface IMpDatacubeSerivce {
    List<ArticleDailyDetail> getArticleSummary(LocalDate date) throws Exception;
    List<Article> getArticleTotal(LocalDate date) throws Exception;
    Integer getUserCumulate(LocalDate date) throws Exception;
    List<UserSummary> getUserSummary(LocalDate date) throws Exception;
}
