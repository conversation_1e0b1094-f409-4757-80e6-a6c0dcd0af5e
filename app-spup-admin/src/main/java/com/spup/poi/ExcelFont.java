package com.spup.poi;

import org.apache.poi.ss.usermodel.Font;


/**
 * 支持excel2003和2007
 * <AUTHOR>
 * @version 2.0
 */

public class ExcelFont {

    private ExcelWriter excelWriter;
    public ExcelFont( ExcelWriter excelWriter){
        this.excelWriter = excelWriter;
    }

    public  Font getTitleFont() {
         Font font = excelWriter.getWorkBook() .createFont() ;
         font.setBold(true);
         font.setFontHeightInPoints((short)20) ;
         return font;
    }

    public Font  getNormalFont() {
        Font font = excelWriter.getWorkBook() .createFont() ;
        font.setBold(true);
        return font;
    }

    public Font getDefaultFont(){
        return  excelWriter.getWorkBook() .createFont() ;
    }

    public Font cloneFont(Font source){
        Font font = this.excelWriter .getWorkBook() .createFont() ;
        font.setBold(source.getBold() ) ;
        font.setColor(source.getColor() ) ;
        font.setFontHeight(source.getFontHeight() ) ;
        font.setFontHeightInPoints(source.getFontHeightInPoints() ) ;
        font.setItalic(source.getItalic() ) ;
        font.setStrikeout(source.getStrikeout() ) ;
        font.setTypeOffset(source.getTypeOffset() ) ;
        font.setUnderline(source.getUnderline() ) ;

        return font;
    }


}
