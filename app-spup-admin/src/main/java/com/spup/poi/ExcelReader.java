package com.spup.poi;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.ss.usermodel.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

public class ExcelReader {
	private static Log log = LogFactory.getLog(ExcelReader.class);;
	private Workbook workbook;
	private Sheet aSheet;
	private Map<String,String> columnCommentMap;
	private List<Map<String,String>> rowList;
	private int rowIndex;

	public ExcelReader(File file) {
		try {
			workbook = WorkbookFactory.create(new FileInputStream(file));
			aSheet = workbook.getSheetAt(0);
			columnCommentMap = new HashMap<String,String>();
			rowList = new ArrayList<Map<String,String>>();
			rowIndex = 0;
		} catch (FileNotFoundException e) {
			log.debug("文件没有找到");
		} catch (IOException e) {
			log.debug("文件已损坏");
		} catch (Exception e) {
			log.debug("",e);
		}
	}

	public void readColumnComment() {
		DecimalFormat df = new DecimalFormat("#.#");
		Row aRow = aSheet.getRow(rowIndex++);
		columnCommentMap.clear();
		for (int cellNumOfRow = 0; cellNumOfRow <= aRow.getLastCellNum(); cellNumOfRow++) {

			if (null != aRow.getCell(cellNumOfRow)) {
				Cell aCell = aRow.getCell(cellNumOfRow);
				
				CellType cellType = aCell.getCellType();
				String strCell = null;
				switch (cellType) {
				case NUMERIC:// Numeric
					if(DateUtil.isCellDateFormatted(aCell)){ //日期格式
						double d = aCell.getNumericCellValue();
						SimpleDateFormat sdf = null;
						if(d<1){ // HH:mm:ss 没有年月日
							sdf = new SimpleDateFormat("HH:mm:ss");
						} else if(d==Math.floor(d)){
							sdf = new SimpleDateFormat("yyyy-MM-dd");
						} else {
							sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						}
		                Date date = DateUtil.getJavaDate(d);
		                log.debug(sdf.format(date));
		                strCell = sdf.format(date);
					} else {
						strCell = df.format(aCell.getNumericCellValue());
						log.debug(strCell);
					}
					columnCommentMap.put(String.valueOf(cellNumOfRow), strCell);
					break;
				case STRING:// String
					strCell = aCell.getStringCellValue();
					columnCommentMap.put(String.valueOf(cellNumOfRow), strCell);
					log.debug("String : " + strCell);

					break;
				case FORMULA:
					break;
				case BOOLEAN:
					break;
				case ERROR:
					break;
				case BLANK:
					break;
				default:
					
				}

			}

		}
	}

	public void readRowData() {
		DecimalFormat df = new DecimalFormat("#.#");
		for(int rowNumOfSheet = rowIndex; rowNumOfSheet <= aSheet.getLastRowNum(); rowNumOfSheet++){
			Row aRow = aSheet.getRow(rowNumOfSheet);
			//log.debug("最大列数："+aRow.getPhysicalNumberOfCells());
			if(aRow==null) continue;
			
			Map<String,String> cellDataMap = new HashMap<String,String>();
			rowList.add(cellDataMap);
			
			for (int cellNumOfRow = 0; cellNumOfRow <= aRow.getLastCellNum(); cellNumOfRow++) {

				if (null != aRow.getCell(cellNumOfRow)) {
					Cell aCell = aRow.getCell(cellNumOfRow);

					CellType cellType = aCell.getCellType();
					String strCell = null;
					switch (cellType) {
					case NUMERIC:// Numeric
						if(DateUtil.isCellDateFormatted(aCell)){ //日期格式
							double d = aCell.getNumericCellValue();
							SimpleDateFormat sdf = null;
							if(d<1){ // HH:mm:ss 没有年月日
								sdf = new SimpleDateFormat("HH:mm:ss");
							} else if(d==Math.floor(d)){
								sdf = new SimpleDateFormat("yyyy-MM-dd");
							} else {
								sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							}
			                Date date = DateUtil.getJavaDate(d);
			                log.debug(sdf.format(date));
			                strCell = sdf.format(date);
						} else {
							strCell = df.format(aCell.getNumericCellValue());
							log.debug(strCell);
						}
						cellDataMap.put(String.valueOf(cellNumOfRow), strCell);
						break;
					case STRING:// String
						strCell = aCell.getStringCellValue();
						cellDataMap.put(String.valueOf(cellNumOfRow), strCell);
						//log.debug("String : " + strCell);
						break;
					case FORMULA:
						strCell = aCell.getCellFormula();
						//这里只考虑了常用的公式计算值,没有考虑布尔值和日期格式值
						try {
							strCell = String.valueOf(aCell.getNumericCellValue());
						} catch (IllegalStateException e) {
							strCell = String.valueOf(aCell.getRichStringCellValue());
						}
						cellDataMap.put(String.valueOf(cellNumOfRow), strCell);
						
						//log.debug("formula : " + strCell);
						break;
					case BOOLEAN:
						strCell = String.valueOf(aCell.getBooleanCellValue());
						cellDataMap.put(String.valueOf(cellNumOfRow), strCell);
						//log.debug("BOOLEAN : " + strCell);
						break;
					case ERROR:
						break;
					case BLANK:
						cellDataMap.put(String.valueOf(cellNumOfRow), "");
						break;
					default:
					}

				}

			}
			
		}
		log.debug("row size : " + rowList.size());
		
	}
	
	public Map<String, String> getColumnCommentMap() {
		return columnCommentMap;
	}

	public void setColumnCommentMap(Map<String, String> columnCommentMap) {
		this.columnCommentMap = columnCommentMap;
	}

	public List<Map<String, String>> getRowList() {
		return rowList;
	}

	public void setRowList(List<Map<String, String>> rowList) {
		this.rowList = rowList;
	}

	public static void main(String[] args) {
		ExcelReader poi = new ExcelReader(new File("C:\\Users\\<USER>\\Desktop\\aa.xlsx"));
		// poi.CreateExcel();
		poi.readColumnComment();
		poi.readRowData();
		
		List<Map<String, String>> list = poi.getRowList();
		
		for(int i=0;i<list.size();i++){
			Map<String, String> rowData = list.get(i);
			//JSONObject obj = new JSONObject();
			Iterator<String> it_k = rowData.keySet().iterator();
			while(it_k.hasNext()){
				String key = it_k.next();
				String value = rowData.get(key);
				System.out.print(value+"\t");
			}
			System.out.print("\n");
		}
		/*Scanner in = new Scanner(System.in);
		while(true){
			System.out.println("input command :");
			String command = in.nextLine();
			if(command.equalsIgnoreCase("quit")) break;
			else if(command.equalsIgnoreCase("edit")) poi.editColumnName();
			else if(command.equalsIgnoreCase("print")) poi.printColumnName();
		}
		System.out.println("the End");*/
	}
	
}
