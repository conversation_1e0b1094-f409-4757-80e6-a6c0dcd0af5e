package com.spup.poi;


import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Calendar;
import java.util.Date;


import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


/**
 * 支持excel2003和2007
 * <AUTHOR>
 * @version 2.0
 */

public class ExcelWriter {

    //field
    private Workbook wb ;
    private Sheet sheet;
    private ExcelStyle style;
    private ExcelFont font;
    private int defaultRowHeight = 20;

    //get set method
    public Workbook getWorkBook(){ return wb; }
    public Sheet getSheet(){ return sheet;}
    public ExcelStyle getExcelStyle(){ return this.style ;}
    public ExcelFont getExcelFont(){ return this.font ; }
    public int getDefaultRowHeight(){ return this.defaultRowHeight ;}
    public void setDefaultRowHeight(int height){ this.defaultRowHeight = height; }

    public int getDefaultColumnWidth(){
        return this.getSheet() .getDefaultColumnWidth()  ;
    }
    public void setDefaultColumnWidth(int width){
        this.getSheet().setDefaultColumnWidth(width) ;
    }
    /**
     * 默认输出文件为exel2003
     */
    public ExcelWriter() {
        wb = new HSSFWorkbook();
        sheet = wb.createSheet() ;
        style = new ExcelStyle(this);
        font = new ExcelFont(this);
    }

    public ExcelWriter(boolean isXlsx) {
        if(isXlsx){
            wb = new XSSFWorkbook();
        } else {
            wb = new HSSFWorkbook();
        }
        sheet = wb.createSheet() ;
        style = new ExcelStyle(this);
        font = new ExcelFont(this);
    }
    /**
     * 根据文件创建工作簿,默认只去第一个sheet
     * @param sourceFile
     * @throws Exception
     */
    public ExcelWriter(File sourceFile) throws Exception{
        createByFile(sourceFile,0);
    }
    /**
     * 根据文件创建工作薄,根据sheetIndex取得sheet表格
     * @param sourceFile
     * @param sheetIndex
     * @throws Exception
     */
    public ExcelWriter(File sourceFile,int sheetIndex) throws Exception{
        createByFile(sourceFile,sheetIndex);
    }
    /**
     * 根据文件路径创建工作薄,默认取第一个sheet
     * @param sourceFile
     * @throws Exception
     */
    public ExcelWriter(String sourceFile) throws Exception{
        File file = new File(sourceFile);
        createByFile(file,0);
    }

    /**
     * 根据文件路径创建工作薄,根据sheetIndex取得sheet表格
     * @param sourceFile
     * @param sheetIndex
     * @throws Exception
     */
    public ExcelWriter(String sourceFile,int sheetIndex) throws Exception{
        File file = new File(sourceFile);
        createByFile(file,sheetIndex);
    }
    private void createByFile(File sourceFile,int sheetIndex) throws InvalidFormatException, FileNotFoundException, IOException{
        wb = (Workbook) WorkbookFactory.create(new FileInputStream(sourceFile));
        sheet = wb.getSheetAt(sheetIndex);
        style = new ExcelStyle(this);
        font = new ExcelFont(this);
    }

    public Row addTitle(int rowIndex,String title,int columnFrom ,int columnTo){
        Row row = sheet.createRow(rowIndex) ;
        Cell cell = row.createCell(columnFrom) ;
        cell.setCellValue(title) ;
        cell.setCellStyle(this.getExcelStyle() .getTitleStyle()   ) ;
        CellRangeAddress region = new CellRangeAddress(rowIndex,rowIndex,columnFrom,columnTo);
        sheet.addMergedRegion(region) ;
        return row ;
    }//addTitle//




    /**
     * ???????,values?е????????????е?????????
     * @param rowIndex ?к?
     * @param columnFrom ????к??????У?
     * @param values ???????
     * @param style ??????
     * @return Row
     * */
    public Row addRow(int rowIndex ,int columnFrom,Object[] values,CellStyle style){
        Row row = sheet.createRow(rowIndex ) ;
        row.setHeightInPoints(this.getDefaultRowHeight() ) ;
        for(int i=0;i<values.length ;i++){
            Cell cell = row.createCell(columnFrom) ;
            CellStyle cloneStyle = style;//this.getExcelStyle() .cloneStyle(style) ;
            cell.setCellStyle(cloneStyle) ;


            setCellValue(values[i], cell);
            columnFrom ++;
        }
        return row ;
    }//addRow//

    /**
     *
     * @param rowIndex
     * @param columnFrom
     * @param values
     * @param ownerColumns
     * @param style
     * @return
     */
    public Row addRow(int rowIndex,int columnFrom,Object[] values,int[] ownerColumns,CellStyle style){
        Row row = sheet.createRow((short)rowIndex) ;
        row.setHeightInPoints(this.getDefaultRowHeight() ) ;
        for(int i=0 ;i<values.length ;i++){
            int tempColumnFrom = columnFrom;
            int tempColumnTo = tempColumnFrom+ownerColumns[i]-1;
            CellRangeAddress region = new CellRangeAddress(rowIndex,rowIndex,tempColumnFrom,tempColumnTo);
            sheet.addMergedRegion(region) ;
            for(int j=tempColumnFrom;j<=tempColumnTo;j++){
                Cell cell = row.createCell(j) ;
                cell = row.createCell(j);
                CellStyle cloneStyle = style;// this.getExcelStyle() .cloneStyle(style) ;
                cell.setCellStyle(cloneStyle);

                setCellValue(values[i], cell);
            }
            columnFrom = columnFrom+ownerColumns[i];
        }
        return row;
    }//addRow//


    /**
     *
     * @param rowFrom
     * @param rowTo
     * @param columnFrom
     * @param columnTo
     * @param value
     * @param style
     * @return
     */
    public Cell addCell(int rowFrom,int rowTo,int columnFrom ,int columnTo,Object value,CellStyle style){
//    	???????????????
        Row row = getRow(rowFrom);
        Cell cell = row.createCell(columnFrom);
        setCellValue(value, cell);

        //????????
        CellRangeAddress region = new CellRangeAddress(rowFrom,rowTo,columnFrom,columnTo);

        sheet.addMergedRegion(region);

        //???????
        CellStyle cloneStyle = style;
        for(int i=rowFrom;i<=rowTo;i++){
            Row row_temp = getRow(i);
            for(int j=columnFrom;j<=columnTo;j++){
                Cell cell_temp = row_temp.getCell(j);
                if(cell_temp ==null ){
                    cell_temp = row_temp.createCell(j);
                }
                cell_temp.setCellStyle(cloneStyle);
            }
        }

        return cell;

    }//addCell//

    /**
     *
     * @param rowIndex
     * @return
     */
    public Row getRow(int rowIndex) {
        Row row = sheet.getRow(rowIndex);
        if(row==null){
            row = sheet.createRow(rowIndex);
            row.setHeightInPoints(this.getDefaultRowHeight()) ;
        }
        return row;
    }


    /**
     *
     * @param rowIndex
     * @param columnIndex
     * @param value
     * @param style
     * @return
     */

    public Cell addCell(int rowIndex,int columnIndex,Object value,CellStyle style){
        Row row = sheet.getRow((short)rowIndex) ;
        row.setHeightInPoints(this.getDefaultRowHeight()  ) ;
        Cell cell = row.createCell(columnIndex) ;
        cell.setCellStyle(style) ;
        setCellValue(value, cell);
        return cell;
    }//addCell//


    /**
     *
     * @param rowIndex
     * @param columnFrom
     * @param columnTo
     * @param border
     */
    public void modifyRowTopBorder(int rowIndex,int columnFrom,int columnTo,short border){
        Row row = sheet.getRow(rowIndex) ;
        for(int i=columnFrom;i<=columnTo;i++){
            row.getCell(i).getCellStyle().setBorderTop(BorderStyle.valueOf(border));
        }//for//
    }

    /**
     *
     * @param rowIndex
     * @param columnFrom
     * @param columnTo
     * @param border
     */
    public void modifyRowBottomBorder(int rowIndex,int columnFrom,int columnTo,short border){
        Row row = sheet.getRow(rowIndex) ;
        for(int i=columnFrom;i<=columnTo;i++){
            row.getCell(i).getCellStyle().setBorderBottom(BorderStyle.valueOf(border));
        }//for//
    }

    /**
     *
     * @param columnIndex
     * @param rowFrom
     * @param rowTo
     * @param border
     */
    public void modifyColumnLeftBorder(int columnIndex,int rowFrom,int rowTo,short border){
        for(int i=rowFrom;i<=rowTo;i++){
            Row row = sheet.getRow(i) ;
            row.getCell(columnIndex).getCellStyle().setBorderLeft(BorderStyle.valueOf(border));
        }//for//
    }


    /**
     *
     * @param columnIndex
     * @param rowFrom
     * @param rowTo
     * @param border
     */
    public void modifyColumnRightBorder(int columnIndex,int rowFrom,int rowTo,short border){
        for(int i=rowFrom;i<=rowTo;i++){
            Row row = sheet.getRow(i) ;
            row.getCell(columnIndex).getCellStyle().setBorderRight(BorderStyle.valueOf(border));
        }//for//
    }

    /**
     *
     * @param columnIndex
     * @param columnWidth
     */
    public void setColumnWidth(int columnIndex,int columnWidth){
        sheet.setColumnWidth(columnIndex,(short)(columnWidth*256)) ;
    }

    /**
     *
     * @param rowIndex
     * @param rowHeight
     */
    public void setRowHeight(int rowIndex,float rowHeight){
        sheet.getRow(rowIndex).setHeightInPoints(rowHeight)  ;
    }

    /**
     *
     * @param rowIndex
     * @param columnIndex
     * @return
     */
    public CellStyle getCellStyle(int rowIndex,int columnIndex){
        return sheet.getRow(rowIndex).getCell(columnIndex).getCellStyle()   ;
    }


    /**
     *
     * @param fileName
     * @throws IOException
     */
    public void saveAs(String fileName)throws IOException{
        FileOutputStream file = new FileOutputStream(fileName) ;
        wb.write(file );
        file.flush() ;
        file.close() ;
    }

    public void output(OutputStream os)throws IOException{
        wb.write(os);
        os.flush();
    }


    private void setCellValue(Object value, Cell cell) {
        if(value instanceof Integer ){
            cell.setCellValue(((Integer)value).doubleValue());
        } else if(value instanceof Long){
            cell.setCellValue(((Long)value).doubleValue());
        } else if(value instanceof Short){

            cell.setCellValue(((Short)value).doubleValue());
        } else if(value instanceof Float){

            cell.setCellValue(((Float)value).floatValue());
        } else if(value instanceof Double ){

            cell.setCellValue(((Double)value).doubleValue());
        } else if(value instanceof String) {

            cell.setCellValue((String)value);
        } else if(value instanceof Calendar) {

            cell.setCellValue((Calendar)value);
        } else if(value instanceof Date){

            cell.setCellValue((Date)value);
        } else {
            //????
            cell.setCellValue(value.toString());
        }
    }

    public void createNewSheet(){
        sheet = wb.createSheet();
    }

    public void createNewSheet(String sheetName){
        sheet = wb.createSheet(sheetName);
    }

    public static void main(String[] a)throws Exception{

    }
}
