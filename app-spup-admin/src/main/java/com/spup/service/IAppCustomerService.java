package com.spup.service;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.authority.AppCustomer;
import com.spup.dto.CustomerListRequest;
import org.springframework.data.domain.Page;

public interface IAppCustomerService {
    AppCustomer get(String unionid);

    AppCustomer save(String unionid,String openid);

    AppCustomer save(String unionid, String openid,String userName,String avatar,byte gender);

    CommonResult<?> update(AppCustomer customer);

    Page<AppCustomer> getPageList(CustomerListRequest param);
}
