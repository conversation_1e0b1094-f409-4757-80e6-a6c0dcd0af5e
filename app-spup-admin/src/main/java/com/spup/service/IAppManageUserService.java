package com.spup.service;

import com.spup.db.entity.authority.AppManageUser;
import com.spup.dto.ManagerUserListRequest;
import org.springframework.data.domain.Page;


public interface IAppManageUserService {
    Page<AppManageUser> getListByPage(ManagerUserListRequest listParam);
    AppManageUser create(AppManageUser appManageUser);
    AppManageUser update(AppManageUser appManageUse);
    int delete(long id);
    AppManageUser getUserByUnionid(String unionid);
    AppManageUser view(long id);
}
