package com.spup.service;

import com.spup.db.entity.AppSurroundingGoods;
import com.spup.dto.GoodsListRequest;
import org.springframework.data.domain.Page;

public interface IAppSurroundingGoodsService {
    Page<AppSurroundingGoods> getListByPage(GoodsListRequest listParam);
    AppSurroundingGoods view(long id);
    AppSurroundingGoods create(AppSurroundingGoods goods,String unionid);
    AppSurroundingGoods update(AppSurroundingGoods appSurroundingGoods,String openId);
    int delete(long id, String openId);
}
