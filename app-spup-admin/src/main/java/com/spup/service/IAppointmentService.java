package com.spup.service;

import com.spup.db.entity.ExhibitionInfo;
import com.spup.db.entity.RoundConfig;

import java.time.LocalDate;
import java.util.Optional;

public interface IAppointmentService {
    Optional<ExhibitionInfo> getExhibitionById(Long id);
    Optional<RoundConfig> getRoundByRoundDateAndExhibitionId(String exhibitionId, LocalDate date);
    Boolean isAvailableByRound(String exhibitionId, LocalDate date);
    Boolean isAvailableByHoliday(String exhibitionId, LocalDate date);
}
