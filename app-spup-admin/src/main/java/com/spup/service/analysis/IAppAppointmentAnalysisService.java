package com.spup.service.analysis;

import com.spup.db.entity.appointment.AppAppointmentAnalysis;
import com.spup.dto.AnalysisListRequest;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

public interface IAppAppointmentAnalysisService {
    Map<String,Object> getAnaDataFromRecord(String yyyyMMdd);
    AppAppointmentAnalysis save(Map<String,Object> map);
    List<AppAppointmentAnalysis> getAnaDataByDate(String startDate, String endDate);
    Page<AppAppointmentAnalysis> listByPage(AnalysisListRequest listParam);
}
