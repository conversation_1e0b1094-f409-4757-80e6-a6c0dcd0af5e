package com.spup.service.analysis;

import com.spup.db.entity.mp.MpDatacubeUserSummary;
import com.spup.dto.DateQueryRequest;
import com.spup.mp.entity.datacube.UserSummary;
import org.springframework.data.domain.Page;

import java.time.LocalDate;
import java.util.List;

public interface IMpDatacubeUserSummaryService {
    Long getUserTotal();
    Long getUserTotal(LocalDate start,LocalDate end);
    MpDatacubeUserSummary findTopByOrderByCreateTimeDesc();
    Page<MpDatacubeUserSummary> getListByPage(DateQueryRequest queryParam);
    void save(LocalDate date,Integer total,List<UserSummary> userSummaryList);

}
