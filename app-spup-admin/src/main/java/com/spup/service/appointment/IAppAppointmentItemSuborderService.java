package com.spup.service.appointment;


import com.spup.db.entity.appointment.AppAppointmentItemSuborder;

import java.time.LocalDate;
import java.util.List;

public interface IAppAppointmentItemSuborderService {
    long countByStatus(Short suborderStatus);
    long countByStatus(Short suborderStatus,LocalDate start,LocalDate end);

    List<AppAppointmentItemSuborder> findByDateBetween(LocalDate startDate, LocalDate endDate);

}
