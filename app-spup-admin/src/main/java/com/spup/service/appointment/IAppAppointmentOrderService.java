package com.spup.service.appointment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.appointment.AppAppointmentOrder;
import com.spup.dto.AppAppointmentOrderRequest;
import com.spup.service.IOrderService;

import java.util.List;


public interface IAppAppointmentOrderService extends IOrderService {
    //对外接口
    CommonResult<?> save(AppAppointmentOrderRequest orderRequest,String unionid) throws JsonProcessingException;
    CommonResult<?> getList(String unionid);
    CommonResult<?> cancel(String orderNo,String unionid);
    CommonResult<?> breaked(String orderNo,String unionid);
    CommonResult<?> delete(String orderNo);
    //内部接口
    List<AppAppointmentOrder> getListByUnionid(String unionid);
    AppAppointmentOrder getOrderByOrderNo(String orderNo);
}
