package com.spup.service.appointment;

import com.huangdou.commons.api.CommonResult;
import com.spup.dto.AppAppointmentOrderTemporaryExhibitionRequest;
import com.spup.service.IOrderService;

import java.io.IOException;

public interface IAppAppointmentOrderTemporaryExhibitionService extends IOrderService {
    // 对外接口
    CommonResult<?> save(AppAppointmentOrderTemporaryExhibitionRequest orderRequest, String unionid) throws IOException;

    CommonResult<?> delete(String orderNo);
    // 内部接口
}
