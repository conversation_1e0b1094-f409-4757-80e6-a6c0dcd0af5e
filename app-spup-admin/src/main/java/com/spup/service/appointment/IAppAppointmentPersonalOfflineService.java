package com.spup.service.appointment;

import com.spup.db.entity.appointment.AppAppointmentPersonalOffline;
import com.spup.dto.PersonalOfflineListRequest;
import org.springframework.data.domain.Page;

public interface IAppAppointmentPersonalOfflineService {
    Page<AppAppointmentPersonalOffline> getList(PersonalOfflineListRequest listParam);
    AppAppointmentPersonalOffline save(AppAppointmentPersonalOffline offline);
    AppAppointmentPersonalOffline view(Long id);
    int delete(Long id);
}
