package com.spup.service.appointment;

import com.spup.db.entity.appointment.AppAppointmentTeamOffline;
import com.spup.dto.DateQueryRequest;
import org.springframework.data.domain.Page;

public interface IAppAppointmentTeamOfflineService {
    Page<AppAppointmentTeamOffline> getList(DateQueryRequest queryParam);
    AppAppointmentTeamOffline save(AppAppointmentTeamOffline offline);
    AppAppointmentTeamOffline view(Long id);
    int delete(Long id);
}
