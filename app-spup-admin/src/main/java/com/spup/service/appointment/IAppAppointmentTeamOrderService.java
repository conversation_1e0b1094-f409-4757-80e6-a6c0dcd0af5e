package com.spup.service.appointment;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.appointment.AppAppointmentTeamOrder;
import com.spup.dto.AppTeamOrderListRequest;
import com.spup.service.IOrderService;
import org.springframework.data.domain.Page;

import java.util.List;


public interface IAppAppointmentTeamOrderService extends IOrderService {
    //对外接口
    CommonResult<?> delete(String orderNo,String unionid);
    List<AppAppointmentTeamOrder> getTeamOrderByDate(String startDate, String endDate);
    AppAppointmentTeamOrder update(AppAppointmentTeamOrder modifyOrder);

    AppAppointmentTeamOrder save(AppAppointmentTeamOrder teamOrder);
    AppAppointmentTeamOrder view(Long id);
    Page<AppAppointmentTeamOrder> getList(AppTeamOrderListRequest queryParam);
}
