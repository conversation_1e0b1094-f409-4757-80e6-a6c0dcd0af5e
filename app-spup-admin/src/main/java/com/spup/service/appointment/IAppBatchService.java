package com.spup.service.appointment;



import com.spup.db.entity.appointment.AppBatch;

import java.util.List;
import java.util.Map;

public interface IAppBatchService {
    AppBatch getByNo(String batchNo, Byte batchCategory);
    Map<String,List<AppBatch>> getListByDate(Byte category, String startDate, String endDate);

    List<AppBatch> getListByDate(Byte category, String date);
    AppBatch save(AppBatch batch);
    AppBatch update(AppBatch batch);
    AppBatch updateRemaining(String batchNo,Byte batchCategory,int updateNum);

}
