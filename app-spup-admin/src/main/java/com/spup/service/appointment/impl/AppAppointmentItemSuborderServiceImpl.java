package com.spup.service.appointment.impl;

import com.spup.db.dao.appointment.AppAppointmentItemSuborderDao;
import com.spup.db.entity.appointment.AppAppointmentItemSuborder;
import com.spup.service.appointment.IAppAppointmentItemSuborderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class AppAppointmentItemSuborderServiceImpl implements IAppAppointmentItemSuborderService {
    @Resource
    private AppAppointmentItemSuborderDao suborderDao;
    @Override
    public long countByStatus(Short suborderStatus) {
        return suborderDao.countBySuborderStatus(suborderStatus);
    }
    @Override
    public long countByStatus(Short suborderStatus,LocalDate start,LocalDate end) {
        return suborderDao.countBySuborderStatusAndBatchDateBetween(suborderStatus,
                                        start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                                        end.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }
    @Override
    public List<AppAppointmentItemSuborder> findByDateBetween(LocalDate startDate, LocalDate endDate) {
        List<AppAppointmentItemSuborder> suborderList =
                suborderDao.findByBatchDateBetween(
                        startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                        endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        return suborderList;
    }
}
