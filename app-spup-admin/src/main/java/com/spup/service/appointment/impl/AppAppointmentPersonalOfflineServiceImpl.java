package com.spup.service.appointment.impl;

import com.spup.db.dao.appointment.AppAppointmentPersonalOfflineDao;
import com.spup.db.entity.appointment.AppAppointmentPersonalOffline;
import com.spup.dto.PersonalOfflineListRequest;
import com.spup.service.appointment.IAppAppointmentPersonalOfflineService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class AppAppointmentPersonalOfflineServiceImpl implements IAppAppointmentPersonalOfflineService {
    @Resource
    private AppAppointmentPersonalOfflineDao appAppointmentPersonalOfflineDao;

    public Page<AppAppointmentPersonalOffline> getList(PersonalOfflineListRequest listParam) {
        // 调用分页插件,访问第一页，每页2条数据
        Pageable pageable = PageRequest.of(listParam.getPageNum() - 1, listParam.getPageSize());
        // 从数据库查询
        Specification<AppAppointmentPersonalOffline> spec = new Specification<AppAppointmentPersonalOffline>() {
            // Predicate:封装了 单个的查询条件
            /**
             * Root<Users> root:查询对象的属性的封装。
             * CriteriaQuery<?> query：封装了我们要执行的查询中的各个部分的信息，select from order by
             * CriteriaBuilder cb:查询条件的构造器。定义不同的查询条件
             */
            @Override
            public Predicate toPredicate(@NonNull Root<AppAppointmentPersonalOffline> root,
                    @NonNull CriteriaQuery<?> query, @NonNull CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();
                if (listParam.getStartDate() != null) {
                    list.add(cb.greaterThanOrEqualTo(root.get("visitDate"), listParam.getStartDate()));// 筛选
                }
                if (listParam.getEndDate() != null) {
                    list.add(cb.lessThanOrEqualTo(root.get("visitDate"), listParam.getEndDate()));// 筛选
                }
                query.orderBy(cb.desc(root.get("visitDate")));// 排序
                Predicate[] arr = new Predicate[list.size()];
                return cb.and(list.toArray(arr));
            }
        };
        Page<AppAppointmentPersonalOffline> all = appAppointmentPersonalOfflineDao.findAll(spec, pageable);
        return all;
    }

    public AppAppointmentPersonalOffline save(AppAppointmentPersonalOffline offline) {
        return appAppointmentPersonalOfflineDao.save(offline);
    }

    public AppAppointmentPersonalOffline view(Long id) {
        Optional<AppAppointmentPersonalOffline> byId = appAppointmentPersonalOfflineDao.findById(id);
        if (!byId.isPresent()) {
            return null;
        }
        return byId.get();
    }

    @Override
    public int delete(Long id) {
        appAppointmentPersonalOfflineDao.deleteById(id);
        return 1;
    }
}
