package com.spup.service.appointment.impl;

import com.spup.db.dao.appointment.AppAppointmentSuborderDao;
import com.spup.db.entity.appointment.AppAppointmentSuborder;
import com.spup.dto.SuborderQueryRequest;
import com.spup.service.appointment.IAppAppointmentSuborderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class AppAppointmentSuborderServiceImpl implements IAppAppointmentSuborderService {
    @Resource
    private AppAppointmentSuborderDao suborderDao;
    @Override
    public long countByStatus(SuborderQueryRequest queryParam) {
        return suborderDao.countBySuborderStatusAndBatchDateBetween(queryParam.getSuborderStatus(),
                                               queryParam.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                                               queryParam.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }

    @Override
    public List<AppAppointmentSuborder> findByDateBetween(LocalDate startDate, LocalDate endDate) {
        List<AppAppointmentSuborder> suborderList =
                suborderDao.findByBatchDateBetween(
                        startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        return suborderList;
    }
}
