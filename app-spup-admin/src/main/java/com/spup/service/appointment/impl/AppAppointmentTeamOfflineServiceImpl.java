package com.spup.service.appointment.impl;

import com.huangdou.commons.utils.NumberGenerator;
import com.spup.db.dao.appointment.AppAppointmentTeamOfflineDao;
import com.spup.db.entity.appointment.AppAppointmentTeamOffline;
import com.spup.dto.DateQueryRequest;
import com.spup.service.appointment.IAppAppointmentTeamOfflineService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class AppAppointmentTeamOfflineServiceImpl implements IAppAppointmentTeamOfflineService {
    @Resource
    private AppAppointmentTeamOfflineDao teamOfflineDao;
    @Override
    public Page<AppAppointmentTeamOffline> getList(DateQueryRequest queryParam) {
        Pageable pageable = PageRequest.of(queryParam.getPageNum()-1, queryParam.getPageSize());
        Specification<AppAppointmentTeamOffline> spec = (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();
            if(queryParam.getStartDate()!=null){
                list.add(cb.greaterThanOrEqualTo(root.get("visitDate"),queryParam.getStartDate()));//筛选
            }
            if(queryParam.getEndDate()!=null){
                list.add(cb.lessThanOrEqualTo(root.get("visitDate"),queryParam.getEndDate()));//筛选
            }
            query.orderBy(cb.desc(root.get("visitDate")));//排序
            Predicate[] arr = new Predicate[list.size()];
            return cb.and(list.toArray(arr));
        };
        Page<AppAppointmentTeamOffline> all = teamOfflineDao.findAll(spec, pageable);
        return all;
    }

    @Override
    public AppAppointmentTeamOffline save(AppAppointmentTeamOffline offline) {
        if(!StringUtils.hasLength(offline.getOrderNo())){
            offline.setOrderNo(createOrderNo());
        }
        return teamOfflineDao.save(offline);
    }

    @Override
    public AppAppointmentTeamOffline view(Long id) {
        Optional<AppAppointmentTeamOffline> teamOfflineOptional = teamOfflineDao.findById(id);
        if(!teamOfflineOptional.isPresent()){
            return null;
        }
        return teamOfflineOptional.get();
    }

    @Override
    public int delete(Long id) {
        return 0;
    }

    private String createOrderNo(){
        return "offline"+ NumberGenerator.getOrderNo();
    }
}
