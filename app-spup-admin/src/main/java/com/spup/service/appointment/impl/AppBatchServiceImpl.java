package com.spup.service.appointment.impl;

import com.spup.db.dao.appointment.AppBatchDao;
import com.spup.db.entity.appointment.AppBatch;
import com.spup.service.appointment.IAppBatchService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class AppBatchServiceImpl implements IAppBatchService {

    @Resource
    private AppBatchDao appBatchDao;

    @Override
    public AppBatch getByNo(String batchNo, Byte batchCategory){
        Optional<AppBatch> byBatchNoAndBatchCategory = appBatchDao.getByBatchNoAndBatchCategory(batchNo, batchCategory);
        if(!byBatchNoAndBatchCategory.isPresent()){
            return  null;
        }
        return byBatchNoAndBatchCategory.get();
    }

    @Override
    public Map<String,List<AppBatch>>  getListByDate(Byte category,String startDate,String endDate) {
        List<AppBatch> batches = appBatchDao.findByBatchCategoryAndBatchDateBetween(category,startDate,endDate);

        Map<String,List<AppBatch>> batchsMap = new LinkedHashMap<>();
        for (AppBatch batch : batches) {
            String date = batch.getBatchDate();
            List<AppBatch> batchList = batchsMap.get(date);
            if(batchList == null){
                batchList = new ArrayList<>();
                batchsMap.put(date,batchList);
            }
            batchList.add(batch);
        }
        return batchsMap ;
    }

    @Override
    public List<AppBatch> getListByDate(Byte category, String date) {
        //appBatchExample.setOrderByClause( " batch_date asc, batch_start_time asc " );
        List<AppBatch> batches = appBatchDao.findByBatchCategoryAndBatchDateBetween(category,date,date);

        return batches;
    }


    @Override
    public AppBatch save(AppBatch batch) {
        return appBatchDao.save(batch);
    }

    @Override
    public AppBatch update(AppBatch batch) {
        return appBatchDao.save(batch);
    }

    @Override
    public AppBatch updateRemaining(String batchNo, Byte batchCategory , int updateNum) {
        AppBatch batch2 = getByNo(batchNo, batchCategory);
        batch2.setTicketRemaining(batch2.getTicketRemaining()+updateNum>batch2.getTicketTotal()?batch2.getTicketTotal():(batch2.getTicketRemaining()+updateNum));
        return update(batch2);
    }


    // private void init(String startDate, String endDate,byte category,String[][] times,int ticketTotal){
    //     try {
    //         Calendar start_c = DateTimeUtil.getDateTime(startDate,DateTimeUtil.PATTERN_7);
    //         Calendar end_c = DateTimeUtil.getDateTime(endDate,DateTimeUtil.PATTERN_7);

    //         while(!start_c.after(end_c)){
    //             String startDateT = DateTimeUtil.getTime(DateTimeUtil.PATTERN_7,start_c);
    //             //删除原有的
    //             appBatchDao.deleteAll(appBatchDao.findByBatchCategoryAndBatchDateBetween(category,startDateT,startDateT));

    //             int week = start_c.get(Calendar.DAY_OF_WEEK);

    //             for (int i=0 ; i<times.length ; i++){
    //                 if(!"*".equals(times[i][2])){
    //                     String weekStr = times[i][2];
    //                     if(!String.valueOf(week).equals(weekStr)){
    //                         continue;
    //                     }
    //                 }
    //                 AppBatch batch = new AppBatch();
    //                 batch.setBatchNo(startDateT+times[i][0]);
    //                 batch.setBatchDate(startDateT);
    //                 batch.setBatchStartTime(times[i][0]);
    //                 batch.setBatchEndTime(times[i][1]);
    //                 batch.setTicketTotal(ticketTotal);
    //                 batch.setTicketRemaining(ticketTotal);

    //                 batch.setBatchCategory(category);
    //                 batch.setBatchStatus(BatchStatusEnum.CLOSED.getCode());

    //                 appBatchDao.save(batch);
    //             }
    //             start_c.add(Calendar.DAY_OF_MONTH,1);
    //         }

    //     } catch (ParseException e) {
    //         e.printStackTrace();
    //     }
    // }



    // public static void main(String[] args) {
    //     Calendar now = Calendar.getInstance();
    //     System.out.println(now.get(Calendar.DAY_OF_WEEK));
    // }
}
