package com.spup.service.appointment.impl;

import com.spup.db.dao.appointment.AppBatchSetDao;
import com.spup.db.dao.appointment.AppBatchSetDetailDao;
import com.spup.db.entity.appointment.AppBatchSet;
import com.spup.db.entity.appointment.AppBatchSetDetail;
import com.spup.dto.AppBatchSetDTO;
import com.spup.enums.BatchSetStatusEnum;
import com.spup.service.appointment.IAppBatchSetService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AppBatchSetServiceImpl implements IAppBatchSetService {
    @Resource
    private AppBatchSetDao appBatchSetDao;
    @Resource
    private AppBatchSetDetailDao appBatchSetDetailDao;

    public AppBatchSetDTO getLastSet(Byte batchCategory){
        List<AppBatchSet> sets = appBatchSetDao.findAll();
        sets = sets.stream()
                .filter(set -> set.getStatus()==BatchSetStatusEnum.WAIT_RUNNING.getCode()
                            || set.getStatus()==BatchSetStatusEnum.RUNNING.getCode())
                .sorted((s1,s2) -> s2.getCreateTime().compareTo(s1.getCreateTime()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(sets)){
            return null;
        }

        AppBatchSet set = sets.get(0);
        AppBatchSetDTO dto = new AppBatchSetDTO();
        BeanUtils.copyProperties(set,dto);

        List<AppBatchSetDetail> details = appBatchSetDetailDao.findByBatchSetId(set.getId());
        details.sort((s1,s2) -> s2.getBatchStartTime().compareTo(s1.getBatchStartTime()));
        dto.setDetails(details);
        return dto;
    }

    public AppBatchSet save(AppBatchSetDTO appBatchSetDTO,String openId){
        AppBatchSetDTO lastSetDTO = getLastSet(appBatchSetDTO.getBatchCategory());
        if(lastSetDTO !=null && lastSetDTO.getStatus().byteValue() == BatchSetStatusEnum.WAIT_RUNNING.getCode()){ //说明前一个规则还未生效，可以去除
            AppBatchSet lastSet = new AppBatchSet();
            BeanUtils.copyProperties(lastSetDTO,lastSet);
            lastSet.setStatus(BatchSetStatusEnum.CLOSED.getCode());
            appBatchSetDao.save(lastSet);
        }
        AppBatchSet set = new AppBatchSet();
        BeanUtils.copyProperties(appBatchSetDTO,set);
        set.setStatus(BatchSetStatusEnum.WAIT_RUNNING.getCode());
        appBatchSetDao.save(set);

        List<AppBatchSetDetail> details = appBatchSetDTO.getDetails();

        for(AppBatchSetDetail detail : details){
            detail.setBatchSetId(set.getId());
            detail.setBatchType(appBatchSetDTO.getBatchCategory());
            appBatchSetDetailDao.save(detail);
        }
        return set;
    }
}
