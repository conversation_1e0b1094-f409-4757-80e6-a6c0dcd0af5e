package com.spup.service.appointment.impl;


import com.spup.db.dao.RoundDao;
import com.spup.db.dao.appointment.AppBatchDao;
import com.spup.db.dao.appointment.AppWorkdayDao;
import com.spup.db.entity.RoundConfig;
import com.spup.db.entity.appointment.AppWorkday;
import com.spup.enums.WorkdayEnum;
import com.spup.service.IAppointmentService;
import com.spup.service.appointment.IAppWorkdayService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

@Service
public class AppWorkdayServiceImpl implements IAppWorkdayService {

    @Resource
    private AppWorkdayDao appWorkdayDao;
    @Resource
    private AppBatchDao appBatchDao;
    @Resource
    private RoundDao roundDao;

    @Resource
    private IAppointmentService iAppointmentService;

    @Override
    public List<AppWorkday> getListByDate(String startDate, String endDate) {
        return appWorkdayDao.findByDayBetween(startDate,endDate);
    }

    @Override
    public boolean isWorkDay(String day) {
        Optional<AppWorkday> workdayOptional = appWorkdayDao.getByDay(day);
        if(!workdayOptional.isPresent()){
            return false;
        }
        AppWorkday workday = workdayOptional.get();
        boolean isWorkDay =   workday.getIsWorkday().byteValue() == WorkdayEnum.OPEN_DAY.getCode();
        return isWorkDay;
    }

    @Override
    public int insert(LocalDate day) {
        DayOfWeek week = day.getDayOfWeek();
        AppWorkday workday = new AppWorkday();
        workday.setIsWorkday(week.getValue()>6?0:1);
        workday.setDay(day.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        workday.setDayRemark(week.getValue()>6?"馆休":"");
        appWorkdayDao.save(workday);
        return 0;
    }

    @Override
    public AppWorkday setWorkDay(String day, Integer status, String remark, String unionid) {
        Optional<AppWorkday> dayOpt = appWorkdayDao.getByDay(day);
        if(dayOpt.isPresent()) {
            AppWorkday workday = dayOpt.get();
            if(status.byteValue() == WorkdayEnum.OPEN_DAY.getCode()){
                workday.setDayRemark("");
            } else {
               if(!StringUtils.hasLength(remark)){
                   LocalDate date = LocalDate.parse(day,DateTimeFormatter.ofPattern("yyyyMMdd"));
                   Boolean availableByHoliday = iAppointmentService.isAvailableByHoliday("all",date);
                   if(!availableByHoliday){
                       workday.setDayRemark("闭馆");
                   }else{
                       workday.setDayRemark("已约满");
                   }
               } else {
                   workday.setDayRemark(remark);
               }
            }
            workday.setIsWorkday(status);
            return appWorkdayDao.save(workday);
        }
        return null;
    }

    /*@Override
    public AppWorkday setWorkDayOfTemp(String exhibitionNo,String day, Integer status, String remark, String unionid) {
        Optional<AppWorkday> dayOpt = appWorkdayDao.getByDay(day);
        if(dayOpt.isPresent()){
            AppWorkday workday = dayOpt.get();
            ObjectNode config = workday.getConfig();
            config.put(exhibitionNo,status);
            workday.setConfig(config);

            List<AppBatch> batchList = appBatchDao.findByBatchCategoryAndBatchDateBetween(BatchCategoryEnum.EXHIBITION_TEAM.getCode(), day, day);
            batchList.forEach(batch -> {
                batch.setBatchStatus(status.byteValue());
                appBatchDao.save(batch);
            });
            return appWorkdayDao.save(workday);
        } else {
            throw new RuntimeException("未开启该日期");
        }
    }*/
    @Override
    public AppWorkday setWorkDayOfTemp(String exhibitionNo,String day, Integer status, String remark, String unionid) {
        Optional<AppWorkday> dayOpt = appWorkdayDao.getByDay(day);
        if(dayOpt.isPresent()){
            AppWorkday workday = dayOpt.get();
            LocalDate date = LocalDate.parse(day,DateTimeFormatter.ofPattern("yyyyMMdd"));

            Optional<RoundConfig> configOpt = roundDao.findByExhibitionIdAndRoundDate(exhibitionNo, date);
            RoundConfig config;
            if(!configOpt.isPresent()){
                config = new RoundConfig();
                config.setExhibitionId(exhibitionNo);
                config.setRoundDate(date);
            } else {
                config = configOpt.get();
            }
            config.setRoundStatus(status==0? RoundConfig.RoundStatusEnum.close:RoundConfig.RoundStatusEnum.open);
            roundDao.save(config);

            workday.setIsWorkday(status);
            return workday;
        } else {
            throw new RuntimeException("未开启该日期");
        }
    }

    @Override
    public LocalDate getDate(LocalDate date, int plusDays,byte dayType) {
        int hasPlusDays = 0;
        while( hasPlusDays < plusDays ) {
            String yyyyMMdd = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            Optional<AppWorkday> workdayOptional = appWorkdayDao.getByDay(yyyyMMdd);
            AppWorkday byDate = workdayOptional.get();
            if( byDate.getIsWorkday().byteValue() == dayType ) {
                hasPlusDays++;
            }
            date = date.plus(1, ChronoUnit.DAYS);
        }

        return date;
    }

    /*@Override
    public List<AppWorkday> getListByDateOfTemp(String exhibitionNo,String startDate, String endDate) {
        List<AppWorkday> dayList = appWorkdayDao.findByDayBetween(startDate, endDate);
        dayList.stream().forEach(day -> {
            ObjectNode config = day.getConfig();
            JsonNode statusNode = config.get(exhibitionNo);
            if(statusNode == null){
                day.setIsWorkday(0);
            } else {
                Integer status = statusNode.intValue();
                day.setIsWorkday(status);
            }

        });
        return dayList;
    }*/

    @Override
    public List<AppWorkday> getListByDateOfTemp(String exhibitionNo,  String startDate, String endDate) {
        List<AppWorkday> dayList = appWorkdayDao.findByDayBetween(startDate, endDate);
        for (int i = 0; i < dayList.size(); i++) {
            AppWorkday workDay = dayList.get(i);
            String day = workDay.getDay();
            workDay.setIsWorkday(1);
            LocalDate dateOfDay = LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyyMMdd"));
            //第一步，判断日期是否在此展会时间内，不在范围内的，全部关闭

            //第二步，判断日期是否已经设置过规则，设置过规则的，按规则处理
            Boolean availableByRound = iAppointmentService.isAvailableByRound(exhibitionNo, dateOfDay);
            if(availableByRound!=null){
                if(!availableByRound){
                    workDay.setIsWorkday(0);
                } else {
                    workDay.setIsWorkday(1);
                }
                continue;
            }
            //第三步，判断日期是节假日
            Boolean availableByHoliday = iAppointmentService.isAvailableByHoliday(exhibitionNo,dateOfDay);
            if(!availableByHoliday){
                workDay.setIsWorkday(0);
            }
        }

        return dayList;
    }
}
