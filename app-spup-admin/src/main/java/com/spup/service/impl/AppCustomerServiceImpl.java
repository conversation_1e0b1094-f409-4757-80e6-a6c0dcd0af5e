package com.spup.service.impl;

import com.huangdou.commons.api.CommonResult;
import com.huangdou.commons.api.ResultCodeEnum;

import com.spup.db.dao.authority.AppCustomerDao;
import com.spup.db.entity.authority.AppCustomer;
import com.spup.dto.CustomerListRequest;
import com.spup.service.IAppCustomerService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class AppCustomerServiceImpl implements IAppCustomerService {
    @Resource
    private AppCustomerDao appCustomerDao;

    @Override
    public AppCustomer get(String unionid) {
        Optional<AppCustomer> customerOptional = appCustomerDao.getByUnionid(unionid);
        if(!customerOptional.isPresent()){
            return null;
        }
        return customerOptional.get();
    }

    @Override
    public AppCustomer save(String unionid, String openid) {

        AppCustomer appCustomer = get(unionid);

        if(appCustomer==null){//首次登录

            appCustomer = new AppCustomer();
            appCustomer.setUnionid(unionid);
            appCustomer.setMiniOpenid(openid);

            AppCustomer customer = appCustomerDao.save(appCustomer);
            return customer;
        }
        return null;
    }

    @Override
    public AppCustomer save(String unionid, String openid,String userName,String avatar,byte gender) {

        AppCustomer appCustomer = get(unionid);

        if(appCustomer==null){//首次登录
            appCustomer = new AppCustomer();
            appCustomer.setUnionid(unionid);
            appCustomer.setMiniOpenid(openid);
            appCustomer.setUserGender(gender);
            appCustomer.setUserAvatarSrc(avatar);
            appCustomer.setUserName(userName);

            AppCustomer customer = appCustomerDao.save(appCustomer);
            return customer;
        } else {
            appCustomer = new AppCustomer();
            appCustomer.setUserGender(gender);
            appCustomer.setUserAvatarSrc(avatar);
            appCustomer.setUserName(userName);

            AppCustomer customer = appCustomerDao.save(appCustomer);
            return customer;
        }
    }

    @Override
    public CommonResult<?> update(AppCustomer customer) {
        //获取unionid
        String unionid = customer.getUnionid();
        //判断用户是否存在
        AppCustomer customerByUnionid = get(unionid);
        if(customerByUnionid==null){
            return CommonResult.failed(ResultCodeEnum.USER_NOEXIST);
        }
        AppCustomer update = appCustomerDao.save(customer);
        return CommonResult.succeeded(update);
    }

    @Override
    public Page<AppCustomer> getPageList(CustomerListRequest listParam) {
        //调用分页插件
        Pageable pageable = PageRequest.of(listParam.getPageNum()-1, listParam.getPageSize());
        //从数据库查询
        Specification<AppCustomer> spec = new Specification<AppCustomer>() {
            //Predicate:封装了 单个的查询条件
            /**
             * Root<Users> root:查询对象的属性的封装。
             * CriteriaQuery<?> query：封装了我们要执行的查询中的各个部分的信息，select  from order by
             * CriteriaBuilder cb:查询条件的构造器。定义不同的查询条件
             */
            @Override
            public Predicate toPredicate(@NonNull Root<AppCustomer> root, @NonNull CriteriaQuery<?> query, @NonNull CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();
                if(StringUtils.hasLength(listParam.getUserName())){
                    list.add(cb.like(root.get("userName"),"%"+listParam.getUserName()+"%"));//筛选
                }
                if(StringUtils.hasLength(listParam.getRealName())){
                    list.add(cb.like(root.get("realName"),"%"+listParam.getRealName()+"%"));//筛选
                }

                if(StringUtils.hasLength(listParam.getPhone())){
                    list.add(cb.like(root.get("phone"),"%"+listParam.getPhone()+"%"));//筛选
                }
                if(StringUtils.hasLength(listParam.getCardNo())){
                    list.add(cb.equal(root.get("cardNo"),listParam.getCardNo()));//筛选
                }
                query.orderBy(cb.desc(root.get("id")));//排序
                Predicate[] arr = new Predicate[list.size()];
                return cb.and(list.toArray(arr));
            }
        };

        Page<AppCustomer> all = appCustomerDao.findAll(spec, pageable);

        return all;
    }
}
