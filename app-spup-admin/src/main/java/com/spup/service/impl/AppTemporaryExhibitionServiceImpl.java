package com.spup.service.impl;

import com.spup.db.dao.appointment.AppTemporaryExhibitionDao;
import com.spup.db.entity.appointment.AppTemporaryExhibition;
import com.spup.enums.ExhibitionStatusEnum;
import com.spup.service.IAppTemporaryExhibitionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
public class AppTemporaryExhibitionServiceImpl implements IAppTemporaryExhibitionService {
    @Resource
    private AppTemporaryExhibitionDao appTemporaryExhibitionDao;

    public List<AppTemporaryExhibition> getExhibition(){
        List<AppTemporaryExhibition> appTemporaryExhibitions = appTemporaryExhibitionDao.findByStatus(ExhibitionStatusEnum.LIVING.getCode());
        return appTemporaryExhibitions;
    }

    @Override
    public AppTemporaryExhibition getExhibitionDetail(String exhibitionNo) {
        Optional<AppTemporaryExhibition> exhibitionOptional = appTemporaryExhibitionDao.getByExhibitionNoAndStatus(exhibitionNo,ExhibitionStatusEnum.LIVING.getCode());
        if(!exhibitionOptional.isPresent()){
            return null;
        }
        return exhibitionOptional.get();
    }
}
