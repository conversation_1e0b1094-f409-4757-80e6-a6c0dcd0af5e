package com.spup.service.impl;

import com.spup.db.dao.ExhibitionDao;
import com.spup.db.dao.RoundDao;
import com.spup.db.entity.ExhibitionInfo;
import com.spup.db.entity.RoundConfig;
import com.spup.javaConfig.AppointmentConfig;
import com.spup.javaConfig.AppointmentConfigDetail;
import com.spup.service.IAppointmentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Optional;

@Service
public class AppointmentServiceImpl implements IAppointmentService {
    @Resource
    RoundDao roundDao;
    @Resource
    ExhibitionDao exhibitionDao;
    @Resource
    AppointmentConfig appointmentConfig;

    @Override
    public Optional<ExhibitionInfo> getExhibitionById(Long id) {
        return exhibitionDao.findById(id);
    }

    @Override
    public Optional<RoundConfig> getRoundByRoundDateAndExhibitionId(String exhibitionId,LocalDate date){
        return roundDao.findByExhibitionIdAndRoundDate(exhibitionId,date);
    }

    @Override
    public Boolean isAvailableByRound(String exhibitionId, LocalDate roundDate) {
        Optional<RoundConfig> result = getRoundByRoundDateAndExhibitionId(exhibitionId,roundDate);
        if (!result.isPresent()) {
            return null;
        }
        RoundConfig roundConfig = result.get();
        if (roundConfig.getRoundStatus() == RoundConfig.RoundStatusEnum.open) {
            return true;
        }
        if (roundConfig.getRoundStatus() == RoundConfig.RoundStatusEnum.close) {
            return false;
        }
        return null;
    }

    @Override
    public Boolean   isAvailableByHoliday(String exhibitionId, LocalDate roundDate) {
        AppointmentConfigDetail config = appointmentConfig.getConfig(exhibitionId);
        if(config!=null) {
            if (config.getWeekdayList().stream().anyMatch(dayOfWeek -> dayOfWeek == roundDate.getDayOfWeek())) {
                return false;
            }
            if (config.getHolidayList().stream().anyMatch(localDate -> roundDate.equals(localDate))) {
                return false;
            }
        }
        return true;
    }
}
