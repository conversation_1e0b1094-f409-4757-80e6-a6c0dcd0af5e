package com.spup.service.impl;

import com.spup.db.dao.ArtCenterInfoDao;
import com.spup.db.entity.ArtCenterInfo;
import com.spup.service.ArtCenterInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class ArtCenterInfoServiceImpl implements ArtCenterInfoService {
    @Resource
    private ArtCenterInfoDao artCenterInfoDao;
    @Override
    public ArtCenterInfo save(ArtCenterInfo info) {
        return artCenterInfoDao.save(info);
    }

    @Override
    public Optional<ArtCenterInfo> findBySection(ArtCenterInfo.SectionEnum section) {
        return artCenterInfoDao.findBySection(section);
    }
}
