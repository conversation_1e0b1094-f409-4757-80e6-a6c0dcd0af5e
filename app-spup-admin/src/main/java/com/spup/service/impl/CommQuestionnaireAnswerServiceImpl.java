package com.spup.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.spup.db.dao.CommQuestionnaireAnswerDao;
import com.spup.db.entity.CommQuestionnaireAnswer;
import com.spup.dto.QuestionnaireListRequest;
import com.spup.service.ICommQuestionnaireAnswerService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class CommQuestionnaireAnswerServiceImpl implements ICommQuestionnaireAnswerService {
    @Resource
    private CommQuestionnaireAnswerDao commQuestionnaireAnswerDao;
    @Resource
    private ObjectMapper objectMapper;

    public CommQuestionnaireAnswer save(CommQuestionnaireAnswer answer, String unionid) {
        return commQuestionnaireAnswerDao.save(answer);
    }

    @Override
    public List<CommQuestionnaireAnswer> getAllAnswer(Long questionnaireId, String unionid) {

        List<CommQuestionnaireAnswer> answers = commQuestionnaireAnswerDao.findByUnionidAndQuestionnaireId(unionid,
                questionnaireId);
        return answers;
    }

    @Override
    public List<CommQuestionnaireAnswer> getAllAnswer(Long questionnaireId, QuestionnaireListRequest listParam) {
        // 从数据库查询
        List<CommQuestionnaireAnswer> questionnaireAnswers = commQuestionnaireAnswerDao
                .findByQuestionnaireId(questionnaireId);
        if (listParam.getStartDate() != null) {
            LocalDateTime startDateTime = listParam.getStartDate().atTime(0, 0);
            questionnaireAnswers = questionnaireAnswers.stream()
                    .filter(answer -> answer.getCreateTime().compareTo(startDateTime) >= 0)
                    .collect(Collectors.toList());
        }
        if (listParam.getEndDate() != null) {
            ;
            LocalDateTime endDateTime = listParam.getEndDate().atTime(23, 59, 59);
            questionnaireAnswers = questionnaireAnswers.stream()
                    .filter(answer -> answer.getCreateTime().compareTo(endDateTime) <= 0)
                    .collect(Collectors.toList());
        }
        return questionnaireAnswers;
    }

    @Override
    public Page<CommQuestionnaireAnswer> getPageList(QuestionnaireListRequest listParam) {
        Pageable pageable = PageRequest.of(listParam.getPageNum() - 1, listParam.getPageSize());
        // 从数据库查询
        Specification<CommQuestionnaireAnswer> spec = new Specification<CommQuestionnaireAnswer>() {
            // Predicate:封装了 单个的查询条件
            /**
             * Root<Users> root:查询对象的属性的封装。
             * CriteriaQuery<?> query：封装了我们要执行的查询中的各个部分的信息，select from order by
             * CriteriaBuilder cb:查询条件的构造器。定义不同的查询条件
             */
            @Override
            public Predicate toPredicate(@NonNull Root<CommQuestionnaireAnswer> root, @NonNull CriteriaQuery<?> query,
                    @NonNull CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();
                if (StringUtils.hasLength(listParam.getTitle())) {
                    list.add(cb.like(root.get("title"), "%" + listParam.getTitle() + "%"));// 筛选
                }
                if (listParam.getStartDate() != null) {
                    list.add(cb.greaterThanOrEqualTo(root.get("createTime"), listParam.getStartDate().atTime(0, 0)));// 筛选
                }
                if (listParam.getEndDate() != null) {
                    list.add(cb.lessThanOrEqualTo(root.get("createTime"), listParam.getEndDate().atTime(23, 59, 59)));// 筛选
                }
                list.add(cb.equal(root.get("questionnaireId"), listParam.getQuestionnaireId()));
                query.orderBy(cb.desc(root.get("id")));// 排序
                Predicate[] arr = new Predicate[list.size()];
                return cb.and(list.toArray(arr));
            }
        };

        Page<CommQuestionnaireAnswer> all = commQuestionnaireAnswerDao.findAll(spec, pageable);
        return all;
    }

    @Override
    public ObjectNode getAnswerDetail(Long answerId) throws JsonProcessingException {
        Optional<CommQuestionnaireAnswer> answerOptional = commQuestionnaireAnswerDao.findById(answerId);
        CommQuestionnaireAnswer answer = answerOptional.get();
        ObjectNode node = objectMapper.valueToTree(answer);
        node.put("answer", objectMapper.writeValueAsString(answer.getAnswer()));

        // dto.setAnswers(JSONArray.parseArray(answer.getAnswer()));
        return node;
    }
}
