package com.spup.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Optional;

import com.spup.dto.UploadRequestParam;
import com.spup.service.IUploadService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
@PropertySource("classpath:fileConfig_${spring.profiles.active}.properties")
public class UploadServiceImpl implements IUploadService {
    @Value("${file.path.save}")
    private String savePath;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public ObjectNode save(MultipartFile file, UploadRequestParam param) {
        LocalDateTime now = LocalDateTime.now();
        String yyyy = now.format(DateTimeFormatter.ofPattern("yyyy"));
        String mm = now.format(DateTimeFormatter.ofPattern("MM"));
        String dd = now.format(DateTimeFormatter.ofPattern("dd"));
        String yyyyMMddhhmmss = now.format(DateTimeFormatter.ofPattern("yyyyMMddhhmmss"));

        String localFilePath = savePath + File.separator + param.getSection() + File.separator + yyyy+File.separator+mm+File.separator+dd
                + File.separator + yyyyMMddhhmmss + "_" + file.getOriginalFilename();

        File localFile = saveMultipartFile(file, localFilePath);

        Optional<String> originalFilenameOpt = Optional.fromNullable(file.getOriginalFilename());
        String originalFilename;
        if (!originalFilenameOpt.isPresent()) {
            throw new RuntimeException();
        }
        originalFilename = originalFilenameOpt.get();
        String fileType = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        String fileName = originalFilename.substring(0, originalFilename.lastIndexOf("."));

        ObjectNode result = objectMapper.createObjectNode();

        result.put("name",fileName);
        result.put("fileType",fileType);
        result.put("filePath",localFile.getAbsolutePath().replaceAll(savePath, ""));
        return result;
    }

    public File saveMultipartFile(MultipartFile file, String localFilePath) {
        File localFile = new File(localFilePath);
        File parentDirectory = localFile.getParentFile();
        if (!parentDirectory.exists()) {
            parentDirectory.mkdirs();
        }
        try {
            file.transferTo(localFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return localFile;
    }
}

