package com.spup;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import com.spup.config.TurnstilesProperties;

@SpringBootApplication
@ComponentScan({"com.spup","com.huangdou"})
@EnableJpaRepositories({"com.spup", "com.huangdou"})
@EntityScan({"com.spup","com.huangdou"})
@EnableConfigurationProperties(TurnstilesProperties.class)
public class SpupApplication extends SpringBootServletInitializer {
    public static void main(String[] args) {
        SpringApplication.run(SpupApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(SpupApplication.class);
    }
}
