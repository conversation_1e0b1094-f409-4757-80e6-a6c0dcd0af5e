package com.spup;

import java.util.concurrent.TimeUnit;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import io.micrometer.core.instrument.Metrics;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class TaskMonitoringAspect {

    @Around("execution(* com.spup.task..*.*(..)) && @annotation(org.springframework.scheduling.annotation.Scheduled)")
    public Object monitorTaskExecution(ProceedingJoinPoint pjp) throws Throwable {
        long start = System.currentTimeMillis();
        try {
            return pjp.proceed();
        } finally {
            long duration = System.currentTimeMillis() - start;
            Metrics.timer("task.execution.time").record(duration, TimeUnit.MILLISECONDS);
        }
    }
}