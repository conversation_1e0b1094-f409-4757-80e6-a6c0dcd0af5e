package com.spup.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Custom metrics configuration to replace the missing MetricsAutoConfiguration
 */
@Configuration
public class CustomMetricsConfiguration {

    /**
     * Provides a simple meter registry for metrics
     * This is a minimal implementation to avoid the dependency on Spring Boot Actuator
     */
    @Bean
    public MeterRegistry meterRegistry() {
        return new SimpleMeterRegistry();
    }
}
