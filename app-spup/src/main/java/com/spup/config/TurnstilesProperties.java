package com.spup.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "turnstiles")
@Getter
@Setter
public class TurnstilesProperties {
    
    private List<Supplier> suppliers;
    
    @Getter
    @Setter
    public static class Supplier {
        private String name;
        private String signKey;
    }
}