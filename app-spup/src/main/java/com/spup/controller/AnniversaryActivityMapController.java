package com.spup.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
@RequestMapping("/anniversary")
public class AnniversaryActivityMapController {
    
    @GetMapping("/activityMap")
    public String showActivityMap() {

        log.info("entering activity map");
        // Load the image directly from the classpath
        // Resource resource = new ClassPathResource("static/images/activity-map.jpg");
        
        // return ResponseEntity.ok()
        //         .contentType(MediaType.IMAGE_JPEG)
        //         .body(resource);
        return "activity-map";
    }
}
