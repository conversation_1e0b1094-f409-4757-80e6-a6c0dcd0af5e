package com.spup.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.huangdou.commons.api.CommonResult;
import com.spup.dto.AppAppointmentOrderTemporaryExhibitionRequest;
import com.spup.service.appointment.IAppAppointmentOrderTemporaryExhibitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;

@Api(tags = "特展订单")
@RestController
@RequestMapping("/exhibitionOrder")
public class AppAppointmentOrderTemporaryExhibitionController {
    @Autowired
    private IAppAppointmentOrderTemporaryExhibitionService iAppAppointmentOrderTemporaryExhibitionService;

    @ApiOperation(value = "下单预约")
    @PostMapping(value = "/createOrder")
    public CommonResult<?> createOrder(@RequestBody AppAppointmentOrderTemporaryExhibitionRequest orderRequest , HttpServletRequest request) throws  JsonProcessingException {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentOrderTemporaryExhibitionService.save(orderRequest,unionid);
    }

    @ApiOperation(value = "预约列表")
    @GetMapping(value = "/list")
    public CommonResult<?> list(HttpServletRequest request) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentOrderTemporaryExhibitionService.getList(unionid);
    }

    @ApiOperation(value = "取消预约")
    @GetMapping(value = "/cancel/{orderNo}")
    public CommonResult<?> cancel(@PathVariable String orderNo , HttpServletRequest request ) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentOrderTemporaryExhibitionService.cancel(orderNo,unionid);
    }

    @ApiOperation(value = "删除预约记录")
    @GetMapping(value = "/delete/{orderNo}")
    public CommonResult<?> delete(@PathVariable String orderNo ) throws ParseException {

        return iAppAppointmentOrderTemporaryExhibitionService.delete(orderNo);
    }



}
