package com.spup.controller;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.appointment.BlackList;
import com.spup.dto.AppAppointmentTeamOrderRequest;
import com.spup.service.BlackListService;
import com.spup.service.appointment.IAppAppointmentTeamOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;

@Api(tags = "团体预约")
@RestController
@RequestMapping("/teamOrder")
public class AppAppointmentTeamOrderController {
    @Resource
    private IAppAppointmentTeamOrderService iAppAppointmentTeamOrderService;
    @Resource
    private BlackListService blackListService;

    @ApiOperation(value = "预约")
    @PostMapping(value = "/createOrder")
    public CommonResult<?> createOrder(@RequestBody AppAppointmentTeamOrderRequest orderRequest , HttpServletRequest request) {
        String unionid = (String)request.getSession().getAttribute("unionid");
        if(blackListService.isInBlackList(unionid, BlackList.CategoryEnum.APPOINTMENT)){
            return CommonResult.failed("禁止预约中...");
        }
        return iAppAppointmentTeamOrderService.save(orderRequest,unionid);
    }

    @ApiOperation(value = "预约列表")
    @GetMapping(value = "/list")
    public CommonResult<?> list(HttpServletRequest request) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentTeamOrderService.getList(unionid);
    }

    @ApiOperation(value = "取消预约")
    @GetMapping(value = "/cancel/{orderNo}")
    public CommonResult<?> cancel(@PathVariable String orderNo , HttpServletRequest request) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentTeamOrderService.cancel(orderNo,unionid);
    }

    @ApiOperation(value = "删除预约记录")
    @GetMapping(value = "/delete/{orderNo}")
    public CommonResult<?> delete(@PathVariable String orderNo , HttpServletRequest request) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");
        return iAppAppointmentTeamOrderService.delete(orderNo,unionid);
    }


    @ApiOperation(value = "查询所有预约")
    @GetMapping(value = "/allList")
    public CommonResult<?> allList(HttpServletRequest request) {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentTeamOrderService.getList(unionid);
    }
}
