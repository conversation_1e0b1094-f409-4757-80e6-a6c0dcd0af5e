package com.spup.controller;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.AppComments;
import com.spup.service.IAppCommentsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "留言")
@RestController
@RequestMapping("/comment")
public class AppCommentsController {
    @Autowired
    private IAppCommentsService iAppCommentsService;

    @ApiOperation(value = "留言")
    @RequestMapping(value = "/save",method = RequestMethod.POST)
    public CommonResult<?> getAvailableTime(@RequestBody AppComments comments , HttpServletRequest request)  {
        String unionid = (String)request.getSession().getAttribute("unionid");

        comments.setCustomer(unionid);
        return CommonResult.succeeded(iAppCommentsService.save(comments));
    }
}
