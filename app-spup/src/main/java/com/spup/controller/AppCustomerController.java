package com.spup.controller;

import com.huangdou.commons.api.CommonResult;

import com.spup.db.entity.authority.AppCustomer;
import com.spup.db.entity.authority.AppCustomerContacts;
import com.spup.dto.AppCustomerContactsRequest;
import com.spup.dto.AppCustomerRequest;
import com.spup.service.IAppCustomerContactsService;
import com.spup.service.IAppCustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Api(tags = "客户管理")
@RestController
@RequestMapping("/customer")
public class AppCustomerController {

    @Autowired
    private IAppCustomerService iAppCustomerService;

    @Autowired
    private IAppCustomerContactsService iAppCustomerContactsService;

    @ApiOperation(value = "获取小程序用户信息")
    @GetMapping(value = "/get")
    public CommonResult<?> get(HttpServletRequest request) throws ParseException {
        String unionid = (String) request.getSession().getAttribute("unionid");

        AppCustomer info = iAppCustomerService.get(unionid);
        if (info == null) {
            return CommonResult.failed("此用户不存在");
        } else {
            return CommonResult.succeeded(info);
        }
    }

    @ApiOperation(value = "添加联系人")
    @PostMapping(value = "/addContacts")
    public CommonResult<?> addContacts(@RequestBody AppCustomerContactsRequest contacts, HttpServletRequest request) {
        String unionid = (String) request.getSession().getAttribute("unionid");

        AppCustomerContacts appCustomerContacts = new AppCustomerContacts();

        BeanUtils.copyProperties(contacts, appCustomerContacts);
        appCustomerContacts.setOwerUnionid(unionid);
        iAppCustomerContactsService.save(appCustomerContacts);
        return CommonResult.succeeded(appCustomerContacts);
    }

    @ApiOperation(value = "删除联系人")
    @GetMapping(value = "/deleteContacts/{contactId}")
    public CommonResult<?> deleteContacts(@PathVariable Long contactId) {

        return CommonResult.succeeded(iAppCustomerContactsService.delete(contactId));
    }

    @ApiOperation(value = "修改联系人")
    @PostMapping(value = "/modifyContacts")
    public CommonResult<?> modifyContacts(@RequestBody AppCustomerContactsRequest contacts) {

        Optional<AppCustomerContacts> contactsOpt = iAppCustomerContactsService.findById(contacts.getId());
        if (!contactsOpt.isPresent()) {
            return CommonResult.failed("联系人不存在");
        }
        AppCustomerContacts appCustomerContacts = contactsOpt.get();
        appCustomerContacts.setIdcardCategory(contacts.getIdcardCategory());
        appCustomerContacts.setName(contacts.getName());
        appCustomerContacts.setIdcardNo(contacts.getIdcardNo());
        appCustomerContacts.setPhone(contacts.getPhone());
        appCustomerContacts.setRemark(contacts.getRemark());
        return CommonResult.succeeded(iAppCustomerContactsService.modify(appCustomerContacts));
    }

    @ApiOperation(value = "获取联系人")
    @GetMapping(value = "/getContacts")
    public CommonResult<?> getContacts(HttpServletRequest request) {
        String unionid = (String) request.getSession().getAttribute("unionid");

        return CommonResult.succeeded(iAppCustomerContactsService.getListByOwner(unionid));
    }

    @ApiOperation(value = "获取可预约人")
    @GetMapping(value = "/getAvaildContacts")
    public CommonResult<?> getAvaildContacts(HttpServletRequest request) {
        String unionid = (String) request.getSession().getAttribute("unionid");
        String yyyyMMdd = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return CommonResult.succeeded(iAppCustomerContactsService.getListByOwner(yyyyMMdd, unionid));
    }

    @ApiOperation(value = "完善资料")
    @PostMapping(value = "/saveCustomer")
    public CommonResult<?> saveCustomer(@RequestBody AppCustomerRequest customerRequest, HttpServletRequest request) {
        String unionid = (String) request.getSession().getAttribute("unionid");

        AppCustomer appCustomer = new AppCustomer();

        BeanUtils.copyProperties(customerRequest, appCustomer);
        appCustomer.setUnionid(unionid);

        return iAppCustomerService.update(appCustomer);
    }
}
