package com.spup.controller;

import com.huangdou.commons.api.CommonResult;
import com.spup.dto.GoodsListParam;
import com.spup.service.IAppSurroundingGoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "文创商品展示")
@RestController
@RequestMapping(value = "/goods")
public class AppGoodsController {
    @Resource
    private IAppSurroundingGoodsService iAppSurroundingGoodsService;


    @ApiOperation(value = "文创商品列表")
    @GetMapping(value="/listByPage")
    public CommonResult<?> listByPage (GoodsListParam param) {
        return CommonResult.succeeded(iAppSurroundingGoodsService.getListByPage(param));
    }



    @ApiOperation(value = "文创商品明细")
    @GetMapping(value="/viewGoods/{id}")
    public CommonResult<?> viewManageUser (@PathVariable Long id) {

        return CommonResult.succeeded(iAppSurroundingGoodsService.view(id));
    }

}
