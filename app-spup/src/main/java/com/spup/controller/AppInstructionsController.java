package com.spup.controller;

import com.huangdou.commons.api.CommonResult;
import com.spup.service.appointment.IAppAppointmentInstructionsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;

@Api(tags = "参观须知等更新")
@RestController
@RequestMapping(value = "/instructions")
public class AppInstructionsController {
    @Resource
    private IAppAppointmentInstructionsService iAppAppointmentInstructionsService;

    @ApiOperation(value = "查询")
    @GetMapping(value="/get")
    public CommonResult<?> get () throws UnsupportedEncodingException {
        return CommonResult.succeeded(iAppAppointmentInstructionsService.get());
    }
}
