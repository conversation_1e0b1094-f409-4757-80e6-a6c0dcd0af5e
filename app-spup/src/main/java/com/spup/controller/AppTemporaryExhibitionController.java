package com.spup.controller;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.appointment.AppTemporaryExhibition;
import com.spup.service.appointment.IAppTemporaryExhibitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Comparator;
import java.util.List;

@Api(tags = "临展")
@RestController
@RequestMapping("/exhibition")
public class AppTemporaryExhibitionController {
    @Resource
    private IAppTemporaryExhibitionService iAppTemporaryExhibitionService;

    @ApiOperation(value = "临展列表")
    @GetMapping(value = "/list")
    public CommonResult<?> list() throws ParseException {
        List<AppTemporaryExhibition> exhibitionList = iAppTemporaryExhibitionService.getExhibition();
        exhibitionList.sort(Comparator.comparing(AppTemporaryExhibition::getCreateTime).reversed());
        return CommonResult.succeeded(exhibitionList);
    }

    @ApiOperation(value = "临展详情")
    @GetMapping(value = "/detail/{exhibitionNo}")
    public CommonResult<?> detail(@PathVariable String exhibitionNo)  {
        return CommonResult.succeeded(iAppTemporaryExhibitionService.getExhibitionDetail(exhibitionNo));
    }
}
