package com.spup.controller;

import com.huangdou.commons.api.CommonResult;
import com.spup.service.IAppVisitGuideService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "导览")
@RestController
@RequestMapping("/guide")
public class AppVisitGuideController {
    @Autowired
    private IAppVisitGuideService iAppVisitGuideService;
    @ApiOperation(value = "获取导览数据")
    @RequestMapping(value = "/getData",method = RequestMethod.GET)
    public CommonResult<?> getData()  {
        return CommonResult.succeeded(iAppVisitGuideService.getAllList());
    }

    @ApiOperation(value = "展项浏览")
    @RequestMapping(value = "/read/{id}",method = RequestMethod.GET)
    public CommonResult<?> read(@PathVariable Long id)  {
        iAppVisitGuideService.read(id);
        return CommonResult.succeeded("");
    }
}
