package com.spup.controller;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.CommQuestionnaire;
import com.spup.db.entity.CommQuestionnaireAnswer;
import com.spup.dto.CommQuestionnaireDTO;
import com.spup.service.ICommQuestionnaireAnswerService;
import com.spup.service.ICommQuestionnaireService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "问卷")
@RestController
@RequestMapping("/questionnare")
public class QuestionnareController {
    @Resource
    private ICommQuestionnaireService iCommQuestionnaireService;
    @Resource
    private ICommQuestionnaireAnswerService iCommQuestionnaireAnswerService;


    @ApiOperation(value = "获取问卷信息")
    @GetMapping(value = {"/get/{id}","/get"})
    public CommonResult<?> get(@PathVariable(value = "id" , required = false) Long id, HttpServletRequest request)  {
        String unionid = (String)request.getSession().getAttribute("unionid");
        //String openid = (String)request.getSession().getAttribute("openid");
        if(id == null){
            id = 1L;
        }
        List<CommQuestionnaireAnswer> allAnswer = iCommQuestionnaireAnswerService.getAllAnswer(id, unionid);
        //todo 判断是否可以提交
        LocalDate now = LocalDate.now();
        allAnswer = allAnswer.stream()
                .filter(commQuestionnaireAnswer -> commQuestionnaireAnswer.getCreateTime().toLocalDate().compareTo(now)==0)
                .collect(Collectors.toList());
        int submit = 0;
        String message = "";

        if(!CollectionUtils.isEmpty(allAnswer)){
            submit = allAnswer.size();
            message = "您已提交过此问卷";
        }
        CommQuestionnaire questionnaire = iCommQuestionnaireService.getQuestionnaireById(id);

        CommQuestionnaireDTO dto = new CommQuestionnaireDTO();
        BeanUtils.copyProperties(questionnaire,dto);
        dto.setHasSubmit(submit);
        dto.setMessage(message);
        return CommonResult.succeeded(dto);
//        return CommonResult.succeeded(objectNode);
    }

    @ApiOperation(value = "问卷提交")
    @PostMapping(value = "/save")
    public CommonResult<?> save(@RequestBody CommQuestionnaireAnswer answer, HttpServletRequest request)  {
        String unionid = (String)request.getSession().getAttribute("unionid");
        //String openid = (String)request.getSession().getAttribute("openid");
        CommQuestionnaireAnswer returnCommQuestionnaireAnswer = iCommQuestionnaireAnswerService.save(answer,unionid);
        return CommonResult.succeeded(returnCommQuestionnaireAnswer);

    }

}
