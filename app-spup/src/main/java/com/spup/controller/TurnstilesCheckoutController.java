package com.spup.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.huangdou.commons.api.CommonResult;
import com.spup.dto.OrderRequest;
import com.spup.dto.Supplier;
import com.spup.dto.TurnstilesRequestDTO;
import com.spup.enums.OrderCategoryEnum;
import com.spup.javaConfig.SupplierConfig;
import com.spup.service.IOrderService;
import com.spup.utils.SignUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Optional;

@Api(tags = "闸机核销")
@RestController
@RequestMapping("/turnstiles")
public class TurnstilesCheckoutController {
    @Resource
    private SupplierConfig supplierConfig;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private ObjectMapper objectMapper;

    private Integer lock = new Integer(1);

    @ApiOperation(value = "上传扫码内容")
    @PostMapping(value = "/acceptResult")
    public CommonResult<?> acceptResult(@RequestBody TurnstilesRequestDTO requestDTO, HttpServletRequest request) {
        //校验
        //校验供应商
        Optional<Supplier> supplierOpt = getSupplier(requestDTO.getSupplier());
        if(!supplierOpt.isPresent()){
            return CommonResult.failed(999,"非法供应商");
        }
        //校验签名
       Supplier supplier = supplierOpt.get();
        @SuppressWarnings("unchecked")
        Map<String, Object> param = objectMapper.convertValue(requestDTO, Map.class);
        param.remove("sign");
        String sign = SignUtil.signSHA256(param,supplier.getSignKey());
        if(!sign.equals(requestDTO.getSign())){
            return CommonResult.failed(9999,"验签失败");
        }
        String scanResult = requestDTO.getScanResult();
        String[] params = scanResult.split("sPUp2000");
        if(params.length!=5){
            return CommonResult.failed(1000,"非法二维码");
        }

        //数据处理
        OrderRequest orderRequest = conver2OrderRequest(params);
        OrderCategoryEnum _enum = OrderCategoryEnum.getEnum(orderRequest.getOrderCategory());
        IOrderService service = (IOrderService) applicationContext.getBean(_enum.getServiceClass());

        synchronized (lock){
            return service.checkout(orderRequest, supplier.getName());
        }

    }

    private Optional<Supplier> getSupplier(String supplier) {
        return supplierConfig.getSuppliers().stream()
                .filter(supplier1 -> supplier1.getName().equals(supplier))
                .findFirst();
    }

    private  OrderRequest conver2OrderRequest(String[] paramsArr){
        String orderCategory =paramsArr[1];
        // orderId is not used but kept for documentation purposes
        // String orderId =paramsArr[2];
        String suborderId =paramsArr[3];
        OrderRequest orderRequest = new OrderRequest();
        orderRequest.setSubOrderId(suborderId);
        orderRequest.setOrderCategory(Byte.parseByte(orderCategory));
        return  orderRequest;
    }

    // Method kept for future validation implementation
    @SuppressWarnings("unused")
    private boolean check(TurnstilesRequestDTO requestDTO){
        return true;
    }
}
