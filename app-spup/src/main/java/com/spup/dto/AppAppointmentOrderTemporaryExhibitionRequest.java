package com.spup.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel("预约下单对象")
public class AppAppointmentOrderTemporaryExhibitionRequest implements Serializable {
    @ApiModelProperty("场次编号，非id，如202301180930")
    private String batchNo;

    @ApiModelProperty("临展编号，非id，如L20230906T20230923")
    private String exhibitionNo;

    @ApiModelProperty("联系人列表，数组字符串格式，如:[{" +
            "  \'idcardCategory\': 1," +
            "  \'idcardNo\': \'string\'," +
            "  \'name\': \'string\'," +
            "  \'phone\': \'string\'" +
            "}]")
    private String contacts;

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getExhibitionNo() {
        return exhibitionNo;
    }

    public void setExhibitionNo(String exhibitionNo) {
        this.exhibitionNo = exhibitionNo;
    }
}
