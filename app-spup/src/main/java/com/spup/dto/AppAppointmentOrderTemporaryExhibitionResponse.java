package com.spup.dto;


import com.spup.db.entity.appointment.AppAppointmentOrder;
import com.spup.db.entity.appointment.AppAppointmentSuborderTemporaryExhibition;
import io.swagger.annotations.ApiModel;

import java.util.List;

@ApiModel("预约下单对象")
public class AppAppointmentOrderTemporaryExhibitionResponse extends AppAppointmentOrder {
    private List<AppAppointmentSuborderTemporaryExhibition> suborders;

    public List<AppAppointmentSuborderTemporaryExhibition> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppAppointmentSuborderTemporaryExhibition> suborders) {
        this.suborders = suborders;
    }
}