package com.spup.dto;

import com.spup.enums.TeamOrderStatusEnum;

import java.io.Serializable;

public class AppTeamOrderConfirmRequest implements Serializable {
    private Long id;
    private String orderRemark;
    private Short orderStatus = TeamOrderStatusEnum.FINISHED.getCode();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderRemark() {
        return orderRemark;
    }

    public void setOrderRemark(String orderRemark) {
        this.orderRemark = orderRemark;
    }

    public Short getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Short orderStatus) {
        this.orderStatus = orderStatus;
    }
}
