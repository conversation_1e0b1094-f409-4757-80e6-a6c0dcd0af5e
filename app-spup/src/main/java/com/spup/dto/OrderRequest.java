package com.spup.dto;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;

@ApiModel("核销对象")
public class OrderRequest implements Serializable {

    private Byte orderCategory;

    private String subOrderId;

    public Byte getOrderCategory() {
        return orderCategory;
    }

    public void setOrderCategory(Byte orderCategory) {
        this.orderCategory = orderCategory;
    }

    public String getSubOrderId() {
        return subOrderId;
    }

    public void setSubOrderId(String subOrderId) {
        this.subOrderId = subOrderId;
    }
}
