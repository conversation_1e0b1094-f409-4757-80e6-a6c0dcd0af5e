package com.spup.enums;

import com.spup.service.appointment.IAppAppointmentItemOrderService;
import com.spup.service.appointment.IAppAppointmentOrderService;
import com.spup.service.appointment.IAppAppointmentOrderTemporaryExhibitionService;
import com.spup.service.appointment.IAppAppointmentTeamOrderService;

public enum OrderCategoryEnum {
    TICKET((byte)1, "门票预约", IAppAppointmentOrderService.class),
    TEAM((byte)2, "团体预约", IAppAppointmentTeamOrderService.class),
    ITEM_FYPD((byte)4, "展项预约-飞阅浦东", IAppAppointmentItemOrderService.class),
    EXHIBITION((byte)8, "临展", IAppAppointmentOrderTemporaryExhibitionService.class),
    EXHIBITION_TEAM((byte)16, "临展团队", IAppAppointmentOrderTemporaryExhibitionService.class),

    ; //此写法防止扩充时忘记分号

    private Byte code;
    private String name;
    private Class<?> serviceClass;
    private OrderCategoryEnum(Byte code, String name, Class<?> serviceClass) {
        this.code = code;
        this.name = name;
        this.serviceClass = serviceClass;
    }

    public static OrderCategoryEnum getEnum(Byte code) {
        OrderCategoryEnum[] enums = OrderCategoryEnum.values();
        for(int i=0; i< enums.length; i++){
            OrderCategoryEnum _enum = enums[i];
            if(_enum.getCode() == code){
                return _enum;
            }
        }
        return null;
    }

    public Byte getCode() {
        return code;
    }

    public void setCode(Byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Class<?> getServiceClass() {
        return serviceClass;
    }

    public void setServiceClass(Class<?> serviceClass) {
        this.serviceClass = serviceClass;
    }

    public static void main(String[] args) {
        byte a = 1;
        System.out.println(a&-1);
    }
}
