package com.spup.init;

import com.spup.counter.Item4Counter;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
@Order(value = 1)
public class ApplicationInit implements CommandLineRunner {

    @Resource
    Item4Counter item4Counter;

    @Override
    public void run(String... arg0) throws Exception {
        try {
            item4Counter.initMap();
            item4Counter.loadHasBook();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
