package com.spup.javaConfig;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 辉煌明天
 * 原文链接：https://blog.csdn.net/weixin_42236404/article/details/89918737
 * FileName: LogAop
 * Author:   huachun
 * email: <EMAIL>
 * Date:     2021/12/27 15:51
 * Description:
 */
@Aspect
@Component
public class LogAop {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogAop.class);
    @Resource
    private ObjectMapper objectMapper;

    @Pointcut("execution(public * com.spup.service.*.*(..))")
    public void webLog() {
    }


    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) throws JsonProcessingException {
        // 接收到请求，记录请求内容

        String methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName() + ":{}";
        String methodParams = Arrays.toString(joinPoint.getArgs());
        LOGGER.info("params:" + methodName, methodParams);
    }


    @AfterReturning(returning = "data", pointcut = "webLog()")
    public void doAfterReturning(JoinPoint joinPoint, Object data) throws JsonProcessingException  {
        String methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName() + ":{}";
        LOGGER.info("return:" + methodName, objectMapper.writeValueAsString(data));
    }

}