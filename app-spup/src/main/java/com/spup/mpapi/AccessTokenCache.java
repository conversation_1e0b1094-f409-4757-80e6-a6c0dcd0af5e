/*
package com.spup.mpapi;

import com.alibaba.fastjson.JSONObject;

import java.util.Date;

public class AccessTokenCache {
    private AccessToken accessToken;
    private static AccessTokenCache instance = new AccessTokenCache();
    private AccessTokenCache() {

    }

    public static  AccessTokenCache getInstance(){
        return instance;
    }

    public String getAccessToken() {
        return getAccessToken(false);
    }

    public String getAccessToken(boolean isRefresh) {
        Date now = new Date();

        if (!isRefresh && accessToken != null && (now.getTime() - accessToken.getCreateTime().getTime()) < (accessToken.getExpiresIn()-300)*1000) {
            return accessToken.getAccessToken();
        }
        accessToken = new AccessToken();
        AccessTokenApi api = new AccessTokenApi("wxb971efaed01158f4","8359f543f4c6df82c7a15c190262f28b");
        JSONObject object = api.sendData();
        accessToken.setAccessToken(object.getString("access_token"));
        accessToken.setExpiresIn(object.getInteger("expires_in"));
        accessToken.setCreateTime(new Date());

        return accessToken.getAccessToken();

    }
}
*/
