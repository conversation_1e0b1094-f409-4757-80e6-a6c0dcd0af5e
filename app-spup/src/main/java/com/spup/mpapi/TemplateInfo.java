/*
package com.spup.mpapi;

import com.alibaba.fastjson.JSONObject;

public class TemplateInfo {
    private JSONObject first;
    private JSONObject keyword1;
    private JSONObject keyword2;
    private JSONObject keyword3;
    private JSONObject keyword4;
    private JSONObject remark;

    public JSONObject getFirst() {
        return first;
    }

    public void setFirst(JSONO<PERSON> first) {
        this.first = first;
    }

    public JSONObject getKeyword1() {
        return keyword1;
    }

    public void setKeyword1(JSONObject keyword1) {
        this.keyword1 = keyword1;
    }

    public JSONObject getKeyword2() {
        return keyword2;
    }

    public void setKeyword2(JSONObject keyword2) {
        this.keyword2 = keyword2;
    }

    public JSONObject getKeyword3() {
        return keyword3;
    }

    public void setKeyword3(JSONObject keyword3) {
        this.keyword3 = keyword3;
    }

    public JSONObject getKeyword4() {
        return keyword4;
    }

    public void setKeyword4(JSONObject keyword4) {
        this.keyword4 = keyword4;
    }

    public JSONObject getRemark() {
        return remark;
    }

    public void setRemark(JSONObject remark) {
        this.remark = remark;
    }
}
*/
