/*
package com.spup.mpapi;

import com.alibaba.fastjson.JSONObject;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class TemplateInfo<PERSON>pi extends PostApi{
    private String openId;
    private String templateId;
    private String url;
    private Map<String, Map<String,String>> data = new HashMap<>();


    public void setData(String key, String value) {
        Map<String,String> _data = new HashMap<>();
        _data.put( "value", value);
        data.put( key, _data);
    }

    private String apiUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send";

    public TemplateInfoApi(String openId,String templateId, String url) {
        this.openId = openId;
        this.templateId = templateId;
        this.url = url;
    }


    @Override
    JSONObject getParams() {
        JSONObject object = new JSONObject();
        object.put("touser",openId);
        object.put("template_id",templateId);
        object.put("url",url);
        object.put("data",data);
        return object;
    }

    @Override
    String getApiUrl() {
        return apiUrl +"?access_token="+AccessTokenCache.getInstance().getAccessToken();
    }


    public static void main(String[] args) {
        TemplateInfoApi api = new TemplateInfoApi("ouHc26Nj3AGlvrSjNM2mQO5VVNIM",
                "d8grb6HQ1zu2XFaMrOt557GZo15SBwyIhi0fTQfxzS4","http://www.baidu.com");
        api.setData("first","团队预约提醒");
        api.setData("keyword1","沈吉");
        api.setData("keyword2","185****8005");
        api.setData("keyword3","2023-07-31 17:30");
        api.setData("keyword4","预约单位：\r\n来访人数：5人");

        JSONObject object = api.sendData();
        //System.out.println(object.toJSONString());
    }
}
*/
