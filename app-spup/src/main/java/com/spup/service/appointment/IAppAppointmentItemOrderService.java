package com.spup.service.appointment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.huangdou.commons.api.CommonResult;
import com.spup.dto.AppAppointmentItemOrderRequest;
import com.spup.service.IOrderService;


public interface IAppAppointmentItemOrderService extends IOrderService {
    //对外接口
    public CommonResult<?> save(AppAppointmentItemOrderRequest orderRequest, String unionid) throws JsonProcessingException;
    public CommonResult<?> getList(String unionid);
    public CommonResult<?> cancel(String orderNo,String unionid);
    public CommonResult<?> delete(String orderNo,String unionid);
    public CommonResult<?> breaked(String orderNo,String unionid);

}
