package com.spup.service.appointment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.appointment.AppAppointmentOrderTemporaryExhibition;
import com.spup.dto.AppAppointmentOrderTemporaryExhibitionRequest;
import com.spup.service.IOrderService;

import java.util.List;

public interface IAppAppointmentOrderTemporaryExhibitionService extends IOrderService {
    //对外接口
    CommonResult<?> save(AppAppointmentOrderTemporaryExhibitionRequest orderRequest, String unionid) throws JsonProcessingException;
    CommonResult<?> getList(String unionid);
    CommonResult<?> cancel(String orderNo,String unionid);
    CommonResult<?> breaked(String orderNo,String unionid);
    CommonResult<?> delete(String orderNo);
    //内部接口
    List<AppAppointmentOrderTemporaryExhibition> getListByUnionid(String unionid);
    AppAppointmentOrderTemporaryExhibition getOrderByOrderNo(String orderNo);
}
