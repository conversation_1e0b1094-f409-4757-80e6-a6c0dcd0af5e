package com.spup.service.appointment;

import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.appointment.AppAppointmentTeamOrder;
import com.spup.dto.AppAppointmentTeamOrderRequest;
import com.spup.dto.AppTeamOrderListRequest;
import com.spup.service.IOrderService;

import java.util.List;


public interface IAppAppointmentTeamOrderService extends IOrderService {
    //对外接口
    CommonResult<?> save(AppAppointmentTeamOrderRequest orderRequest, String unionid);
    CommonResult<?> getList(String unionid);
    CommonResult<?> cancel(String orderNo,String unionid);
    CommonResult<?> delete(String orderNo,String unionid);
    CommonResult<?> breaked(String orderNo,String unionid);

    CommonResult<?> getTodayTeamOrder();

    List<AppAppointmentTeamOrder> getTeamOrderByDate(String startDate, String endDate);
    CommonResult<?> listByParam(AppTeamOrderListRequest request);
}
