package com.spup.service.appointment;


import com.spup.db.entity.appointment.AppBatch;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface IAppBatchService {
    public AppBatch getByNo(String batchNo, Byte batchCategory);
    public Map<String,List<AppBatch>> getListByDate(Byte category, String startDate, String endDate);

    public List<AppBatch> getListByDate(Byte category, String date);
    public List<AppBatch> getListByDate(String exhibitionNo,Byte category, String date);
    public AppBatch save(AppBatch batch);
    public AppBatch update(AppBatch batch);
    AppBatch updateRemaining(String batchNo,Byte batchCategory,int updateNum);
    public void closeOverTime();

    public int initTicket(LocalDate startDate, LocalDate endDate);

    int initTeam(LocalDate startDate,LocalDate endDate);

    int initItem(LocalDate startDate,LocalDate endDate);

    void init(LocalDate startDate, LocalDate endDate, byte category, String[][] times, int[] ticketTotal);

     void init(LocalDate startDate, LocalDate endDate, byte category, String[][] times, int[] ticketTotal , String remark);

}
