package com.spup.service.impl;

import com.huangdou.commons.api.CommonResult;
import com.huangdou.commons.api.ResultCodeEnum;

import com.spup.db.dao.authority.AppCustomerDao;
import com.spup.db.entity.authority.AppCustomer;
import com.spup.service.IAppCustomerService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class AppCustomerServiceImpl implements IAppCustomerService {
    @Resource
    private AppCustomerDao appCustomerDao;

    @Override
    public AppCustomer get(String unionid) {
        Optional<AppCustomer> customerOptional = appCustomerDao.getByUnionid(unionid);
        if(!customerOptional.isPresent()){
            return null;
        }
        return customerOptional.get();
    }

    @Override
    public AppCustomer save(String unionid, String openid) {

        AppCustomer appCustomer = get(unionid);

        if(appCustomer==null){//首次登录

            appCustomer = new AppCustomer();
            appCustomer.setUnionid(unionid);
            appCustomer.setMiniOpenid(openid);

            AppCustomer customer = appCustomerDao.save(appCustomer);
            return customer;
        }
        return null;
    }

    @Override
    public AppCustomer save(String unionid, String openid,String userName,String avatar,byte gender) {

        AppCustomer appCustomer = get(unionid);

        if(appCustomer==null){//首次登录
            appCustomer = new AppCustomer();
            appCustomer.setUnionid(unionid);
            appCustomer.setMiniOpenid(openid);
            appCustomer.setUserGender(gender);
            appCustomer.setUserAvatarSrc(avatar);
            appCustomer.setUserName(userName);

            AppCustomer customer = appCustomerDao.save(appCustomer);
            return customer;
        } else {
            appCustomer.setUserGender(gender);
            appCustomer.setUserAvatarSrc(avatar);
            appCustomer.setUserName(userName);

            AppCustomer customer = appCustomerDao.save(appCustomer);
            return customer;
        }
    }

    @Override
    public CommonResult<?> update(AppCustomer customer) {
        //获取unionid
        String unionid = customer.getUnionid();
        //判断用户是否存在
        AppCustomer customerByUnionid = get(unionid);
        if(customerByUnionid==null){
            return CommonResult.failed(ResultCodeEnum.AUTHORIZED_FAILED);
        }
        customerByUnionid.setRealName(customer.getRealName());
        customerByUnionid.setPhone(customer.getPhone());
        customerByUnionid.setJob(customer.getJob());
        customerByUnionid.setUserGender(customer.getUserGender());
        customerByUnionid.setCardCategory(customer.getCardCategory());
        customerByUnionid.setCardNo(customer.getCardNo());
        AppCustomer update = appCustomerDao.save(customerByUnionid);
        return CommonResult.succeeded(update);
    }
}
