package com.spup.service.impl;


import com.spup.db.dao.sys.AppOperateLogDao;
import com.spup.db.entity.sys.AppOperateLog;
import com.spup.service.IAppOperateLogService;
import org.springframework.stereotype.Service;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import java.nio.charset.Charset;
import java.time.LocalDateTime;

@Service
public class AppOperateLogServiceImpl implements IAppOperateLogService {
    @Resource
    private AppOperateLogDao appOperateLogDao;
    @Override
    public AppOperateLog saveLog(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if(session == null){
            return null;
        }
        String unionid = (String)request.getSession(false).getAttribute("unionid");
        if(unionid!=null){
            AppOperateLog log = new AppOperateLog();
            log.setOperator(unionid);
            log.setOperateTime(LocalDateTime.now());
            log.setOperatorBrowser(request.getHeader("User-Agent"));
            log.setOperatorIp(getIpAddress(request));
            log.setOperateUrl(request.getRequestURI());
            String queryString = request.getQueryString();
            if(queryString == null || queryString.isEmpty()){
                queryString = getPostBody(request);
            }
            log.setOperateParams(queryString);
            return appOperateLogDao.save(log);
        }
        return null;
    }

    public String getIpAddress(HttpServletRequest request) {
        String ipAddress = request.getHeader("x-forwarded-for");
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }
        return ipAddress;
    }

    public String getPostBody(HttpServletRequest request) {
        String body = "";
        if (request != null && request instanceof ContentCachingRequestWrapper) {
            ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) request;
            body = new String(wrapper.getContentAsByteArray(), Charset.forName(wrapper.getCharacterEncoding()));
        }
        return body;
    }
}
