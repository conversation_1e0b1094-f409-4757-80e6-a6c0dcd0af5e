package com.spup.service.impl;


import com.spup.db.dao.AppVisitGuideDao;
import com.spup.db.entity.AppVisitGuide;
import com.spup.service.IAppVisitGuideService;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
public class AppVisitGuideServiceImpl implements IAppVisitGuideService {
    @Resource
    private AppVisitGuideDao appVisitGuideDao;
    @Override
    public List<AppVisitGuide> getAllList() {

        List<AppVisitGuide> appVisitGuideList = appVisitGuideDao.findAll(Sort.by("sortCode").ascending());
        return appVisitGuideList;
    }

    @Override
    public AppVisitGuide view(long id) {
        return null;
    }

    @Override
    public void read(long id) {
        Optional<AppVisitGuide> guideOpt = appVisitGuideDao.findById(id);
        if(!guideOpt.isPresent()){
            return;
        }
        AppVisitGuide appVisitGuide = guideOpt.get();
        appVisitGuide.setPageViews(appVisitGuide.getPageViews()+1);
        appVisitGuideDao.save(appVisitGuide);
    }
}
