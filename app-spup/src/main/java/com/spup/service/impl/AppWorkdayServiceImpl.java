package com.spup.service.impl;


import com.spup.db.dao.appointment.AppWorkdayDao;
import com.spup.db.entity.appointment.AppWorkday;
import com.spup.enums.WorkdayEnum;
import com.spup.service.IAppWorkdayService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class AppWorkdayServiceImpl implements IAppWorkdayService {

    @Resource
    private AppWorkdayDao appWorkdayDao;

    @Override
    public List<AppWorkday> getListByDate(String startDate, String endDate) {
        return appWorkdayDao.findByDayBetween(startDate,endDate);
    }

    @Override
    public  Optional<AppWorkday>  getByDate(String date) {
        return appWorkdayDao.getByDay(date);
    }

    @Override
    public boolean isWorkDay(String day) {
        Optional<AppWorkday> workdayOptional = appWorkdayDao.getByDay(day);
        if(!workdayOptional.isPresent()){
            return false;
        }
        AppWorkday workday = workdayOptional.get();
        boolean isWorkDay =   workday.getIsWorkday().byteValue() == WorkdayEnum.OPEN_DAY.getCode();
        return isWorkDay;
    }

    @Override
    public int insert(Date day) {
        // TODO

        return 0;
    }

    @Override
    public LocalDate getDate(LocalDate date, int plusDays,byte dayType) {
        int hasPlusDays = 0;
        while( hasPlusDays < plusDays ) {
            String yyyyMMdd = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            Optional<AppWorkday> workdayOptional = appWorkdayDao.getByDay(yyyyMMdd);
            AppWorkday byDate = workdayOptional.get();
            if( byDate.getIsWorkday().byteValue() == dayType ) {
                hasPlusDays++;
            }
            date = date.plus(1, ChronoUnit.DAYS);
        }

        return date;
    }

   /* public LocalDate getDate(LocalDate date, int plusDays,byte dayType) {
        int hasPlusDays = 0;
        while( hasPlusDays < plusDays ) {
            String yyyyMMdd = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            AppWorkday byDate = mapperHandler.getByDate(yyyyMMdd);
            if( byDate.getIsWorkday().byteValue() == dayType ) {
                hasPlusDays++;
            }
            date = date.plus(1, ChronoUnit.DAYS);
        }

        return date;
    }*/
}
