package com.spup.task;


import com.spup.controller.AppBatchController;
import com.spup.service.appointment.IAppAppointmentAnalysisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;


@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class AnalysisDailyTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Resource
    private IAppAppointmentAnalysisService iAppAppointmentAnalysisService;
    //3.添加定时任务
    @Scheduled(cron = "0 0 16 * * ?")
    protected void configureTasks() {
        logger.info("执行汇总每日数据");
        LocalDate date = LocalDate.now();
        String yyyyMMdd = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        Map<String,Object> dailyData =  iAppAppointmentAnalysisService.getAnaDataFromRecord(yyyyMMdd);
        iAppAppointmentAnalysisService.save(dailyData);
    }
}