package com.spup.task;


import com.huangdou.commons.utils.DateTimeUtil;
import com.spup.controller.AppBatchController;

import com.spup.db.dao.appointment.AppAppointmentItemOrderDao;
import com.spup.db.entity.appointment.AppAppointmentItemOrder;
import com.spup.enums.OrderStatusEnum;
import com.spup.service.appointment.IAppAppointmentItemOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


@Configuration
@EnableScheduling
public class CloseItemOrderTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Resource
    private IAppAppointmentItemOrderService iAppAppointmentItemOrderService;
    @Resource
    private AppAppointmentItemOrderDao appAppointmentItemOrderDao;
    //3.添加定时任务
    @Scheduled(cron = "0 3/5 10,11,14,15 * * ?")
    protected void configureTasks() {
        logger.info("执行定时清理未核销的展项订单");
        String yyyymmdd = DateTimeUtil.getSysTime(DateTimeUtil.PATTERN_7);
        String HHmm = DateTimeUtil.getSysTime("HHmm");
        List<AppAppointmentItemOrder> noCheckoutOrders = getNoCheckoutOrders(yyyymmdd);
        for (int i = 0; i < noCheckoutOrders.size(); i++) {
            AppAppointmentItemOrder order = noCheckoutOrders.get(i);
            if(order.getBatchEndTime().compareTo(HHmm)<0){
                iAppAppointmentItemOrderService.breaked(order.getOrderNo(),"CloseItemOrderTask");
            }
        }

    }

    private List<AppAppointmentItemOrder> getNoCheckoutOrders(String yyyymmdd){
        List<AppAppointmentItemOrder> byBatchDateBetween = appAppointmentItemOrderDao.findByBatchDateBetween(yyyymmdd, yyyymmdd);
        byBatchDateBetween =
                byBatchDateBetween.stream()
                .filter(itemOrder -> itemOrder.getOrderStatus().shortValue() == OrderStatusEnum.SUCCESS.getCode())
                .collect(Collectors.toList());

        return byBatchDateBetween;
    }
}