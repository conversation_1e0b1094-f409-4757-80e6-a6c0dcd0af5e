package com.spup.task;


import com.spup.controller.AppBatchController;
import com.spup.db.entity.appointment.AppBatch;
import com.spup.enums.BatchStatusEnum;
import com.spup.enums.OrderCategoryEnum;
import com.spup.service.appointment.IAppBatchService;
import com.spup.service.IAppConfigService;
import com.spup.service.IAppWorkdayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.List;
import java.util.Map;


@Configuration
@EnableScheduling
public class OpenBatchTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Resource
    private IAppBatchService iAppBatchService;
    @Resource
    private IAppWorkdayService iAppWorkdayService;
    @Resource
    private IAppConfigService iAppConfigService;

    //3.添加定时任务
    @Scheduled(cron = "0 0 9 * * ?")
    protected void configureTasks() {
        logger.info("执行定时开启门票场次");

        byte category = OrderCategoryEnum.TICKET.getCode();

        Map<String, Object> configsByGroup = iAppConfigService.getConfigsByGroup("appointment.category." + category);

        int offsetDay = Integer.parseInt((String) configsByGroup.get("offset.day"));
        int showDays = Integer.parseInt((String) configsByGroup.get("appointment.default_show_days"));
        // int appointmentDays = Integer.parseInt((String) configsByGroup.get("appointment.days"));
        int validDays = Integer.parseInt((String) configsByGroup.get("appointment.valid_days"));

        Calendar now = Calendar.getInstance();
        now.add(Calendar.DAY_OF_MONTH,offsetDay);
        Calendar end = Calendar.getInstance();
        end.add(Calendar.DAY_OF_MONTH,offsetDay);
        end.add(Calendar.DAY_OF_MONTH,showDays-1);
        int validDaysIndex = 0;
        while (!now.after(end)){
            Instant instant = now.getTime().toInstant();
            ZoneId zoneId = ZoneId.systemDefault();

            LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();

            String yyyyMMdd = localDateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if(iAppWorkdayService.isWorkDay(yyyyMMdd)){ //后续根据假期情况来判断
                if(validDaysIndex>=validDays){
                    break;
                }
                validDaysIndex++;
                Map<String, List<AppBatch>> listByDate = iAppBatchService.getListByDate(category, yyyyMMdd, yyyyMMdd);
                List<AppBatch> batchs = listByDate.get(yyyyMMdd);
                for (int i = 0; i < batchs.size() ; i++) {
                    AppBatch batch = batchs.get(i);
                    if(batch.getBatchStatus().byteValue() == BatchStatusEnum.CLOSED.getCode()) {
                        batch.setBatchStatus(BatchStatusEnum.RUNNING.getCode());
                        iAppBatchService.update(batch);
                    }
                }
            }
            now.add(Calendar.DAY_OF_MONTH, 1);
        }
    }

}
