package com.spup.task;


import com.huangdou.commons.utils.DateTimeUtil;
import com.spup.controller.AppBatchController;
import com.spup.db.entity.appointment.AppBatch;
import com.spup.enums.OrderCategoryEnum;
import com.spup.service.appointment.IAppBatchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Calendar;
import java.util.List;
import java.util.Map;


@Configuration
@EnableScheduling
public class OpenItemBatchTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Autowired
    private IAppBatchService iAppBatchService;
    //3.添加定时任务
    @Scheduled(cron = "0 0 9,12 * * ?")
    protected void configureTasks() {
        logger.info("执行定时开启展项场次");
        Calendar now = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        end.add(Calendar.HOUR_OF_DAY,4);
        String start = DateTimeUtil.getTime("yyyyMMdd", now);

        String hhmm = DateTimeUtil.getTime("HHmm",now);
        String hhmm_end = DateTimeUtil.getTime("HHmm",end);

        Map<String, List<AppBatch>> listByDateForFYPD = iAppBatchService.getListByDate(OrderCategoryEnum.ITEM_FYPD.getCode(), start, start);
        List<AppBatch> batchsForFYPD = listByDateForFYPD.get(start);

        for (int i = 0; i < batchsForFYPD.size() ; i++) {
            AppBatch batch = batchsForFYPD.get(i);
            if(batch.getBatchStartTime().compareTo(hhmm)>0 && batch.getBatchStartTime().compareTo(hhmm_end)<0){
                batch.setBatchStatus((byte)1);
                iAppBatchService.update(batch);
            }

        }

    }
}