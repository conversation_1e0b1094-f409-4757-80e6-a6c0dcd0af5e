server:
  port: 8080

spring:
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    # url: jdbc:mysql://***********:3306/web_spup_test?serverTimezone=Asia/Shanghai&useSSL=false&useUnicode=true&characterEncoding=UTF-8
    # username: root
    url: ********************************************************************************************************************************************************
    username: root
    password: 123456
    # password: 9a9*5b9*GGl1234gg
    # url: ENC(KMaps/3VOvTks9Uh8psRq3NJyCTl/uLilUxwWatsKkuetwAHxbh82NDAoze7LK8cS0Vb755Hrrm/Xb3AMCwXrh0pjEvnqSnZ8D5VDMCLCox7ByncVc1PPXd8sWAiWKwjdq2jSePS1xF+05iFe2Q324HvSseTYntqX7zoD2r6htzzSrfkXbkl6/d/gbnB/DsNAdMmkO7VTG8GYRCUC4Pkyw==)
    # username: ENC(omRqLbdy2HHFc1CFs/Tx32xnKse0WctI2pspDDtqRwmLymmxDNSWupyUqLPqjGpR)
    # password: ENC(Fw7R5/40E7ay4EgRV/68LVCM7qKlCMOB63rwS1ZVrOkHcfOwaLkP225C5k7DBIWMsVq6oNJ5/7L9tICOCC85Rw==)
  # sql:
  #   init:
  #     mode: always
  #     schema-locations: classpath:schema.sql
  jmx:
    enabled: false

  jpa:
    show-sql: true
    hibernate:
      #When you use @Enumerated(EnumType.STRING) with JPA, the enum values are stored as strings in the database. Hibernate's update mode doesn't modify existing enum constraints or update the allowed values for enum columns.
      ddl-auto: update  # Disable schema validation, in production, should set 'none'
    #关闭懒加载 否则通过id查询有问题
    properties:
      hibernate:
        "[enable_lazy_load_no_trans]": false
        dialect: org.hibernate.dialect.MySQL8Dialect
        "[format_sql]": true
    generate-ddl: true
    database-platform: org.hibernate.dialect.MySQL8Dialect

turnstiles:
  suppliers:
    - name: xiansong
      signKey: isIBNIZxiansong4
    - name: test
      signKey: 9sblTbo7u3Ntestb
