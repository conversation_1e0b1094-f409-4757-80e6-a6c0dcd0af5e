server:
  port: 8080

spring:
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************
    username: root
    password: 123456
  jmx:
    enabled: false

  jpa:
    show-sql: true
    hibernate:
      #When you use @Enumerated(EnumType.STRING) with JPA, the enum values are stored as strings in the database. Hibernate's update mode doesn't modify existing enum constraints or update the allowed values for enum columns.
      ddl-auto: update  # Disable schema validation, in production, should set 'none'
    #关闭懒加载 否则通过id查询有问题
    properties:
      hibernate:
        "[enable_lazy_load_no_trans]": false
        dialect: org.hibernate.dialect.MySQL8Dialect
        "[format_sql]": true
    generate-ddl: true
    database-platform: org.hibernate.dialect.MySQL8Dialect

turnstiles:
  suppliers:
    - name: <PERSON><PERSON><PERSON>
      signKey: E7qO2xPQaKxl9w6KP5mA




