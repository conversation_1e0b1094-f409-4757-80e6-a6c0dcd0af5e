#!/bin/bash

# Authentication options - uncomment and fill in the one you want to use

# Option 1: JWT Token
JWT_TOKEN="your_actual_jwt_token"
AUTH_HEADER="Authorization: Bearer $JWT_TOKEN"

# Option 2: Basic Authentication
# USERNAME="your_username"
# PASSWORD="your_password"
# AUTH_OPTION="-u $USERNAME:$PASSWORD"
# AUTH_HEADER=""

echo "Attempting to connect to the API..."
TASK_ID=$(curl -v -X POST 'http://localhost:8080/spup/api/tasks/one-time' \
-H "Authorization: Bearer $JWT_TOKEN" \
-H 'Content-Type: application/json' \
-d '{
  "executeTime": "2025-05-15T21:27:00Z",
  "taskType": "OVERWRITE_ITEM_BATCH",
  "taskClass": "com.spup.task.BatchOverwriteTask",
  "taskData": "[{\"batchNo\":\"20240515001\",\"batchDate\":\"20240515\",\"batchStartTime\":\"0900\",\"batchEndTime\":\"1700\",\"ticketTotal\":200,\"ticketRemaining\":200,\"batchStatus\":1,\"batchCategory\":4,\"batchRemark\":\"Overwrite test batch\"}]"
}' | jq -r '.taskId')

echo "Created task with ID: $TASK_ID"

# Wait for a moment to let the task execute
sleep 5

# Check task status
curl -v ${AUTH_OPTION:-} ${AUTH_HEADER:+-H "$AUTH_HEADER"} \
-X GET "http://localhost:8080/spup/api/tasks/$TASK_ID"