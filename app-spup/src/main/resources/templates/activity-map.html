<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动地图</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            background: #f0f0f0;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-width: 100%;
            margin: 0 auto;
        }
        .activity-map, .schedule-table {
            width: 100%;
        }
        img {
            max-width: 100%;
            height: auto;
            display: block;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="activity-map">
        <img th:src="@{/images/activity-map.jpg}" alt="活动地图">
    </div>
    <div class="schedule-table">
        <img th:src="@{/images/schedule-table.jpg}" alt="活动日程">
    </div>

</body>
</html>