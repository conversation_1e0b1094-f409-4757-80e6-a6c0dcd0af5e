#!/bin/bash

# Replace with your actual authentication credentials
USERNAME="admin"
PASSWORD="admin"

# Get a JWT token (this is a placeholder - you'll need to implement this based on your authentication system)
# JWT_TOKEN=$(curl -s -X POST "http://localhost:8080/spup/api/auth/login" -H "Content-Type: application/json" -d '{"username":"'$USERNAME'","password":"'$PASSWORD'"}' | jq -r '.token')

# For testing, we'll use Basic Auth instead of JWT
AUTH_HEADER="Authorization: Basic $(echo -n $USERNAME:$PASSWORD | base64)"

echo "Creating a task with OPEN_TEAM_BATCH type..."
TASK_ID=$(curl -s -X POST "http://localhost:8080/spup/api/tasks/one-time" \
  -H "$AUTH_HEADER" \
  -H "Content-Type: application/json" \
  -d '{
    "executeTime": "2024-05-14T14:42:00Z",
    "taskType": "OPEN_TEAM_BATCH",
    "taskClass": "com.spup.task.OpenTeamBatchTask",
    "taskData": "[{\"batchNo\":\"20240514001\",\"batchDate\":\"20240514\",\"batchStartTime\":\"0900\",\"batchEndTime\":\"1700\",\"ticketTotal\":200,\"ticketRemaining\":200,\"batchStatus\":1,\"batchCategory\":4,\"batchRemark\":\"Test batch\"}]"
  }' | jq -r '.taskId')

echo "Created task with ID: $TASK_ID"

# Wait for a moment to let the task execute
sleep 2

echo "Checking task status..."
curl -s -X GET "http://localhost:8080/spup/api/tasks/$TASK_ID" \
  -H "$AUTH_HEADER"
