package com.spup.controller;

import com.spup.db.entity.OneTimeTask;
import com.spup.db.entity.OneTimeTask.TaskStatusEnum;
import com.spup.db.repository.OneTimeTaskRepository;
import com.spup.service.appointment.IAppBatchService;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.TaskScheduler;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SimpleControllerTest {

    @Mock
    private TaskScheduler taskScheduler;

    @Mock
    private OneTimeTaskRepository taskRepository;

    @Mock
    private IAppBatchService batchService;

    @InjectMocks
    private PersistentTaskController persistentTaskController;

    @Test
    public void testScheduleOneTimeTask_PastTime() {
        // Arrange
        Instant pastTime = Instant.now().minus(1, ChronoUnit.HOURS);
        PersistentTaskController.ScheduleRequest request = new PersistentTaskController.ScheduleRequest();
        request.setExecuteTime(pastTime);
        // We don't set the task type to avoid enum issues

        // Act
        ResponseEntity<?> response = persistentTaskController.scheduleOneTimeTask(request);

        // Assert
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        Assertions.assertEquals("执行时间不能早于当前时间", response.getBody());

        // Verify that no task was saved
        verify(taskRepository, never()).save(any(OneTimeTask.class));
    }

    @Test
    public void testCancelTask_NotFound() {
        // Arrange
        String taskId = "non-existent-id";
        when(taskRepository.findById(taskId)).thenReturn(Optional.empty());

        // Act
        ResponseEntity<?> response = persistentTaskController.cancelTask(taskId);

        // Assert
        Assertions.assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());

        // Verify
        verify(taskRepository).findById(taskId);
        verify(taskRepository, never()).save(any(OneTimeTask.class));
    }

    @Test
    public void testCancelTask_NotPending() {
        // Arrange
        String taskId = "executing-task-id";
        OneTimeTask task = new OneTimeTask();
        task.setId(taskId);
        task.setStatus(TaskStatusEnum.EXECUTING);

        when(taskRepository.findById(taskId)).thenReturn(Optional.of(task));

        // Act
        ResponseEntity<?> response = persistentTaskController.cancelTask(taskId);

        // Assert
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        Assertions.assertEquals("任务已执行，无法取消", response.getBody());

        // Verify
        verify(taskRepository).findById(taskId);
        verify(taskRepository, never()).save(any(OneTimeTask.class));
    }

    @Test
    public void testCancelTask_Success() {
        // Arrange
        String taskId = "pending-task-id";
        OneTimeTask task = new OneTimeTask();
        task.setId(taskId);
        task.setStatus(TaskStatusEnum.PENDING);

        when(taskRepository.findById(taskId)).thenReturn(Optional.of(task));

        // Act
        ResponseEntity<?> response = persistentTaskController.cancelTask(taskId);

        // Assert
        Assertions.assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());

        // Verify
        verify(taskRepository).findById(taskId);
        verify(taskRepository).save(any(OneTimeTask.class));
    }


}
