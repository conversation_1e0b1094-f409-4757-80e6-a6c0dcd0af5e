-- 达梦数据库 DM_SQL 兼容脚本 (语法修复版)
-- 基于达梦数据库官方语法规范转换
-- 修复了注释语法问题

-- 表: app_activity
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT NOT NULL,
  address VARCHAR(255) DEFAULT NULL,
  content VARCHAR(255) DEFAULT NULL,
  conver_picture VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  end_time DATETIME DEFAULT NULL,
  sort INT DEFAULT NULL,
  start_time DATETIME DEFAULT NULL,
  status INT DEFAULT NULL,
  title VARCHAR(255) DEFAULT NULL,
  type TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 注释说明:
-- id: 主键，原MySQL的AUTO_INCREMENT，建议创建序列实现自增
-- address: 活动地点
-- content: 活动内容  
-- conver_picture: 活动封面图
-- end_time: 活动结束时间
-- sort: 排序值
-- start_time: 活动开始时间
-- status: 活动状态
-- title: 活动标题
-- type: 活动类型

