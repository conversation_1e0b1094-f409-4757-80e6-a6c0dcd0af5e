from flask import Flask, render_template_string, request, send_file, url_for, redirect
import hashlib
import os
import base64

import flask

app = Flask(__name__)
TOKEN = "anniversary"  # 需与公众平台配置的Token一致

# Add HTML template
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>活动地图</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: #f0f0f0;
        }
        img {
            max-width: 100%;
            height: auto;
            display: block;
        }
    </style>
</head>
<body>
    <img src="{{ url_for('serve_image', openid=openid) }}" alt="活动地图">
</body>
</html>
"""

@app.route('/mp/test/callback', methods=['GET', 'POST'])
def wechat():
    if request.method == 'GET':
        # 接收微信服务器发送的参数
        signature = request.args.get('signature', '')
        timestamp = request.args.get('timestamp', '')
        nonce = request.args.get('nonce', '')
        echostr = request.args.get('echostr', '')


        # print url and params
        
        # 验证逻辑
        try:
            # 对token、timestamp、nonce按字典排序并拼接
            tmp_list = [TOKEN, timestamp, nonce]
            tmp_list.sort()
            tmp_str = ''.join(tmp_list)
            
            # 进行SHA1加密
            tmp_str = hashlib.sha1(tmp_str.encode('utf-8')).hexdigest()
            
            # 与微信提供的签名进行比较
            if tmp_str == signature:
                # 验证成功，返回echostr
                return echostr
            else:
                return 'Invalid signature', 403
        except Exception as e:
            return str(e), 500
    else:
        # 处理POST请求（后续消息处理）
        print("url:", request.url)
        print("params:", request.args)
        openid = request.args.get('openid', '')
        print(f"Received POST from openid: {openid}")
        # Redirect to a new URL which is only a picture
        return redirect(url_for('image_page', openid=openid, _external=True, _method='GET'))

@app.route('/image', methods=['GET', 'POST'])  # 改为GET方法
def image_page():
    try:
        openid = request.args.get('openid', '')
        print(f"Serving image for openid: {openid}")
        image_path = os.path.join(os.path.dirname(__file__), 'activity-map.jpg')
        print(image_path)
        return render_template_string(HTML_TEMPLATE, openid=openid)
    except Exception as e:
        print(f"Error serving image: {str(e)}")
        return str(e), 500


@app.route('/serve-image')
def serve_image():
    try:
        openid = request.args.get('openid', '')
        print(f"Serving image for openid: {openid}")
        image_path = os.path.join(os.path.dirname(__file__), 'activity-map.jpg')
        return send_file(image_path, mimetype='image/jpeg')
    except Exception as e:
        print(f"Error serving image: {str(e)}")
        return str(e), 500

if __name__ == '__main__':
    dir_path = 'relletest.douwifi.cn_other'
    print(os.path.dirname(__file__))
    base_path = os.path.abspath(os.path.dirname(__file__))
    print(f"Base path: {base_path}")
    
    crt_path = os.path.join(base_path, dir_path, 'relletest.douwifi.cn_bundle.crt')
    key_path = os.path.join(base_path, dir_path, 'relletest.douwifi.cn.key')
    print(f"Certificate path: {crt_path}")
    print(f"Key path: {key_path}")
    app.run(host='0.0.0.0', port=443, ssl_context=(crt_path, key_path))  # 使用HTTPS