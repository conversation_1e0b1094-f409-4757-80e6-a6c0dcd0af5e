#!/bin/bash
# Clean corrupted Maven repository files

# Remove Spring Cloud related files
rm -rf ~/.m2/repository/org/springframework/cloud/

# Remove corrupted BOM files
rm -rf ~/.m2/repository/com/datastax/oss/java-driver-bom/
rm -rf ~/.m2/repository/io/dropwizard/metrics/metrics-bom/
rm -rf ~/.m2/repository/org/infinispan/infinispan-bom/
rm -rf ~/.m2/repository/io/micrometer/micrometer-bom/
rm -rf ~/.m2/repository/io/netty/netty-bom/
rm -rf ~/.m2/repository/com/oracle/database/jdbc/ojdbc-bom/
rm -rf ~/.m2/repository/io/prometheus/simpleclient_bom/
rm -rf ~/.m2/repository/io/r2dbc/r2dbc-bom/
rm -rf ~/.m2/repository/io/projectreactor/reactor-bom/
rm -rf ~/.m2/repository/org/springframework/data/spring-data-bom/
rm -rf ~/.m2/repository/org/springframework/spring-framework-bom/
rm -rf ~/.m2/repository/org/springframework/security/spring-security-bom/
rm -rf ~/.m2/repository/org/springframework/session/spring-session-bom/

echo "Maven repository cleaned successfully!"
