package com.huangdou.commons.ssbapi;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.extern.slf4j.Slf4j;

import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Date;

import javax.annotation.Resource;

@Slf4j
@Component
public class SSBApi {
    private ObjectMapper objectMapper = new ObjectMapper();
    @Resource
    private RestTemplate restTemplate;

    public AccessToken getTokenFromApi(String code) throws JsonProcessingException, JsonMappingException {
        Date now = new Date();
        StringBuffer url = new StringBuffer("http://api.eshimin.com/api/oauth/token?t="+now.getTime());
        url.append("&client_id="+ApiParamsConstant.getClientId());
        url.append("&client_secret="+ApiParamsConstant.getSecret());
        url.append("&grant_type=authorization_code");
        url.append("&code="+code);
        url.append("&redirect_uri="+ ApiParamsConstant.getRegisteredRedirectUri());

        System.out.println("ssb-url:"+url);

        //String response = HttpRequestUtil.httpGet(url.toString());
        ResponseEntity<String> responseEntity  = restTemplate.getForEntity(url.toString(), String.class);

        log.info(responseEntity.getBody());
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        AccessToken accessToken = objectMapper.readValue(responseEntity.getBody(),AccessToken.class);

        accessToken.setValidDate(new Date(now.getTime()+accessToken.getExpiresIn()-300));
        return  accessToken;

    }

    public String getBasicUser(String user,String token) throws JsonProcessingException, JsonMappingException{
        String url = "http://api.eshimin.com/api/v2/user/basic/"+user+"?access_token="+token;
        ResponseEntity<String> responseEntity  = restTemplate.getForEntity(url, String.class);
        log.info(responseEntity.getBody());
        ObjectNode obj = (ObjectNode)objectMapper.readTree(responseEntity.getBody());
        if(obj.get("success").booleanValue()){
            return obj.get("data").get("userId").asText();
        }
        return null;
    }

}
