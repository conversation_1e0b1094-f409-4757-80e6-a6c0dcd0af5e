package com.huangdou.commons.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.Calendar;
import java.util.HashMap;

@Component
@PropertySource("classpath:/jwtConifg.properties")
public class JWTUtil {

    @Value("${jwt.sign.string}")
    private String signStr;


    public String getToken(String unionid,String openid) throws UnsupportedEncodingException {
        return  getToken(unionid,openid,120);
    }

    public String getToken(String unionid,String openid,int validMinute) throws UnsupportedEncodingException {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, validMinute);

        String token = JWT.create()
                .withHeader(new HashMap<>())  // Header
                .withClaim("unionid", unionid)  // Payload
                .withClaim("openid", openid)
                .withExpiresAt(calendar.getTime())  // 过期时间
                .sign(Algorithm.HMAC384(signStr));  // 签名用的secret
        return token;
    }

    /**
     * 1有效token,0,非法token，-1过期token
     * @param token
     * @return
     * @throws UnsupportedEncodingException
     */
    public Integer verifierToken(String token) throws UnsupportedEncodingException {
        JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC384(signStr)).build();
        try {
            jwtVerifier.verify(token);
        }catch (JWTDecodeException e){
            return -1;
        }catch (TokenExpiredException e){
            return -2;
        }
        return 0;
    }

    public DecodedJWT decodeToken(String token) throws UnsupportedEncodingException {
        JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC384(signStr)).build();
        DecodedJWT decodedJWT = null;

        decodedJWT =  jwtVerifier.verify(token);

        return decodedJWT;
    }
}
