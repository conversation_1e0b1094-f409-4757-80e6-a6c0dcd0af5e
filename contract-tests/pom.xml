<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.huangdou</groupId>
        <artifactId>spup-root</artifactId>
        <version>0.2</version>
    </parent>

    <artifactId>contract-tests</artifactId>
    <version>0.1</version>

    <properties>
        <spring-cloud-contract.version>3.1.3</spring-cloud-contract.version>
        <spring-restdocs.version>2.0.7.RELEASE</spring-restdocs.version>
        <rest-assured.version>4.5.1</rest-assured.version>
        <spring-boot.version>2.7.13</spring-boot.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2021.0.3</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- Reference to your existing modules -->
        <dependency>
            <groupId>com.huangdou</groupId>
            <artifactId>spup</artifactId>
            <version>0.2</version>
        </dependency>
        <dependency>
            <groupId>com.huangdou</groupId>
            <artifactId>spup-admin</artifactId>
            <version>0.2</version>
        </dependency>

        <!-- Spring Cloud Contract -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-contract-verifier</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Spring REST Docs -->
        <dependency>
            <groupId>org.springframework.restdocs</groupId>
            <artifactId>spring-restdocs-mockmvc</artifactId>
            <version>${spring-restdocs.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- Test dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>${spring-boot.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>rest-assured</artifactId>
            <version>${rest-assured.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>spring-mock-mvc</artifactId>
            <version>${rest-assured.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-contract-maven-plugin</artifactId>
                    <version>${spring-cloud-contract.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-contract-maven-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <baseClassForTests>com.spup.contract.BaseContractTest</baseClassForTests>
                    <testFramework>JUNIT5</testFramework>
                    <basePackageForTests>com.spup.contract</basePackageForTests>
                    <packageWithBaseClasses>com.spup.contract</packageWithBaseClasses>
                    <contractsDirectory>src/test/resources/contracts</contractsDirectory>
                    <testMode>EXPLICIT</testMode>
                    <contractsRepositoryUrl>file://${project.basedir}/src/test/resources/contracts</contractsRepositoryUrl>
                    <contractDependency>
                        <groupId>${project.groupId}</groupId>
                        <artifactId>${project.artifactId}</artifactId>
                        <version>${project.version}</version>
                    </contractDependency>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.asciidoctor</groupId>
                <artifactId>asciidoctor-maven-plugin</artifactId>
                <version>2.2.1</version>
                <executions>
                    <execution>
                        <id>generate-docs</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>process-asciidoc</goal>
                        </goals>
                        <configuration>
                            <backend>html5</backend>
                            <sourceDirectory>${project.basedir}/src/main/asciidoc</sourceDirectory>
                            <outputDirectory>${project.build.directory}/generated-docs</outputDirectory>
                            <attributes>
                                <snippets>${project.build.directory}/generated-snippets</snippets>
                                <source-highlighter>highlight.js</source-highlighter>
                                <toc>left</toc>
                                <toclevels>3</toclevels>
                                <sectlinks>true</sectlinks>
                            </attributes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
