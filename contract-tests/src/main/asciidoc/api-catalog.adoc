# API Catalog

## Customer-Facing APIs (app-spup)
1. GET /api/appointments/{id} - Get appointment details
2. POST /api/appointments/book - Book a new appointment
3. GET /api/appointments/list - List user appointments
4. GET /api/user/profile - Get user profile
5. POST /api/user/register - Register new user

## Admin APIs (app-spup-admin)
1. GET /manage/appointments - List all appointments
2. PUT /manage/appointments/{id}/status - Update appointment status
3. GET /manage/users - List all users
4. GET /manage/statistics - Get system statistics