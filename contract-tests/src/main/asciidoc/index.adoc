// contract-tests/src/main/asciidoc/index.adoc
= SPUP API Documentation
:doctype: book
:icons: font
:source-highlighter: highlightjs
:toc: left
:toclevels: 4
:sectlinks:

== Introduction

This document provides documentation for the SPUP API. It is generated automatically from the API contracts.

== API Catalog

For a complete list of all available APIs, see the link:api-catalog.html[API Catalog].

== Customer-Facing APIs

=== Appointments

==== Get Appointment Details

include::{snippets}/contracts/app/appointments/get_appointment_details/http-request.adoc[]
include::{snippets}/contracts/app/appointments/get_appointment_details/http-response.adoc[]

==== Book Appointment

include::{snippets}/contracts/app/appointments/book_appointment/http-request.adoc[]
include::{snippets}/contracts/app/appointments/book_appointment/http-response.adoc[]

== Admin APIs

=== Appointments Management

==== List Appointments

include::{snippets}/contracts/admin/appointments/list_appointments/http-request.adoc[]
include::{snippets}/contracts/admin/appointments/list_appointments/http-response.adoc[]

==== Update Appointment Status

include::{snippets}/contracts/admin/appointments/update_appointment_status/http-request.adoc[]
include::{snippets}/contracts/admin/appointments/update_appointment_status/http-response.adoc[]
