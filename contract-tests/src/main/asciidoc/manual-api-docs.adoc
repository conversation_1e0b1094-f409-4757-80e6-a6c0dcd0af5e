# SPUP API Documentation

## Common Response Format

All API responses follow a common format:

```json
{
  "status": boolean,       // true for success, false for failure
  "code": number,          // HTTP status code
  "message": string,       // Human-readable message
  "data": object|null      // Response data (null for error responses)
}
```

## Common Error Responses

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - The request was invalid or cannot be served |
| 401 | Unauthorized - Authentication is required or has failed |
| 403 | Forbidden - The server understood the request but refuses to authorize it |
| 404 | Not Found - The requested resource could not be found |
| 409 | Conflict - The request could not be completed due to a conflict with the current state of the resource |
| 500 | Internal Server Error - An unexpected condition was encountered |

## Data Validation Rules

### Phone Number Format
- Must be a Chinese mobile phone number
- Must start with 1, followed by a digit between 3-9, and then 9 more digits
- Regex pattern: `^1[3-9]\\d{9}$`
- Example: `13800138000`

### ID Card Format
- Must be a valid Chinese ID card number
- 18 digits in the format: RRRRRRYYYYMMDDSSSC
  - RRRRRR: 6-digit region code
  - YYYYMMDD: 8-digit birth date
  - SSS: 3-digit sequence code
  - C: 1-digit check code (can be 0-9 or X)
- Regex pattern: `^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$`
- Example: `110101199001011234`

### Appointment Status Values
- Valid values: `CONFIRMED`, `CANCELLED`, `COMPLETED`, `NO_SHOW`

## App API Endpoints

### Get Appointment Details

Retrieves the details of a specific appointment.

**Request:**
```
GET /api/appointments/{id}
Accept: application/json
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the appointment |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "id": 123,
    "orderNo": "ORDER123456789012",
    "batchDate": "2023-10-15",
    "batchStartTime": "10:00",
    "batchEndTime": "11:00",
    "status": "CONFIRMED"
  }
}
```

**Error Responses:**

*Appointment Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Appointment not found"
}
```

*Unauthorized Access (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Unauthorized access"
}
```

### Book Appointment

Books a new appointment for a specific batch.

**Request:**
```
POST /api/appointments/book
Content-Type: application/json
Accept: application/json

{
  "batchId": 789,
  "contactName": "John Doe",
  "contactPhone": "13800138000",
  "idCardType": 1,
  "idCardNo": "110101199001011234"
}
```

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| batchId | Integer | Yes | The ID of the batch to book | Must be a valid batch ID |
| contactName | String | Yes | The name of the contact person | |
| contactPhone | String | Yes | The phone number of the contact person | Must match the phone number format |
| idCardType | Integer | Yes | The type of ID card (1 for Chinese ID) | |
| idCardNo | String | Yes | The ID card number | Must match the ID card format |

**Success Response (201 Created):**
```json
{
  "status": true,
  "code": 200,
  "message": "Appointment booked successfully",
  "data": {
    "orderNo": "ORDER123456789012",
    "batchDate": "2023-10-20",
    "batchStartTime": "14:00",
    "batchEndTime": "15:00",
    "status": "CONFIRMED"
  }
}
```

**Error Responses:**

*Missing Required Fields (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Missing required fields"
}
```

*Invalid Phone Number Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid phone number format"
}
```

*Invalid ID Card Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid ID card format"
}
```

*Batch Not Available (409 Conflict):*
```json
{
  "status": false,
  "code": 409,
  "message": "Batch is not available"
}
```

## Admin API Endpoints

### List Appointments

Retrieves a paginated list of appointments with optional filtering.

**Request:**
```
GET /manage/appointments?page=0&size=10&status=CONFIRMED&date=2023-10-15
Accept: application/json
```

**Query Parameters:**
| Parameter | Type | Required | Default | Description | Validation |
|-----------|------|----------|---------|-------------|------------|
| page | Integer | No | 0 | The page number (zero-based) | Must be >= 0 |
| size | Integer | No | 10 | The page size | Must be <= 100 |
| status | String | No | | Filter by appointment status | Must be a valid status value |
| date | String | No | | Filter by appointment date (YYYY-MM-DD) | |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [
      {
        "id": 123,
        "orderNo": "ORDER123456789012",
        "batchDate": "2023-10-15",
        "batchStartTime": "10:00",
        "batchEndTime": "11:00",
        "status": "CONFIRMED",
        "contactName": "John Doe",
        "contactPhone": "13800138000"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

**Success Response - Empty Result (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [],
    "totalElements": 0,
    "totalPages": 0,
    "size": 10,
    "number": 0
  }
}
```

**Error Responses:**

*Unauthorized Access (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Unauthorized access"
}
```

*Invalid Page Size (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Page size must not be greater than 100"
}
```

### Update Appointment Status

Updates the status of an existing appointment.

**Request:**
```
PUT /manage/appointments/{id}/status
Content-Type: application/json
Accept: application/json

{
  "status": "CANCELLED",
  "reason": "Customer request"
}
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the appointment |

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| status | String | Yes | The new status of the appointment | Must be a valid status value |
| reason | String | No | The reason for the status change | |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Status updated successfully",
  "data": {
    "id": 123,
    "status": "CANCELLED"
  }
}
```

**Error Responses:**

*Appointment Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Appointment not found"
}
```

*Missing Status (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Status is required"
}
```

*Invalid Status Value (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid status value. Must be one of: [CONFIRMED, CANCELLED, COMPLETED, NO_SHOW]"
}
```

*Invalid Status Transition (409 Conflict):*
```json
{
  "status": false,
  "code": 409,
  "message": "Cannot transition from CANCELLED to COMPLETED"
}
```
