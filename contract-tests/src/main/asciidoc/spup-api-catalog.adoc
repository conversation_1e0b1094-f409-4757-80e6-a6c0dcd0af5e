= SPUP API Catalog
:doctype: book
:icons: font
:source-highlighter: highlightjs
:toc: left
:toclevels: 3
:sectlinks:

== Introduction

This catalog provides an overview of all APIs available in the SPUP system. It serves as a reference for both the customer-facing and admin applications.

== Customer-Facing APIs (app-spup)

=== Appointment APIs

[cols="2,3,5"]
|===
|Method|Endpoint|Description

|GET
|/api/appointments/{id}
|Get appointment details by ID

|POST
|/api/appointments/book
|Book a new appointment

|GET
|/api/appointments/list
|List user appointments

|DELETE
|/api/appointments/{id}/cancel
|Cancel an existing appointment
|===

=== User APIs

[cols="2,3,5"]
|===
|Method|Endpoint|Description

|GET
|/api/user/profile
|Get user profile information

|POST
|/api/user/register
|Register a new user

|PUT
|/api/user/profile
|Update user profile information
|===

=== Turnstiles APIs

[cols="2,3,5"]
|===
|Method|Endpoint|Description

|POST
|/api/turnstiles/checkout
|Process turnstile checkout

|GET
|/api/turnstiles/suppliers
|Get list of turnstile suppliers
|===

== Admin APIs (app-spup-admin)

=== Appointment Management

[cols="2,3,5"]
|===
|Method|Endpoint|Description

|GET
|/manage/appointments
|List all appointments with filtering and pagination

|PUT
|/manage/appointments/{id}/status
|Update appointment status

|GET
|/manage/appointments/{id}
|Get detailed appointment information

|POST
|/manage/appointments/batch
|Create appointment batch
|===

=== User Management

[cols="2,3,5"]
|===
|Method|Endpoint|Description

|GET
|/manage/users
|List all users with filtering and pagination

|GET
|/manage/users/{id}
|Get detailed user information

|PUT
|/manage/users/{id}/status
|Update user status
|===

=== System Management

[cols="2,3,5"]
|===
|Method|Endpoint|Description

|GET
|/manage/statistics
|Get system statistics

|GET
|/manage/config
|Get system configuration

|PUT
|/manage/config
|Update system configuration
|===

== Integration with Contract Testing

Each API listed in this catalog has corresponding contract tests that verify its behavior. The contracts serve as the source of truth for API behavior and are used to generate both tests and documentation.

To see detailed documentation for each API, refer to the specific API documentation sections.