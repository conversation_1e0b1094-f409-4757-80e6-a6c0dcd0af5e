// contract-tests/src/test/java/com/spup/contract/admin/AdminBaseContractTest.java
package com.spup.contract.admin;

import com.spup.SPUPApplication;
import com.spup.contract.BaseContractTest;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(
    classes = SPUPApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = {
        "spring.profiles.active=test",
        "spring.jpa.hibernate.ddl-auto=create-drop",
        "spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1",
        "spring.datasource.username=sa",
        "spring.datasource.password=",
        "spring.datasource.driver-class-name=org.h2.Driver"
    }
)
public abstract class AdminBaseContractTest extends BaseContractTest {
    // Additional setup specific to app-spup-admin
}