package com.spup.contract.admin;

import io.restassured.module.mockmvc.RestAssuredMockMvc;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * Contract tests for the appointment management API
 */
public class AppointmentManagementContractTest {

    private MockMvc mockMvc;
    private AppointmentManagementController appointmentManagementController;

    @BeforeEach
    public void setup() {
        appointmentManagementController = new AppointmentManagementController();
        mockMvc = MockMvcBuilders.standaloneSetup(appointmentManagementController).build();
        RestAssuredMockMvc.mockMvc(mockMvc);
    }

    // Success scenarios

    @Test
    public void shouldListAppointments() throws Exception {
        mockMvc.perform(get("/manage/appointments")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].id").value(123))
                .andExpect(jsonPath("$.data.content[0].orderNo").value("ORDER123456789012"))
                .andExpect(jsonPath("$.data.content[0].batchDate").value("2023-10-15"))
                .andExpect(jsonPath("$.data.content[0].batchStartTime").value("10:00"))
                .andExpect(jsonPath("$.data.content[0].batchEndTime").value("11:00"))
                .andExpect(jsonPath("$.data.content[0].status").value("CONFIRMED"))
                .andExpect(jsonPath("$.data.content[0].contactName").value("John Doe"))
                .andExpect(jsonPath("$.data.content[0].contactPhone").value("13800138000"))
                .andExpect(jsonPath("$.data.totalElements").value(1))
                .andExpect(jsonPath("$.data.totalPages").value(1))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));
    }

    @Test
    public void shouldUpdateAppointmentStatus() throws Exception {
        String requestBody = "{\n" +
                "  \"status\": \"CANCELLED\",\n" +
                "  \"reason\": \"Customer request\"\n" +
                "}";

        mockMvc.perform(put("/manage/appointments/123/status")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Status updated successfully"))
                .andExpect(jsonPath("$.data.id").value(123))
                .andExpect(jsonPath("$.data.status").value("CANCELLED"));
    }

    // Edge cases

    @Test
    public void shouldReturnEmptyListWhenNoAppointmentsMatchCriteria() throws Exception {
        mockMvc.perform(get("/manage/appointments")
                .param("page", "0")
                .param("size", "10")
                .param("status", "COMPLETED")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content").isEmpty())
                .andExpect(jsonPath("$.data.totalElements").value(0))
                .andExpect(jsonPath("$.data.totalPages").value(0))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));
    }

    // Error scenarios

    @Test
    public void shouldReturnUnauthorizedWhenAccessingWithInvalidCredentials() throws Exception {
        mockMvc.perform(get("/manage/appointments")
                .param("page", "-1")  // Trigger unauthorized scenario
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnBadRequestWhenPageSizeExceedsLimit() throws Exception {
        mockMvc.perform(get("/manage/appointments")
                .param("page", "0")
                .param("size", "101")  // Exceeds limit of 100
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Page size must not be greater than 100"));
    }

    @Test
    public void shouldReturnNotFoundWhenAppointmentDoesNotExist() throws Exception {
        String requestBody = "{\n" +
                "  \"status\": \"CANCELLED\",\n" +
                "  \"reason\": \"Customer request\"\n" +
                "}";

        mockMvc.perform(put("/manage/appointments/999/status")  // Non-existent appointment
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Appointment not found"));
    }

    @Test
    public void shouldReturnBadRequestWhenStatusIsMissing() throws Exception {
        String requestBody = "{\n" +
                "  \"reason\": \"Customer request\"\n" +
                "}";

        mockMvc.perform(put("/manage/appointments/123/status")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Status is required"));
    }

    @Test
    public void shouldReturnBadRequestWhenStatusIsInvalid() throws Exception {
        String requestBody = "{\n" +
                "  \"status\": \"INVALID_STATUS\",\n" +
                "  \"reason\": \"Customer request\"\n" +
                "}";

        mockMvc.perform(put("/manage/appointments/123/status")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid status value. Must be one of: [CONFIRMED, CANCELLED, COMPLETED, NO_SHOW]"));
    }

    @Test
    public void shouldReturnConflictWhenStatusTransitionIsInvalid() throws Exception {
        String requestBody = "{\n" +
                "  \"status\": \"COMPLETED\",\n" +
                "  \"reason\": \"Customer request\"\n" +
                "}";

        mockMvc.perform(put("/manage/appointments/456/status")  // Appointment with invalid transition
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(409))
                .andExpect(jsonPath("$.message").value("Cannot transition from CANCELLED to COMPLETED"));
    }
}
