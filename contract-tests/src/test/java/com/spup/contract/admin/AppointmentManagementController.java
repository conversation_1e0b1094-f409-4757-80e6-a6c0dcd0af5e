package com.spup.contract.admin;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test controller for admin appointment management contracts
 */
@RestController
@RequestMapping("/manage/appointments")
public class AppointmentManagementController {

    private static final List<String> VALID_STATUSES = Arrays.asList("CONFIRMED", "CANCELLED", "COMPLETED", "NO_SHOW");

    @GetMapping
    public ResponseEntity<Map<String, Object>> listAppointments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String date) {

        // Simulate unauthorized access
        if (page < 0) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate invalid parameters
        if (size > 100) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Page size must not be greater than 100");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate empty result
        if ("COMPLETED".equals(status)) {
            Map<String, Object> data = new HashMap<>();
            data.put("content", new ArrayList<>());
            data.put("totalElements", 0);
            data.put("totalPages", 0);
            data.put("size", size);
            data.put("number", page);

            Map<String, Object> response = new HashMap<>();
            response.put("status", true);
            response.put("code", 200);
            response.put("message", "Success");
            response.put("data", data);

            return ResponseEntity.ok(response);
        }

        List<Map<String, Object>> content = new ArrayList<>();
        Map<String, Object> appointment = new HashMap<>();
        appointment.put("id", 123);
        appointment.put("orderNo", "ORDER123456789012");
        appointment.put("batchDate", "2023-10-15");
        appointment.put("batchStartTime", "10:00");
        appointment.put("batchEndTime", "11:00");
        appointment.put("status", "CONFIRMED");
        appointment.put("contactName", "John Doe");
        appointment.put("contactPhone", "13800138000");
        content.add(appointment);

        Map<String, Object> data = new HashMap<>();
        data.put("content", content);
        data.put("totalElements", 1);
        data.put("totalPages", 1);
        data.put("size", size);
        data.put("number", page);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}/status")
    public ResponseEntity<Map<String, Object>> updateAppointmentStatus(
            @PathVariable Long id,
            @RequestBody Map<String, String> request) {

        Map<String, Object> response = new HashMap<>();

        // Simulate appointment not found
        if (id == 999) {
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Appointment not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Validate required fields
        if (!request.containsKey("status")) {
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Status is required");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Validate status value
        String status = request.get("status");
        if (!VALID_STATUSES.contains(status)) {
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid status value. Must be one of: " + VALID_STATUSES);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate invalid state transition
        if (id == 456 && "COMPLETED".equals(status)) {
            response.put("status", false);
            response.put("code", 409);
            response.put("message", "Cannot transition from CANCELLED to COMPLETED");
            return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("id", id);
        data.put("status", status);

        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Status updated successfully");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }
}
