package com.spup.contract.admin;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test controller for batch management contracts
 */
@RestController
@RequestMapping("/manage/batches")
public class BatchManagementController {

    @GetMapping
    public ResponseEntity<Map<String, Object>> listBatches(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String date,
            @RequestParam(required = false) String status) {
        
        // Simulate unauthorized access
        if (page < 0) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate invalid parameters
        if (size > 100) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Page size must not be greater than 100");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate empty result for a specific date
        if (date != null && date.equals("2023-12-25")) {
            Map<String, Object> data = new HashMap<>();
            data.put("content", new ArrayList<>());
            data.put("totalElements", 0);
            data.put("totalPages", 0);
            data.put("size", size);
            data.put("number", page);

            Map<String, Object> response = new HashMap<>();
            response.put("status", true);
            response.put("code", 200);
            response.put("message", "Success");
            response.put("data", data);

            return ResponseEntity.ok(response);
        }

        // Normal response with batches
        List<Map<String, Object>> content = new ArrayList<>();
        
        Map<String, Object> batch1 = new HashMap<>();
        batch1.put("id", 101);
        batch1.put("date", "2023-10-15");
        batch1.put("startTime", "09:00");
        batch1.put("endTime", "10:00");
        batch1.put("capacity", 50);
        batch1.put("remaining", 20);
        batch1.put("status", "OPEN");
        batch1.put("createdBy", "admin");
        batch1.put("createdAt", "2023-10-01T10:00:00Z");
        content.add(batch1);
        
        Map<String, Object> batch2 = new HashMap<>();
        batch2.put("id", 102);
        batch2.put("date", "2023-10-15");
        batch2.put("startTime", "10:30");
        batch2.put("endTime", "11:30");
        batch2.put("capacity", 50);
        batch2.put("remaining", 0);
        batch2.put("status", "FULL");
        batch2.put("createdBy", "admin");
        batch2.put("createdAt", "2023-10-01T10:15:00Z");
        content.add(batch2);

        Map<String, Object> data = new HashMap<>();
        data.put("content", content);
        data.put("totalElements", 2);
        data.put("totalPages", 1);
        data.put("size", size);
        data.put("number", page);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getBatchDetails(@PathVariable Long id) {
        // Simulate batch not found
        if (id == 999L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Batch not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Normal response with batch details
        List<Map<String, Object>> appointments = new ArrayList<>();
        
        Map<String, Object> appointment1 = new HashMap<>();
        appointment1.put("id", 201);
        appointment1.put("orderNo", "ORDER123456789012");
        appointment1.put("contactName", "John Doe");
        appointment1.put("contactPhone", "13800138000");
        appointment1.put("status", "CONFIRMED");
        appointment1.put("createdAt", "2023-10-10T09:15:00Z");
        appointments.add(appointment1);
        
        Map<String, Object> appointment2 = new HashMap<>();
        appointment2.put("id", 202);
        appointment2.put("orderNo", "ORDER987654321098");
        appointment2.put("contactName", "Jane Smith");
        appointment2.put("contactPhone", "13900139000");
        appointment2.put("status", "CONFIRMED");
        appointment2.put("createdAt", "2023-10-10T10:30:00Z");
        appointments.add(appointment2);

        Map<String, Object> data = new HashMap<>();
        data.put("id", id);
        data.put("date", "2023-10-15");
        data.put("startTime", "09:00");
        data.put("endTime", "10:00");
        data.put("capacity", 50);
        data.put("remaining", 20);
        data.put("status", "OPEN");
        data.put("description", "Morning batch");
        data.put("notes", "Please arrive 15 minutes early");
        data.put("createdBy", "admin");
        data.put("createdAt", "2023-10-01T10:00:00Z");
        data.put("updatedBy", "admin");
        data.put("updatedAt", "2023-10-05T14:30:00Z");
        data.put("appointments", appointments);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PostMapping
    public ResponseEntity<Map<String, Object>> createBatch(@RequestBody Map<String, Object> request) {
        // Validate required fields
        if (!request.containsKey("date") || !request.containsKey("startTime") || 
            !request.containsKey("endTime") || !request.containsKey("capacity")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Missing required fields");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        String date = (String) request.get("date");
        String startTime = (String) request.get("startTime");
        String endTime = (String) request.get("endTime");

        // Validate date format
        if (!date.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid date format. Use YYYY-MM-DD");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Validate time format
        if (!startTime.matches("^\\d{2}:\\d{2}$") || !endTime.matches("^\\d{2}:\\d{2}$")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid time format. Use HH:MM (24-hour format)");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate batch already exists
        if (date.equals("2023-10-15") && startTime.equals("09:00") && endTime.equals("10:00")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 409);
            response.put("message", "Batch already exists for this date and time");
            return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
        }

        // Successful batch creation
        Map<String, Object> data = new HashMap<>();
        data.put("id", 103);
        data.put("date", date);
        data.put("startTime", startTime);
        data.put("endTime", endTime);
        data.put("capacity", request.get("capacity"));
        data.put("remaining", request.get("capacity"));
        data.put("status", "OPEN");
        data.put("description", request.containsKey("description") ? request.get("description") : null);
        data.put("notes", request.containsKey("notes") ? request.get("notes") : null);
        data.put("createdBy", "admin");
        data.put("createdAt", "2023-10-15T10:00:00Z");

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 201);
        response.put("message", "Batch created successfully");
        response.put("data", data);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateBatch(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request) {
        
        // Simulate batch not found
        if (id == 999L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Batch not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Validate time format if provided
        if (request.containsKey("startTime") && !((String) request.get("startTime")).matches("^\\d{2}:\\d{2}$")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid start time format. Use HH:MM (24-hour format)");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        if (request.containsKey("endTime") && !((String) request.get("endTime")).matches("^\\d{2}:\\d{2}$")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid end time format. Use HH:MM (24-hour format)");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Successful batch update
        Map<String, Object> data = new HashMap<>();
        data.put("id", id);
        data.put("date", "2023-10-15");
        data.put("startTime", request.containsKey("startTime") ? request.get("startTime") : "09:00");
        data.put("endTime", request.containsKey("endTime") ? request.get("endTime") : "10:00");
        data.put("capacity", request.containsKey("capacity") ? request.get("capacity") : 50);
        data.put("remaining", 20);
        data.put("status", request.containsKey("status") ? request.get("status") : "OPEN");
        data.put("description", request.containsKey("description") ? request.get("description") : "Morning batch");
        data.put("notes", request.containsKey("notes") ? request.get("notes") : "Please arrive 15 minutes early");
        data.put("updatedBy", "admin");
        data.put("updatedAt", "2023-10-15T14:30:00Z");

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Batch updated successfully");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}/status")
    public ResponseEntity<Map<String, Object>> updateBatchStatus(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request) {
        
        // Simulate batch not found
        if (id == 999L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Batch not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Validate required fields
        if (!request.containsKey("status")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Status is required");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        String status = (String) request.get("status");

        // Validate status value
        if (!status.equals("OPEN") && !status.equals("FULL") && !status.equals("CLOSED") && !status.equals("CANCELLED")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid status value. Must be one of: [OPEN, FULL, CLOSED, CANCELLED]");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate invalid status transition
        if (id == 102L && status.equals("CANCELLED")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 409);
            response.put("message", "Cannot cancel a batch with confirmed appointments");
            return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
        }

        // Successful status update
        Map<String, Object> data = new HashMap<>();
        data.put("id", id);
        data.put("status", status);
        data.put("updatedBy", "admin");
        data.put("updatedAt", "2023-10-15T14:30:00Z");

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Batch status updated successfully");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteBatch(@PathVariable Long id) {
        // Simulate batch not found
        if (id == 999L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Batch not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Simulate cannot delete batch with appointments
        if (id == 102L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 409);
            response.put("message", "Cannot delete a batch with appointments");
            return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
        }

        // Successful deletion
        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Batch deleted successfully");

        return ResponseEntity.ok(response);
    }
}
