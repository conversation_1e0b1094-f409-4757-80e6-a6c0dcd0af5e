// contract-tests/src/test/resources/contracts/app/appointments/shouldBookAppointment.groovy
package contracts.app.appointments

import org.springframework.cloud.contract.spec.Contract

Contract.make {
    name("book appointment")
    description("should book a new appointment")
    
    request {
        method POST()
        url("/api/appointments/book")
        headers {
            contentType(applicationJson())
            accept(applicationJson())
        }
        body([
            "batchId": 789,
            "contactName": "John Doe",
            "contactPhone": "13800138000",
            "idCardType": 1,
            "idCardNo": "123456789012345678"
        ])
    }
    
    response {
        status 201
        headers {
            contentType(applicationJson())
        }
        body([
            "status": true,
            "code": 200,
            "message": "Appointment booked successfully",
            "data": [
                "orderNo": $(producer(regex('[A-Z0-9]{16}')), consumer("ORDER123456789012")),
                "batchDate": $(producer(regex('\\d{4}-\\d{2}-\\d{2}')), consumer("2023-10-20")),
                "batchStartTime": $(producer(regex('\\d{2}:\\d{2}')), consumer("14:00")),
                "batchEndTime": $(producer(regex('\\d{2}:\\d{2}')), consumer("15:00")),
                "status": "CONFIRMED"
            ]
        ])
    }
}