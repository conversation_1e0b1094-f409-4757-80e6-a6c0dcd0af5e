// contract-tests/src/test/resources/contracts/admin/appointments/shouldUpdateAppointmentStatus.groovy
package contracts.admin.appointments

import org.springframework.cloud.contract.spec.Contract

Contract.make {
    name("update appointment status")
    description("should update the status of an appointment")
    
    request {
        method PUT()
        url("/manage/appointments/123/status")
        headers {
            contentType(applicationJson())
            accept(applicationJson())
        }
        body([
            "status": "CANCELLED",
            "reason": "Customer request"
        ])
    }
    
    response {
        status 200
        headers {
            contentType(applicationJson())
        }
        body([
            "status": true,
            "code": 200,
            "message": "Status updated successfully",
            "data": [
                "id": 123,
                "status": "CANCELLED"
            ]
        ])
    }
}