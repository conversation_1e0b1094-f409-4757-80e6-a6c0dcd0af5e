package com.spup.contract.app;

import io.restassured.module.mockmvc.RestAssuredMockMvc;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * Contract tests for the Activity API
 */
public class ActivityContractTest {

    private MockMvc mockMvc;
    private ActivityController activityController;

    @BeforeEach
    public void setup() {
        activityController = new ActivityController();
        mockMvc = MockMvcBuilders.standaloneSetup(activityController).build();
        RestAssuredMockMvc.mockMvc(mockMvc);
    }

    // Success scenarios

    @Test
    public void shouldListActivities() throws Exception {
        mockMvc.perform(get("/api/activities")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].id").value(101))
                .andExpect(jsonPath("$.data.content[0].title").value("Summer Art Workshop"))
                .andExpect(jsonPath("$.data.content[0].startDate").value("2023-07-15"))
                .andExpect(jsonPath("$.data.content[0].endDate").value("2023-07-20"))
                .andExpect(jsonPath("$.data.content[0].type").value("WORKSHOP"))
                .andExpect(jsonPath("$.data.content[0].status").value("ACTIVE"))
                .andExpect(jsonPath("$.data.content[0].capacity").value(30))
                .andExpect(jsonPath("$.data.content[0].remaining").value(15))
                .andExpect(jsonPath("$.data.content[1].id").value(102))
                .andExpect(jsonPath("$.data.content[1].title").value("Artist Talk: Modern Expressions"))
                .andExpect(jsonPath("$.data.content[1].type").value("TALK"))
                .andExpect(jsonPath("$.data.totalElements").value(2))
                .andExpect(jsonPath("$.data.totalPages").value(1))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));
    }

    @Test
    public void shouldListActivitiesByType() throws Exception {
        mockMvc.perform(get("/api/activities")
                .param("page", "0")
                .param("size", "10")
                .param("type", "WORKSHOP")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].type").value("WORKSHOP"));
    }

    @Test
    public void shouldReturnEmptyListWhenNoActivitiesForType() throws Exception {
        mockMvc.perform(get("/api/activities")
                .param("page", "0")
                .param("size", "10")
                .param("type", "nonexistent")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content").isEmpty())
                .andExpect(jsonPath("$.data.totalElements").value(0))
                .andExpect(jsonPath("$.data.totalPages").value(0));
    }

    @Test
    public void shouldGetActivityDetails() throws Exception {
        mockMvc.perform(get("/api/activities/101")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.id").value(101))
                .andExpect(jsonPath("$.data.title").value("Summer Art Workshop"))
                .andExpect(jsonPath("$.data.startDate").value("2023-07-15"))
                .andExpect(jsonPath("$.data.endDate").value("2023-07-20"))
                .andExpect(jsonPath("$.data.startTime").value("09:00"))
                .andExpect(jsonPath("$.data.endTime").value("12:00"))
                .andExpect(jsonPath("$.data.location").value("Main Gallery"))
                .andExpect(jsonPath("$.data.type").value("WORKSHOP"))
                .andExpect(jsonPath("$.data.status").value("ACTIVE"))
                .andExpect(jsonPath("$.data.capacity").value(30))
                .andExpect(jsonPath("$.data.remaining").value(15))
                .andExpect(jsonPath("$.data.price").value(50.00))
                .andExpect(jsonPath("$.data.schedule").isArray())
                .andExpect(jsonPath("$.data.schedule.length()").value(2))
                .andExpect(jsonPath("$.data.schedule[0].date").value("2023-07-15"))
                .andExpect(jsonPath("$.data.schedule[0].title").value("Introduction to Drawing"));
    }

    @Test
    public void shouldRegisterForActivity() throws Exception {
        String requestBody = "{\n" +
                "  \"participants\": [\n" +
                "    {\n" +
                "      \"name\": \"John Doe\",\n" +
                "      \"age\": 30,\n" +
                "      \"email\": \"<EMAIL>\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        mockMvc.perform(post("/api/activities/register/101")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(201))
                .andExpect(jsonPath("$.message").value("Registration successful"))
                .andExpect(jsonPath("$.data.registrationId").value(5001))
                .andExpect(jsonPath("$.data.activityId").value(101))
                .andExpect(jsonPath("$.data.activityTitle").value("Summer Art Workshop"))
                .andExpect(jsonPath("$.data.userId").value(1001))
                .andExpect(jsonPath("$.data.status").value("CONFIRMED"))
                .andExpect(jsonPath("$.data.registrationDate").isString())
                .andExpect(jsonPath("$.data.participants").isArray());
    }

    @Test
    public void shouldGetActivityTypes() throws Exception {
        mockMvc.perform(get("/api/activities/types")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.types[0].code").value("WORKSHOP"))
                .andExpect(jsonPath("$.data.types[0].name").value("Workshop"))
                .andExpect(jsonPath("$.data.types[1].code").value("TALK"))
                .andExpect(jsonPath("$.data.types[1].name").value("Artist Talk"))
                .andExpect(jsonPath("$.data.types[2].code").value("EXHIBITION"))
                .andExpect(jsonPath("$.data.types[2].name").value("Exhibition"))
                .andExpect(jsonPath("$.data.types[3].code").value("TOUR"))
                .andExpect(jsonPath("$.data.types[3].name").value("Guided Tour"));
    }

    @Test
    public void shouldGetUserRegistrations() throws Exception {
        mockMvc.perform(get("/api/activities/user/registrations")
                .header("Authorization", "Bearer valid-token")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].registrationId").value(5001))
                .andExpect(jsonPath("$.data.content[0].activityId").value(101))
                .andExpect(jsonPath("$.data.content[0].activityTitle").value("Summer Art Workshop"))
                .andExpect(jsonPath("$.data.content[0].startDate").value("2023-07-15"))
                .andExpect(jsonPath("$.data.content[0].endDate").value("2023-07-20"))
                .andExpect(jsonPath("$.data.content[0].status").value("CONFIRMED"))
                .andExpect(jsonPath("$.data.content[1].registrationId").value(5002))
                .andExpect(jsonPath("$.data.content[1].activityId").value(102))
                .andExpect(jsonPath("$.data.totalElements").value(2))
                .andExpect(jsonPath("$.data.totalPages").value(1))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));
    }

    @Test
    public void shouldReturnEmptyRegistrationsWhenNoRegistrationsExist() throws Exception {
        mockMvc.perform(get("/api/activities/user/registrations")
                .header("Authorization", "Bearer valid-token")
                .param("page", "11")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content").isEmpty())
                .andExpect(jsonPath("$.data.totalElements").value(0))
                .andExpect(jsonPath("$.data.totalPages").value(0));
    }

    @Test
    public void shouldCancelRegistration() throws Exception {
        mockMvc.perform(delete("/api/activities/user/registrations/5001")
                .header("Authorization", "Bearer valid-token")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Registration cancelled successfully"));
    }

    // Error scenarios

    @Test
    public void shouldReturnNotFoundWhenActivityDoesNotExist() throws Exception {
        mockMvc.perform(get("/api/activities/999")
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Activity not found"));
    }

    @Test
    public void shouldReturnBadRequestWhenPageSizeExceedsLimit() throws Exception {
        mockMvc.perform(get("/api/activities")
                .param("page", "0")
                .param("size", "101")
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Page size must not be greater than 100"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenRegisterWithoutToken() throws Exception {
        String requestBody = "{\n" +
                "  \"participants\": [\n" +
                "    {\n" +
                "      \"name\": \"John Doe\",\n" +
                "      \"age\": 30,\n" +
                "      \"email\": \"<EMAIL>\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        mockMvc.perform(post("/api/activities/register/101")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnNotFoundWhenRegisterForNonExistentActivity() throws Exception {
        String requestBody = "{\n" +
                "  \"participants\": [\n" +
                "    {\n" +
                "      \"name\": \"John Doe\",\n" +
                "      \"age\": 30,\n" +
                "      \"email\": \"<EMAIL>\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        mockMvc.perform(post("/api/activities/register/999")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Activity not found"));
    }

    @Test
    public void shouldReturnConflictWhenRegisterForFullActivity() throws Exception {
        String requestBody = "{\n" +
                "  \"participants\": [\n" +
                "    {\n" +
                "      \"name\": \"John Doe\",\n" +
                "      \"age\": 30,\n" +
                "      \"email\": \"<EMAIL>\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        mockMvc.perform(post("/api/activities/register/103")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(409))
                .andExpect(jsonPath("$.message").value("Activity is full"));
    }

    @Test
    public void shouldReturnBadRequestWhenRegisterForInactiveActivity() throws Exception {
        String requestBody = "{\n" +
                "  \"participants\": [\n" +
                "    {\n" +
                "      \"name\": \"John Doe\",\n" +
                "      \"age\": 30,\n" +
                "      \"email\": \"<EMAIL>\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        mockMvc.perform(post("/api/activities/register/104")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Activity is not active"));
    }

    @Test
    public void shouldReturnBadRequestWhenRegisterWithoutParticipants() throws Exception {
        String requestBody = "{}";

        mockMvc.perform(post("/api/activities/register/101")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Participants information is required"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenGetUserRegistrationsWithoutToken() throws Exception {
        mockMvc.perform(get("/api/activities/user/registrations")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnBadRequestWhenGetUserRegistrationsWithInvalidPageSize() throws Exception {
        mockMvc.perform(get("/api/activities/user/registrations")
                .header("Authorization", "Bearer valid-token")
                .param("page", "0")
                .param("size", "101")
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Page size must not be greater than 100"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenCancelRegistrationWithoutToken() throws Exception {
        mockMvc.perform(delete("/api/activities/user/registrations/5001")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnNotFoundWhenCancelNonExistentRegistration() throws Exception {
        mockMvc.perform(delete("/api/activities/user/registrations/9999")
                .header("Authorization", "Bearer valid-token")
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Registration not found"));
    }

    @Test
    public void shouldReturnForbiddenWhenCancelRegistrationNotOwnedByUser() throws Exception {
        mockMvc.perform(delete("/api/activities/user/registrations/5003")
                .header("Authorization", "Bearer valid-token")
                .accept(APPLICATION_JSON))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(403))
                .andExpect(jsonPath("$.message").value("You can only cancel your own registrations"));
    }

    @Test
    public void shouldReturnBadRequestWhenCancelRegistrationTooCloseToStartDate() throws Exception {
        mockMvc.perform(delete("/api/activities/user/registrations/5004")
                .header("Authorization", "Bearer valid-token")
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Registration cannot be cancelled within 24 hours of the activity start time"));
    }
}
