package com.spup.contract.app;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test controller for activity contracts
 */
@RestController
@RequestMapping("/api/activities")
public class ActivityController {

    @GetMapping
    public ResponseEntity<Map<String, Object>> listActivities(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status) {
        
        // Simulate invalid parameters
        if (size > 100) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Page size must not be greater than 100");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate empty result for a specific type
        if (type != null && type.equals("nonexistent")) {
            Map<String, Object> data = new HashMap<>();
            data.put("content", new ArrayList<>());
            data.put("totalElements", 0);
            data.put("totalPages", 0);
            data.put("size", size);
            data.put("number", page);

            Map<String, Object> response = new HashMap<>();
            response.put("status", true);
            response.put("code", 200);
            response.put("message", "Success");
            response.put("data", data);

            return ResponseEntity.ok(response);
        }

        // Normal response with activities
        List<Map<String, Object>> content = new ArrayList<>();
        
        Map<String, Object> activity1 = new HashMap<>();
        activity1.put("id", 101);
        activity1.put("title", "Summer Art Workshop");
        activity1.put("description", "Join us for a fun-filled summer art workshop for all ages.");
        activity1.put("startDate", "2023-07-15");
        activity1.put("endDate", "2023-07-20");
        activity1.put("startTime", "09:00");
        activity1.put("endTime", "12:00");
        activity1.put("location", "Main Gallery");
        activity1.put("type", "WORKSHOP");
        activity1.put("status", "ACTIVE");
        activity1.put("capacity", 30);
        activity1.put("remaining", 15);
        activity1.put("imageUrl", "https://example.com/images/summer-workshop.jpg");
        activity1.put("createdAt", "2023-06-01T10:00:00Z");
        content.add(activity1);
        
        Map<String, Object> activity2 = new HashMap<>();
        activity2.put("id", 102);
        activity2.put("title", "Artist Talk: Modern Expressions");
        activity2.put("description", "A talk by renowned artist Jane Smith about modern art expressions.");
        activity2.put("startDate", "2023-08-05");
        activity2.put("endDate", "2023-08-05");
        activity2.put("startTime", "14:00");
        activity2.put("endTime", "16:00");
        activity2.put("location", "Lecture Hall");
        activity2.put("type", "TALK");
        activity2.put("status", "ACTIVE");
        activity2.put("capacity", 100);
        activity2.put("remaining", 45);
        activity2.put("imageUrl", "https://example.com/images/artist-talk.jpg");
        activity2.put("createdAt", "2023-06-15T11:30:00Z");
        content.add(activity2);

        Map<String, Object> data = new HashMap<>();
        data.put("content", content);
        data.put("totalElements", 2);
        data.put("totalPages", 1);
        data.put("size", size);
        data.put("number", page);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getActivityDetails(@PathVariable Long id) {
        // Simulate activity not found
        if (id == 999L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Activity not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Normal response with activity details
        Map<String, Object> data = new HashMap<>();
        data.put("id", id);
        data.put("title", "Summer Art Workshop");
        data.put("description", "Join us for a fun-filled summer art workshop for all ages. Learn various art techniques from professional artists.");
        data.put("startDate", "2023-07-15");
        data.put("endDate", "2023-07-20");
        data.put("startTime", "09:00");
        data.put("endTime", "12:00");
        data.put("location", "Main Gallery");
        data.put("type", "WORKSHOP");
        data.put("status", "ACTIVE");
        data.put("capacity", 30);
        data.put("remaining", 15);
        data.put("price", 50.00);
        data.put("imageUrl", "https://example.com/images/summer-workshop.jpg");
        data.put("organizer", "Art Museum Education Department");
        data.put("contactEmail", "<EMAIL>");
        data.put("contactPhone", "************");
        data.put("createdAt", "2023-06-01T10:00:00Z");
        data.put("updatedAt", "2023-06-10T14:30:00Z");
        
        // Add schedule details
        List<Map<String, Object>> schedule = new ArrayList<>();
        Map<String, Object> day1 = new HashMap<>();
        day1.put("date", "2023-07-15");
        day1.put("title", "Introduction to Drawing");
        day1.put("description", "Basic drawing techniques and materials");
        schedule.add(day1);
        
        Map<String, Object> day2 = new HashMap<>();
        day2.put("date", "2023-07-16");
        day2.put("title", "Color Theory");
        day2.put("description", "Understanding color relationships and mixing");
        schedule.add(day2);
        
        data.put("schedule", schedule);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PostMapping("/register/{id}")
    public ResponseEntity<Map<String, Object>> registerForActivity(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @PathVariable Long id,
            @RequestBody Map<String, Object> request) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate activity not found
        if (id == 999L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Activity not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Simulate activity is full
        if (id == 103L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 409);
            response.put("message", "Activity is full");
            return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
        }

        // Simulate activity is not active
        if (id == 104L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Activity is not active");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Validate required fields
        if (!request.containsKey("participants")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Participants information is required");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Successful registration
        Map<String, Object> data = new HashMap<>();
        data.put("registrationId", 5001);
        data.put("activityId", id);
        data.put("activityTitle", "Summer Art Workshop");
        data.put("userId", 1001);
        data.put("status", "CONFIRMED");
        data.put("registrationDate", "2023-06-20T09:45:00Z");
        data.put("participants", request.get("participants"));

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 201);
        response.put("message", "Registration successful");
        response.put("data", data);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping("/types")
    public ResponseEntity<Map<String, Object>> getActivityTypes() {
        List<Map<String, Object>> types = new ArrayList<>();
        
        Map<String, Object> type1 = new HashMap<>();
        type1.put("code", "WORKSHOP");
        type1.put("name", "Workshop");
        types.add(type1);
        
        Map<String, Object> type2 = new HashMap<>();
        type2.put("code", "TALK");
        type2.put("name", "Artist Talk");
        types.add(type2);
        
        Map<String, Object> type3 = new HashMap<>();
        type3.put("code", "EXHIBITION");
        type3.put("name", "Exhibition");
        types.add(type3);
        
        Map<String, Object> type4 = new HashMap<>();
        type4.put("code", "TOUR");
        type4.put("name", "Guided Tour");
        types.add(type4);

        Map<String, Object> data = new HashMap<>();
        data.put("types", types);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/user/registrations")
    public ResponseEntity<Map<String, Object>> getUserRegistrations(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate invalid parameters
        if (size > 100) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Page size must not be greater than 100");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate empty result for a specific page
        if (page > 10) {
            Map<String, Object> data = new HashMap<>();
            data.put("content", new ArrayList<>());
            data.put("totalElements", 0);
            data.put("totalPages", 0);
            data.put("size", size);
            data.put("number", page);

            Map<String, Object> response = new HashMap<>();
            response.put("status", true);
            response.put("code", 200);
            response.put("message", "Success");
            response.put("data", data);

            return ResponseEntity.ok(response);
        }

        // Normal response with user registrations
        List<Map<String, Object>> content = new ArrayList<>();
        
        Map<String, Object> registration1 = new HashMap<>();
        registration1.put("registrationId", 5001);
        registration1.put("activityId", 101);
        registration1.put("activityTitle", "Summer Art Workshop");
        registration1.put("startDate", "2023-07-15");
        registration1.put("endDate", "2023-07-20");
        registration1.put("status", "CONFIRMED");
        registration1.put("registrationDate", "2023-06-20T09:45:00Z");
        content.add(registration1);
        
        Map<String, Object> registration2 = new HashMap<>();
        registration2.put("registrationId", 5002);
        registration2.put("activityId", 102);
        registration2.put("activityTitle", "Artist Talk: Modern Expressions");
        registration2.put("startDate", "2023-08-05");
        registration2.put("endDate", "2023-08-05");
        registration2.put("status", "CONFIRMED");
        registration2.put("registrationDate", "2023-06-25T14:30:00Z");
        content.add(registration2);

        Map<String, Object> data = new HashMap<>();
        data.put("content", content);
        data.put("totalElements", 2);
        data.put("totalPages", 1);
        data.put("size", size);
        data.put("number", page);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/user/registrations/{registrationId}")
    public ResponseEntity<Map<String, Object>> cancelRegistration(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @PathVariable Long registrationId) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate registration not found
        if (registrationId == 9999L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Registration not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Simulate registration not owned by user
        if (registrationId == 5003L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 403);
            response.put("message", "You can only cancel your own registrations");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
        }

        // Simulate registration cannot be cancelled (e.g., too close to start date)
        if (registrationId == 5004L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Registration cannot be cancelled within 24 hours of the activity start time");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Successful cancellation
        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Registration cancelled successfully");

        return ResponseEntity.ok(response);
    }
}
