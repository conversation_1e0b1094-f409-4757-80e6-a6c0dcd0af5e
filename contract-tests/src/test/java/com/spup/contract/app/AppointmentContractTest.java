package com.spup.contract.app;

import io.restassured.module.mockmvc.RestAssuredMockMvc;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * Contract tests for the appointment API
 */
public class AppointmentContractTest {

    private MockMvc mockMvc;
    private AppointmentController appointmentController;

    @BeforeEach
    public void setup() {
        appointmentController = new AppointmentController();
        mockMvc = MockMvcBuilders.standaloneSetup(appointmentController).build();
        RestAssuredMockMvc.mockMvc(mockMvc);
    }

    // Success scenarios

    @Test
    public void shouldGetAppointmentDetails() throws Exception {
        mockMvc.perform(get("/api/appointments/123")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.id").value(123))
                .andExpect(jsonPath("$.data.orderNo").value("ORDER123456789012"))
                .andExpect(jsonPath("$.data.batchDate").value("2023-10-15"))
                .andExpect(jsonPath("$.data.batchStartTime").value("10:00"))
                .andExpect(jsonPath("$.data.batchEndTime").value("11:00"))
                .andExpect(jsonPath("$.data.status").value("CONFIRMED"));
    }

    @Test
    public void shouldBookAppointment() throws Exception {
        String requestBody = "{\n" +
                "  \"batchId\": 789,\n" +
                "  \"contactName\": \"John Doe\",\n" +
                "  \"contactPhone\": \"13800138000\",\n" +
                "  \"idCardType\": 1,\n" +
                "  \"idCardNo\": \"110101199001011234\"\n" +
                "}";

        mockMvc.perform(post("/api/appointments/book")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Appointment booked successfully"))
                .andExpect(jsonPath("$.data.orderNo").isString())
                .andExpect(jsonPath("$.data.batchDate").isString())
                .andExpect(jsonPath("$.data.batchStartTime").isString())
                .andExpect(jsonPath("$.data.batchEndTime").isString())
                .andExpect(jsonPath("$.data.status").value("CONFIRMED"));
    }

    // Error scenarios

    @Test
    public void shouldReturnNotFoundWhenAppointmentDoesNotExist() throws Exception {
        mockMvc.perform(get("/api/appointments/999")
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Appointment not found"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenAccessingForbiddenAppointment() throws Exception {
        mockMvc.perform(get("/api/appointments/888")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnBadRequestWhenMissingRequiredFields() throws Exception {
        String requestBody = "{\n" +
                "  \"batchId\": 789,\n" +
                "  \"contactName\": \"John Doe\"\n" +
                "}";

        mockMvc.perform(post("/api/appointments/book")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Missing required fields"));
    }

    @Test
    public void shouldReturnBadRequestWhenPhoneNumberFormatIsInvalid() throws Exception {
        String requestBody = "{\n" +
                "  \"batchId\": 789,\n" +
                "  \"contactName\": \"John Doe\",\n" +
                "  \"contactPhone\": \"12345\",\n" +
                "  \"idCardType\": 1,\n" +
                "  \"idCardNo\": \"110101199001011234\"\n" +
                "}";

        mockMvc.perform(post("/api/appointments/book")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid phone number format"));
    }

    @Test
    public void shouldReturnBadRequestWhenIdCardFormatIsInvalid() throws Exception {
        String requestBody = "{\n" +
                "  \"batchId\": 789,\n" +
                "  \"contactName\": \"John Doe\",\n" +
                "  \"contactPhone\": \"13800138000\",\n" +
                "  \"idCardType\": 1,\n" +
                "  \"idCardNo\": \"12345\"\n" +
                "}";

        mockMvc.perform(post("/api/appointments/book")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid ID card format"));
    }

    @Test
    public void shouldReturnConflictWhenBatchIsNotAvailable() throws Exception {
        String requestBody = "{\n" +
                "  \"batchId\": 999,\n" +
                "  \"contactName\": \"John Doe\",\n" +
                "  \"contactPhone\": \"13800138000\",\n" +
                "  \"idCardType\": 1,\n" +
                "  \"idCardNo\": \"110101199001011234\"\n" +
                "}";

        mockMvc.perform(post("/api/appointments/book")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(409))
                .andExpect(jsonPath("$.message").value("Batch is not available"));
    }
}
