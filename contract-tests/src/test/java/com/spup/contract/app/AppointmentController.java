package com.spup.contract.app;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Test controller for appointment contracts
 */
@RestController
@RequestMapping("/api/appointments")
public class AppointmentController {

    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$");

    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getAppointmentDetails(@PathVariable Long id) {
        // Simulate not found scenario
        if (id == 999) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Appointment not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Simulate unauthorized access
        if (id == 888) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        Map<String, Object> response = new HashMap<>();

        Map<String, Object> data = new HashMap<>();
        data.put("id", id);
        data.put("orderNo", "ORDER123456789012");
        data.put("batchDate", "2023-10-15");
        data.put("batchStartTime", "10:00");
        data.put("batchEndTime", "11:00");
        data.put("status", "CONFIRMED");

        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PostMapping("/book")
    public ResponseEntity<Map<String, Object>> bookAppointment(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();

        // Validate required fields
        if (!request.containsKey("batchId") || !request.containsKey("contactName") ||
            !request.containsKey("contactPhone") || !request.containsKey("idCardType") ||
            !request.containsKey("idCardNo")) {

            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Missing required fields");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Validate phone number format
        String contactPhone = (String) request.get("contactPhone");
        if (!PHONE_PATTERN.matcher(contactPhone).matches()) {
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid phone number format");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Validate ID card format
        String idCardNo = (String) request.get("idCardNo");
        if (!ID_CARD_PATTERN.matcher(idCardNo).matches()) {
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid ID card format");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate batch not available
        Integer batchId = (Integer) request.get("batchId");
        if (batchId == 999) {
            response.put("status", false);
            response.put("code", 409);
            response.put("message", "Batch is not available");
            return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("orderNo", "ORDER123456789012");
        data.put("batchDate", "2023-10-20");
        data.put("batchStartTime", "14:00");
        data.put("batchEndTime", "15:00");
        data.put("status", "CONFIRMED");

        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Appointment booked successfully");
        response.put("data", data);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
}
