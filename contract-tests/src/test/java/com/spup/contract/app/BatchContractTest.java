package com.spup.contract.app;

import io.restassured.module.mockmvc.RestAssuredMockMvc;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * Contract tests for the batch API
 */
public class BatchContractTest {

    private MockMvc mockMvc;
    private BatchController batchController;

    @BeforeEach
    public void setup() {
        batchController = new BatchController();
        mockMvc = MockMvcBuilders.standaloneSetup(batchController).build();
        RestAssuredMockMvc.mockMvc(mockMvc);
    }

    // Success scenarios

    @Test
    public void shouldListBatches() throws Exception {
        mockMvc.perform(get("/api/batches")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].id").value(101))
                .andExpect(jsonPath("$.data.content[0].date").value("2023-10-15"))
                .andExpect(jsonPath("$.data.content[0].startTime").value("09:00"))
                .andExpect(jsonPath("$.data.content[0].endTime").value("10:00"))
                .andExpect(jsonPath("$.data.content[0].capacity").value(50))
                .andExpect(jsonPath("$.data.content[0].remaining").value(20))
                .andExpect(jsonPath("$.data.content[0].status").value("OPEN"))
                .andExpect(jsonPath("$.data.content[1].id").value(102))
                .andExpect(jsonPath("$.data.content[1].status").value("FULL"))
                .andExpect(jsonPath("$.data.totalElements").value(2))
                .andExpect(jsonPath("$.data.totalPages").value(1))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));
    }

    @Test
    public void shouldListBatchesForSpecificDate() throws Exception {
        mockMvc.perform(get("/api/batches")
                .param("date", "2023-11-01")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].date").value("2023-11-01"))
                .andExpect(jsonPath("$.data.totalElements").value(2));
    }

    @Test
    public void shouldGetBatchDetails() throws Exception {
        mockMvc.perform(get("/api/batches/101")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.id").value(101))
                .andExpect(jsonPath("$.data.date").value("2023-10-15"))
                .andExpect(jsonPath("$.data.startTime").value("09:00"))
                .andExpect(jsonPath("$.data.endTime").value("10:00"))
                .andExpect(jsonPath("$.data.capacity").value(50))
                .andExpect(jsonPath("$.data.remaining").value(20))
                .andExpect(jsonPath("$.data.status").value("OPEN"))
                .andExpect(jsonPath("$.data.description").value("Morning batch"))
                .andExpect(jsonPath("$.data.notes").value("Please arrive 15 minutes early"));
    }

    @Test
    public void shouldGetAvailableDates() throws Exception {
        mockMvc.perform(get("/api/batches/available-dates")
                .param("month", "2023-10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.month").value("2023-10"))
                .andExpect(jsonPath("$.data.dates").isArray())
                .andExpect(jsonPath("$.data.dates.length()").value(6));
    }

    // Edge cases

    @Test
    public void shouldReturnEmptyListWhenNoAvailableBatchesForDate() throws Exception {
        mockMvc.perform(get("/api/batches")
                .param("date", "2023-12-25")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content").isEmpty())
                .andExpect(jsonPath("$.data.totalElements").value(0))
                .andExpect(jsonPath("$.data.totalPages").value(0));
    }

    // Error scenarios

    @Test
    public void shouldReturnNotFoundWhenBatchDoesNotExist() throws Exception {
        mockMvc.perform(get("/api/batches/999")
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Batch not found"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenAccessingWithInvalidCredentials() throws Exception {
        mockMvc.perform(get("/api/batches")
                .param("page", "-1")  // Trigger unauthorized scenario
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnBadRequestWhenPageSizeExceedsLimit() throws Exception {
        mockMvc.perform(get("/api/batches")
                .param("page", "0")
                .param("size", "101")  // Exceeds limit of 100
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Page size must not be greater than 100"));
    }
}
