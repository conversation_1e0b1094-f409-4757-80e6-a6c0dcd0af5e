package com.spup.contract.app;

import io.restassured.module.mockmvc.RestAssuredMockMvc;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * Contract tests for the Comments API
 */
public class CommentsContractTest {

    private MockMvc mockMvc;
    private CommentsController commentsController;

    @BeforeEach
    public void setup() {
        commentsController = new CommentsController();
        mockMvc = MockMvcBuilders.standaloneSetup(commentsController).build();
        RestAssuredMockMvc.mockMvc(mockMvc);
    }

    // Success scenarios

    @Test
    public void shouldListComments() throws Exception {
        mockMvc.perform(get("/api/comments")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].id").value(101))
                .andExpect(jsonPath("$.data.content[0].content").value("Great experience! Will definitely visit again."))
                .andExpect(jsonPath("$.data.content[0].rating").value(5))
                .andExpect(jsonPath("$.data.content[0].type").value("VISIT"))
                .andExpect(jsonPath("$.data.content[0].userId").value(1001))
                .andExpect(jsonPath("$.data.content[0].username").value("johndoe"))
                .andExpect(jsonPath("$.data.content[1].id").value(102))
                .andExpect(jsonPath("$.data.content[1].rating").value(4))
                .andExpect(jsonPath("$.data.content[1].type").value("SERVICE"))
                .andExpect(jsonPath("$.data.totalElements").value(2))
                .andExpect(jsonPath("$.data.totalPages").value(1))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));
    }

    @Test
    public void shouldListCommentsByType() throws Exception {
        mockMvc.perform(get("/api/comments")
                .param("page", "0")
                .param("size", "10")
                .param("type", "VISIT")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].type").value("VISIT"));
    }

    @Test
    public void shouldReturnEmptyListWhenNoCommentsForType() throws Exception {
        mockMvc.perform(get("/api/comments")
                .param("page", "0")
                .param("size", "10")
                .param("type", "nonexistent")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content").isEmpty())
                .andExpect(jsonPath("$.data.totalElements").value(0))
                .andExpect(jsonPath("$.data.totalPages").value(0));
    }

    @Test
    public void shouldGetCommentDetails() throws Exception {
        mockMvc.perform(get("/api/comments/101")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.id").value(101))
                .andExpect(jsonPath("$.data.content").value("Great experience! Will definitely visit again."))
                .andExpect(jsonPath("$.data.rating").value(5))
                .andExpect(jsonPath("$.data.type").value("VISIT"))
                .andExpect(jsonPath("$.data.userId").value(1001))
                .andExpect(jsonPath("$.data.username").value("johndoe"))
                .andExpect(jsonPath("$.data.status").value("APPROVED"));
    }

    @Test
    public void shouldCreateComment() throws Exception {
        String requestBody = "{\n" +
                "  \"content\": \"The exhibition was amazing!\",\n" +
                "  \"rating\": 5,\n" +
                "  \"type\": \"EXHIBITION\"\n" +
                "}";

        mockMvc.perform(post("/api/comments")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(201))
                .andExpect(jsonPath("$.message").value("Comment submitted successfully"))
                .andExpect(jsonPath("$.data.id").value(103))
                .andExpect(jsonPath("$.data.content").value("The exhibition was amazing!"))
                .andExpect(jsonPath("$.data.rating").value(5))
                .andExpect(jsonPath("$.data.type").value("EXHIBITION"))
                .andExpect(jsonPath("$.data.userId").value(1001))
                .andExpect(jsonPath("$.data.username").value("johndoe"))
                .andExpect(jsonPath("$.data.status").value("PENDING"));
    }

    @Test
    public void shouldUpdateComment() throws Exception {
        String requestBody = "{\n" +
                "  \"content\": \"Updated: Great experience! Will definitely visit again.\",\n" +
                "  \"rating\": 4\n" +
                "}";

        mockMvc.perform(put("/api/comments/101")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Comment updated successfully"))
                .andExpect(jsonPath("$.data.id").value(101))
                .andExpect(jsonPath("$.data.content").value("Updated: Great experience! Will definitely visit again."))
                .andExpect(jsonPath("$.data.rating").value(4))
                .andExpect(jsonPath("$.data.updatedAt").isString());
    }

    @Test
    public void shouldDeleteComment() throws Exception {
        mockMvc.perform(delete("/api/comments/101")
                .header("Authorization", "Bearer valid-token")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Comment deleted successfully"));
    }

    @Test
    public void shouldGetCommentTypes() throws Exception {
        mockMvc.perform(get("/api/comments/types")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.types[0].code").value("VISIT"))
                .andExpect(jsonPath("$.data.types[0].name").value("Visit Experience"))
                .andExpect(jsonPath("$.data.types[1].code").value("SERVICE"))
                .andExpect(jsonPath("$.data.types[1].name").value("Service Quality"))
                .andExpect(jsonPath("$.data.types[2].code").value("EXHIBITION"))
                .andExpect(jsonPath("$.data.types[2].name").value("Exhibition Feedback"));
    }

    // Error scenarios

    @Test
    public void shouldReturnNotFoundWhenCommentDoesNotExist() throws Exception {
        mockMvc.perform(get("/api/comments/999")
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Comment not found"));
    }

    @Test
    public void shouldReturnBadRequestWhenPageSizeExceedsLimit() throws Exception {
        mockMvc.perform(get("/api/comments")
                .param("page", "0")
                .param("size", "101")
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Page size must not be greater than 100"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenCreateCommentWithoutToken() throws Exception {
        String requestBody = "{\n" +
                "  \"content\": \"The exhibition was amazing!\",\n" +
                "  \"rating\": 5,\n" +
                "  \"type\": \"EXHIBITION\"\n" +
                "}";

        mockMvc.perform(post("/api/comments")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnBadRequestWhenCreateCommentMissingRequiredFields() throws Exception {
        String requestBody = "{\n" +
                "  \"content\": \"The exhibition was amazing!\",\n" +
                "  \"rating\": 5\n" +
                "}";

        mockMvc.perform(post("/api/comments")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Missing required fields"));
    }

    @Test
    public void shouldReturnBadRequestWhenCreateCommentInvalidRating() throws Exception {
        String requestBody = "{\n" +
                "  \"content\": \"The exhibition was amazing!\",\n" +
                "  \"rating\": 6,\n" +
                "  \"type\": \"EXHIBITION\"\n" +
                "}";

        mockMvc.perform(post("/api/comments")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Rating must be between 1 and 5"));
    }

    @Test
    public void shouldReturnBadRequestWhenCreateCommentInvalidType() throws Exception {
        String requestBody = "{\n" +
                "  \"content\": \"The exhibition was amazing!\",\n" +
                "  \"rating\": 5,\n" +
                "  \"type\": \"INVALID_TYPE\"\n" +
                "}";

        mockMvc.perform(post("/api/comments")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid comment type. Must be one of: [VISIT, SERVICE, EXHIBITION]"));
    }

    @Test
    public void shouldReturnForbiddenWhenUpdateCommentNotOwnedByUser() throws Exception {
        String requestBody = "{\n" +
                "  \"content\": \"Updated comment\",\n" +
                "  \"rating\": 4\n" +
                "}";

        mockMvc.perform(put("/api/comments/102")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(403))
                .andExpect(jsonPath("$.message").value("You can only update your own comments"));
    }

    @Test
    public void shouldReturnForbiddenWhenDeleteCommentNotOwnedByUser() throws Exception {
        mockMvc.perform(delete("/api/comments/102")
                .header("Authorization", "Bearer valid-token")
                .accept(APPLICATION_JSON))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(403))
                .andExpect(jsonPath("$.message").value("You can only delete your own comments"));
    }
}
