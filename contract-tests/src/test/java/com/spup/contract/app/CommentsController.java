package com.spup.contract.app;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test controller for comments contracts
 */
@RestController
@RequestMapping("/api/comments")
public class CommentsController {

    @GetMapping
    public ResponseEntity<Map<String, Object>> listComments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String type) {
        
        // Simulate invalid parameters
        if (size > 100) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Page size must not be greater than 100");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate empty result for a specific type
        if (type != null && type.equals("nonexistent")) {
            Map<String, Object> data = new HashMap<>();
            data.put("content", new ArrayList<>());
            data.put("totalElements", 0);
            data.put("totalPages", 0);
            data.put("size", size);
            data.put("number", page);

            Map<String, Object> response = new HashMap<>();
            response.put("status", true);
            response.put("code", 200);
            response.put("message", "Success");
            response.put("data", data);

            return ResponseEntity.ok(response);
        }

        // Normal response with comments
        List<Map<String, Object>> content = new ArrayList<>();
        
        Map<String, Object> comment1 = new HashMap<>();
        comment1.put("id", 101);
        comment1.put("content", "Great experience! Will definitely visit again.");
        comment1.put("rating", 5);
        comment1.put("type", "VISIT");
        comment1.put("userId", 1001);
        comment1.put("username", "johndoe");
        comment1.put("createdAt", "2023-10-15T10:00:00Z");
        content.add(comment1);
        
        Map<String, Object> comment2 = new HashMap<>();
        comment2.put("id", 102);
        comment2.put("content", "The staff was very helpful and friendly.");
        comment2.put("rating", 4);
        comment2.put("type", "SERVICE");
        comment2.put("userId", 1002);
        comment2.put("username", "janesmith");
        comment2.put("createdAt", "2023-10-14T15:30:00Z");
        content.add(comment2);

        Map<String, Object> data = new HashMap<>();
        data.put("content", content);
        data.put("totalElements", 2);
        data.put("totalPages", 1);
        data.put("size", size);
        data.put("number", page);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getCommentDetails(@PathVariable Long id) {
        // Simulate comment not found
        if (id == 999L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Comment not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Normal response with comment details
        Map<String, Object> data = new HashMap<>();
        data.put("id", id);
        data.put("content", "Great experience! Will definitely visit again.");
        data.put("rating", 5);
        data.put("type", "VISIT");
        data.put("userId", 1001);
        data.put("username", "johndoe");
        data.put("createdAt", "2023-10-15T10:00:00Z");
        data.put("updatedAt", null);
        data.put("status", "APPROVED");
        data.put("replyContent", null);
        data.put("replyAt", null);
        data.put("replyBy", null);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PostMapping
    public ResponseEntity<Map<String, Object>> createComment(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @RequestBody Map<String, Object> request) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Validate required fields
        if (!request.containsKey("content") || !request.containsKey("rating") || !request.containsKey("type")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Missing required fields");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        Integer rating = (Integer) request.get("rating");
        String type = (String) request.get("type");

        // Validate rating range
        if (rating < 1 || rating > 5) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Rating must be between 1 and 5");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Validate type
        if (!type.equals("VISIT") && !type.equals("SERVICE") && !type.equals("EXHIBITION")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid comment type. Must be one of: [VISIT, SERVICE, EXHIBITION]");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Successful comment creation
        Map<String, Object> data = new HashMap<>();
        data.put("id", 103);
        data.put("content", request.get("content"));
        data.put("rating", rating);
        data.put("type", type);
        data.put("userId", 1001);
        data.put("username", "johndoe");
        data.put("createdAt", "2023-10-16T09:30:00Z");
        data.put("status", "PENDING");

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 201);
        response.put("message", "Comment submitted successfully");
        response.put("data", data);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateComment(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @PathVariable Long id,
            @RequestBody Map<String, Object> request) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate comment not found
        if (id == 999L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Comment not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Simulate comment not owned by user
        if (id == 102L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 403);
            response.put("message", "You can only update your own comments");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
        }

        // Validate rating if provided
        if (request.containsKey("rating")) {
            Integer rating = (Integer) request.get("rating");
            if (rating < 1 || rating > 5) {
                Map<String, Object> response = new HashMap<>();
                response.put("status", false);
                response.put("code", 400);
                response.put("message", "Rating must be between 1 and 5");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
        }

        // Successful comment update
        Map<String, Object> data = new HashMap<>();
        data.put("id", id);
        data.put("content", request.containsKey("content") ? request.get("content") : "Great experience! Will definitely visit again.");
        data.put("rating", request.containsKey("rating") ? request.get("rating") : 5);
        data.put("type", "VISIT");
        data.put("userId", 1001);
        data.put("username", "johndoe");
        data.put("createdAt", "2023-10-15T10:00:00Z");
        data.put("updatedAt", "2023-10-16T11:45:00Z");
        data.put("status", "PENDING");

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Comment updated successfully");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteComment(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @PathVariable Long id) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate comment not found
        if (id == 999L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Comment not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Simulate comment not owned by user
        if (id == 102L) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 403);
            response.put("message", "You can only delete your own comments");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
        }

        // Successful comment deletion
        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Comment deleted successfully");

        return ResponseEntity.ok(response);
    }

    @GetMapping("/types")
    public ResponseEntity<Map<String, Object>> getCommentTypes() {
        List<Map<String, Object>> types = new ArrayList<>();
        
        Map<String, Object> type1 = new HashMap<>();
        type1.put("code", "VISIT");
        type1.put("name", "Visit Experience");
        types.add(type1);
        
        Map<String, Object> type2 = new HashMap<>();
        type2.put("code", "SERVICE");
        type2.put("name", "Service Quality");
        types.add(type2);
        
        Map<String, Object> type3 = new HashMap<>();
        type3.put("code", "EXHIBITION");
        type3.put("name", "Exhibition Feedback");
        types.add(type3);

        Map<String, Object> data = new HashMap<>();
        data.put("types", types);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }
}
