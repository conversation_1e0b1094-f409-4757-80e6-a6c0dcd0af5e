package com.spup.contract.app;

import io.restassured.module.mockmvc.RestAssuredMockMvc;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * Contract tests for the Customer API
 */
public class CustomerContractTest {

    private MockMvc mockMvc;
    private CustomerController customerController;

    @BeforeEach
    public void setup() {
        customerController = new CustomerController();
        mockMvc = MockMvcBuilders.standaloneSetup(customerController).build();
        RestAssuredMockMvc.mockMvc(mockMvc);
    }

    // Success scenarios

    @Test
    public void shouldGetCustomerProfile() throws Exception {
        mockMvc.perform(get("/api/customers/profile")
                .header("Authorization", "Bearer valid-token")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.customerId").value(1001))
                .andExpect(jsonPath("$.data.userId").value(2001))
                .andExpect(jsonPath("$.data.name").value("John Doe"))
                .andExpect(jsonPath("$.data.phone").value("13800138000"))
                .andExpect(jsonPath("$.data.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.data.address").value("123 Main St, Anytown, AN 12345"))
                .andExpect(jsonPath("$.data.membershipLevel").value("GOLD"))
                .andExpect(jsonPath("$.data.membershipPoints").value(1250))
                .andExpect(jsonPath("$.data.memberSince").value("2022-01-15"))
                .andExpect(jsonPath("$.data.lastVisit").value("2023-06-20"))
                .andExpect(jsonPath("$.data.visitCount").value(15));
    }

    @Test
    public void shouldCreateCustomerProfile() throws Exception {
        String requestBody = "{\n" +
                "  \"name\": \"Jane Smith\",\n" +
                "  \"phone\": \"13900139000\",\n" +
                "  \"email\": \"<EMAIL>\",\n" +
                "  \"address\": \"456 Oak St, Othertown, OT 67890\"\n" +
                "}";

        mockMvc.perform(post("/api/customers/profile")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(201))
                .andExpect(jsonPath("$.message").value("Customer profile created successfully"))
                .andExpect(jsonPath("$.data.customerId").value(1001))
                .andExpect(jsonPath("$.data.userId").value(2001))
                .andExpect(jsonPath("$.data.name").value("Jane Smith"))
                .andExpect(jsonPath("$.data.phone").value("13900139000"))
                .andExpect(jsonPath("$.data.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.data.address").value("456 Oak St, Othertown, OT 67890"))
                .andExpect(jsonPath("$.data.membershipLevel").value("BRONZE"))
                .andExpect(jsonPath("$.data.membershipPoints").value(0))
                .andExpect(jsonPath("$.data.memberSince").value("2023-07-01"));
    }

    @Test
    public void shouldUpdateCustomerProfile() throws Exception {
        String requestBody = "{\n" +
                "  \"name\": \"John Smith\",\n" +
                "  \"phone\": \"13900139000\",\n" +
                "  \"email\": \"<EMAIL>\",\n" +
                "  \"address\": \"789 Pine St, Newtown, NT 54321\"\n" +
                "}";

        mockMvc.perform(put("/api/customers/profile")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Customer profile updated successfully"))
                .andExpect(jsonPath("$.data.customerId").value(1001))
                .andExpect(jsonPath("$.data.userId").value(2001))
                .andExpect(jsonPath("$.data.name").value("John Smith"))
                .andExpect(jsonPath("$.data.phone").value("13900139000"))
                .andExpect(jsonPath("$.data.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.data.address").value("789 Pine St, Newtown, NT 54321"))
                .andExpect(jsonPath("$.data.membershipLevel").value("GOLD"))
                .andExpect(jsonPath("$.data.membershipPoints").value(1250))
                .andExpect(jsonPath("$.data.updatedAt").isString());
    }

    @Test
    public void shouldGetCustomerVisits() throws Exception {
        mockMvc.perform(get("/api/customers/visits")
                .header("Authorization", "Bearer valid-token")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].visitId").value(5001))
                .andExpect(jsonPath("$.data.content[0].date").value("2023-06-20"))
                .andExpect(jsonPath("$.data.content[0].time").value("10:30"))
                .andExpect(jsonPath("$.data.content[0].duration").value(120))
                .andExpect(jsonPath("$.data.content[0].type").value("GENERAL"))
                .andExpect(jsonPath("$.data.content[0].ticketNo").value("T123456789"))
                .andExpect(jsonPath("$.data.content[0].pointsEarned").value(50))
                .andExpect(jsonPath("$.data.content[1].visitId").value(5002))
                .andExpect(jsonPath("$.data.content[1].date").value("2023-05-15"))
                .andExpect(jsonPath("$.data.content[1].type").value("EXHIBITION"))
                .andExpect(jsonPath("$.data.totalElements").value(15))
                .andExpect(jsonPath("$.data.totalPages").value(8))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));
    }

    @Test
    public void shouldReturnEmptyVisitsWhenNoVisitsExist() throws Exception {
        mockMvc.perform(get("/api/customers/visits")
                .header("Authorization", "Bearer valid-token")
                .param("page", "11")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content").isEmpty())
                .andExpect(jsonPath("$.data.totalElements").value(0))
                .andExpect(jsonPath("$.data.totalPages").value(0));
    }

    @Test
    public void shouldGetPointsHistory() throws Exception {
        mockMvc.perform(get("/api/customers/points/history")
                .header("Authorization", "Bearer valid-token")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].id").value(7001))
                .andExpect(jsonPath("$.data.content[0].date").value("2023-06-20"))
                .andExpect(jsonPath("$.data.content[0].type").value("EARNED"))
                .andExpect(jsonPath("$.data.content[0].source").value("VISIT"))
                .andExpect(jsonPath("$.data.content[0].points").value(50))
                .andExpect(jsonPath("$.data.content[0].balance").value(1250))
                .andExpect(jsonPath("$.data.content[1].id").value(7002))
                .andExpect(jsonPath("$.data.content[1].type").value("EARNED"))
                .andExpect(jsonPath("$.data.content[1].source").value("EXHIBITION"))
                .andExpect(jsonPath("$.data.content[2].id").value(7003))
                .andExpect(jsonPath("$.data.content[2].type").value("REDEEMED"))
                .andExpect(jsonPath("$.data.content[2].points").value(-100))
                .andExpect(jsonPath("$.data.totalElements").value(20))
                .andExpect(jsonPath("$.data.totalPages").value(7))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));
    }

    @Test
    public void shouldReturnEmptyPointsHistoryWhenNoHistoryExists() throws Exception {
        mockMvc.perform(get("/api/customers/points/history")
                .header("Authorization", "Bearer valid-token")
                .param("page", "11")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content").isEmpty())
                .andExpect(jsonPath("$.data.totalElements").value(0))
                .andExpect(jsonPath("$.data.totalPages").value(0));
    }

    @Test
    public void shouldGetMembershipLevels() throws Exception {
        mockMvc.perform(get("/api/customers/membership/levels")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.levels[0].code").value("BRONZE"))
                .andExpect(jsonPath("$.data.levels[0].name").value("Bronze Membership"))
                .andExpect(jsonPath("$.data.levels[0].pointsRequired").value(0))
                .andExpect(jsonPath("$.data.levels[1].code").value("SILVER"))
                .andExpect(jsonPath("$.data.levels[1].pointsRequired").value(500))
                .andExpect(jsonPath("$.data.levels[2].code").value("GOLD"))
                .andExpect(jsonPath("$.data.levels[2].pointsRequired").value(1000))
                .andExpect(jsonPath("$.data.levels[3].code").value("PLATINUM"))
                .andExpect(jsonPath("$.data.levels[3].pointsRequired").value(2000));
    }

    // Error scenarios

    @Test
    public void shouldReturnUnauthorizedWhenGetProfileWithoutToken() throws Exception {
        mockMvc.perform(get("/api/customers/profile")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenGetProfileWithInvalidToken() throws Exception {
        mockMvc.perform(get("/api/customers/profile")
                .header("Authorization", "Bearer invalid")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Invalid token"));
    }

    @Test
    public void shouldReturnNotFoundWhenProfileDoesNotExist() throws Exception {
        mockMvc.perform(get("/api/customers/profile")
                .header("Authorization", "Bearer not-found")
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Customer profile not found"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenCreateProfileWithoutToken() throws Exception {
        String requestBody = "{\n" +
                "  \"name\": \"Jane Smith\",\n" +
                "  \"phone\": \"13900139000\",\n" +
                "  \"email\": \"<EMAIL>\"\n" +
                "}";

        mockMvc.perform(post("/api/customers/profile")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnBadRequestWhenCreateProfileMissingRequiredFields() throws Exception {
        String requestBody = "{\n" +
                "  \"name\": \"Jane Smith\"\n" +
                "}";

        mockMvc.perform(post("/api/customers/profile")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Missing required fields"));
    }

    @Test
    public void shouldReturnBadRequestWhenCreateProfileInvalidPhoneFormat() throws Exception {
        String requestBody = "{\n" +
                "  \"name\": \"Jane Smith\",\n" +
                "  \"phone\": \"12345\",\n" +
                "  \"email\": \"<EMAIL>\"\n" +
                "}";

        mockMvc.perform(post("/api/customers/profile")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid phone number format"));
    }

    @Test
    public void shouldReturnBadRequestWhenCreateProfileInvalidEmailFormat() throws Exception {
        String requestBody = "{\n" +
                "  \"name\": \"Jane Smith\",\n" +
                "  \"phone\": \"13900139000\",\n" +
                "  \"email\": \"invalid-email\"\n" +
                "}";

        mockMvc.perform(post("/api/customers/profile")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid email format"));
    }

    @Test
    public void shouldReturnConflictWhenProfileAlreadyExists() throws Exception {
        String requestBody = "{\n" +
                "  \"name\": \"Jane Smith\",\n" +
                "  \"phone\": \"13900139000\",\n" +
                "  \"email\": \"<EMAIL>\"\n" +
                "}";

        mockMvc.perform(post("/api/customers/profile")
                .header("Authorization", "Bearer existing")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(409))
                .andExpect(jsonPath("$.message").value("Customer profile already exists"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenUpdateProfileWithoutToken() throws Exception {
        String requestBody = "{\n" +
                "  \"name\": \"John Smith\"\n" +
                "}";

        mockMvc.perform(put("/api/customers/profile")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnNotFoundWhenUpdateProfileDoesNotExist() throws Exception {
        String requestBody = "{\n" +
                "  \"name\": \"John Smith\"\n" +
                "}";

        mockMvc.perform(put("/api/customers/profile")
                .header("Authorization", "Bearer not-found")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Customer profile not found"));
    }

    @Test
    public void shouldReturnBadRequestWhenUpdateProfileInvalidPhoneFormat() throws Exception {
        String requestBody = "{\n" +
                "  \"phone\": \"12345\"\n" +
                "}";

        mockMvc.perform(put("/api/customers/profile")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid phone number format"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenGetVisitsWithoutToken() throws Exception {
        mockMvc.perform(get("/api/customers/visits")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnNotFoundWhenGetVisitsProfileDoesNotExist() throws Exception {
        mockMvc.perform(get("/api/customers/visits")
                .header("Authorization", "Bearer not-found")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Customer profile not found"));
    }

    @Test
    public void shouldReturnBadRequestWhenGetVisitsInvalidPageSize() throws Exception {
        mockMvc.perform(get("/api/customers/visits")
                .header("Authorization", "Bearer valid-token")
                .param("page", "0")
                .param("size", "101")
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Page size must not be greater than 100"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenGetPointsHistoryWithoutToken() throws Exception {
        mockMvc.perform(get("/api/customers/points/history")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnNotFoundWhenGetPointsHistoryProfileDoesNotExist() throws Exception {
        mockMvc.perform(get("/api/customers/points/history")
                .header("Authorization", "Bearer not-found")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Customer profile not found"));
    }

    @Test
    public void shouldReturnBadRequestWhenGetPointsHistoryInvalidPageSize() throws Exception {
        mockMvc.perform(get("/api/customers/points/history")
                .header("Authorization", "Bearer valid-token")
                .param("page", "0")
                .param("size", "101")
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Page size must not be greater than 100"));
    }
}
