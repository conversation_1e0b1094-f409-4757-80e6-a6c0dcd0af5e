package com.spup.contract.app;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test controller for customer contracts
 */
@RestController
@RequestMapping("/api/customers")
public class CustomerController {

    @GetMapping("/profile")
    public ResponseEntity<Map<String, Object>> getCustomerProfile(
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate invalid token
        if (authHeader.equals("Bearer invalid")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Invalid token");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate customer not found
        if (authHeader.equals("Bearer not-found")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Customer profile not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Normal response with customer profile
        Map<String, Object> data = new HashMap<>();
        data.put("customerId", 1001);
        data.put("userId", 2001);
        data.put("name", "John Doe");
        data.put("phone", "13800138000");
        data.put("email", "<EMAIL>");
        data.put("address", "123 Main St, Anytown, AN 12345");
        data.put("membershipLevel", "GOLD");
        data.put("membershipPoints", 1250);
        data.put("memberSince", "2022-01-15");
        data.put("lastVisit", "2023-06-20");
        data.put("visitCount", 15);
        data.put("createdAt", "2022-01-15T10:00:00Z");
        data.put("updatedAt", "2023-06-20T14:30:00Z");

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PostMapping("/profile")
    public ResponseEntity<Map<String, Object>> createCustomerProfile(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @RequestBody Map<String, Object> request) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Validate required fields
        if (!request.containsKey("name") || !request.containsKey("phone") || !request.containsKey("email")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Missing required fields");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        String phone = (String) request.get("phone");
        String email = (String) request.get("email");

        // Validate phone number format
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid phone number format");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Validate email format
        if (!email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid email format");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate profile already exists
        if (authHeader.equals("Bearer existing")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 409);
            response.put("message", "Customer profile already exists");
            return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
        }

        // Successful profile creation
        Map<String, Object> data = new HashMap<>();
        data.put("customerId", 1001);
        data.put("userId", 2001);
        data.put("name", request.get("name"));
        data.put("phone", phone);
        data.put("email", email);
        data.put("address", request.containsKey("address") ? request.get("address") : null);
        data.put("membershipLevel", "BRONZE");
        data.put("membershipPoints", 0);
        data.put("memberSince", "2023-07-01");
        data.put("createdAt", "2023-07-01T10:00:00Z");

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 201);
        response.put("message", "Customer profile created successfully");
        response.put("data", data);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @PutMapping("/profile")
    public ResponseEntity<Map<String, Object>> updateCustomerProfile(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @RequestBody Map<String, Object> request) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate customer not found
        if (authHeader.equals("Bearer not-found")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Customer profile not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Validate phone number format if provided
        if (request.containsKey("phone")) {
            String phone = (String) request.get("phone");
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                Map<String, Object> response = new HashMap<>();
                response.put("status", false);
                response.put("code", 400);
                response.put("message", "Invalid phone number format");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
        }

        // Validate email format if provided
        if (request.containsKey("email")) {
            String email = (String) request.get("email");
            if (!email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
                Map<String, Object> response = new HashMap<>();
                response.put("status", false);
                response.put("code", 400);
                response.put("message", "Invalid email format");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
        }

        // Successful profile update
        Map<String, Object> data = new HashMap<>();
        data.put("customerId", 1001);
        data.put("userId", 2001);
        data.put("name", request.containsKey("name") ? request.get("name") : "John Doe");
        data.put("phone", request.containsKey("phone") ? request.get("phone") : "13800138000");
        data.put("email", request.containsKey("email") ? request.get("email") : "<EMAIL>");
        data.put("address", request.containsKey("address") ? request.get("address") : "123 Main St, Anytown, AN 12345");
        data.put("membershipLevel", "GOLD");
        data.put("membershipPoints", 1250);
        data.put("memberSince", "2022-01-15");
        data.put("updatedAt", "2023-07-01T11:30:00Z");

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Customer profile updated successfully");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/visits")
    public ResponseEntity<Map<String, Object>> getCustomerVisits(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate customer not found
        if (authHeader.equals("Bearer not-found")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Customer profile not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Simulate invalid parameters
        if (size > 100) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Page size must not be greater than 100");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate empty result for a specific page
        if (page > 10) {
            Map<String, Object> data = new HashMap<>();
            data.put("content", new ArrayList<>());
            data.put("totalElements", 0);
            data.put("totalPages", 0);
            data.put("size", size);
            data.put("number", page);

            Map<String, Object> response = new HashMap<>();
            response.put("status", true);
            response.put("code", 200);
            response.put("message", "Success");
            response.put("data", data);

            return ResponseEntity.ok(response);
        }

        // Normal response with customer visits
        List<Map<String, Object>> content = new ArrayList<>();
        
        Map<String, Object> visit1 = new HashMap<>();
        visit1.put("visitId", 5001);
        visit1.put("date", "2023-06-20");
        visit1.put("time", "10:30");
        visit1.put("duration", 120);
        visit1.put("type", "GENERAL");
        visit1.put("ticketNo", "T123456789");
        visit1.put("pointsEarned", 50);
        content.add(visit1);
        
        Map<String, Object> visit2 = new HashMap<>();
        visit2.put("visitId", 5002);
        visit2.put("date", "2023-05-15");
        visit2.put("time", "14:00");
        visit2.put("duration", 90);
        visit2.put("type", "EXHIBITION");
        visit2.put("ticketNo", "T987654321");
        visit2.put("pointsEarned", 75);
        content.add(visit2);

        Map<String, Object> data = new HashMap<>();
        data.put("content", content);
        data.put("totalElements", 15);
        data.put("totalPages", 8);
        data.put("size", size);
        data.put("number", page);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/points/history")
    public ResponseEntity<Map<String, Object>> getPointsHistory(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate customer not found
        if (authHeader.equals("Bearer not-found")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Customer profile not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Simulate invalid parameters
        if (size > 100) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Page size must not be greater than 100");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate empty result for a specific page
        if (page > 10) {
            Map<String, Object> data = new HashMap<>();
            data.put("content", new ArrayList<>());
            data.put("totalElements", 0);
            data.put("totalPages", 0);
            data.put("size", size);
            data.put("number", page);

            Map<String, Object> response = new HashMap<>();
            response.put("status", true);
            response.put("code", 200);
            response.put("message", "Success");
            response.put("data", data);

            return ResponseEntity.ok(response);
        }

        // Normal response with points history
        List<Map<String, Object>> content = new ArrayList<>();
        
        Map<String, Object> points1 = new HashMap<>();
        points1.put("id", 7001);
        points1.put("date", "2023-06-20");
        points1.put("type", "EARNED");
        points1.put("source", "VISIT");
        points1.put("description", "Museum visit");
        points1.put("points", 50);
        points1.put("balance", 1250);
        content.add(points1);
        
        Map<String, Object> points2 = new HashMap<>();
        points2.put("id", 7002);
        points2.put("date", "2023-05-15");
        points2.put("type", "EARNED");
        points2.put("source", "EXHIBITION");
        points2.put("description", "Special exhibition visit");
        points2.put("points", 75);
        points2.put("balance", 1200);
        content.add(points2);
        
        Map<String, Object> points3 = new HashMap<>();
        points3.put("id", 7003);
        points3.put("date", "2023-04-10");
        points3.put("type", "REDEEMED");
        points3.put("source", "GIFT_SHOP");
        points3.put("description", "Gift shop purchase discount");
        points3.put("points", -100);
        points3.put("balance", 1125);
        content.add(points3);

        Map<String, Object> data = new HashMap<>();
        data.put("content", content);
        data.put("totalElements", 20);
        data.put("totalPages", 7);
        data.put("size", size);
        data.put("number", page);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/membership/levels")
    public ResponseEntity<Map<String, Object>> getMembershipLevels() {
        List<Map<String, Object>> levels = new ArrayList<>();
        
        Map<String, Object> level1 = new HashMap<>();
        level1.put("code", "BRONZE");
        level1.put("name", "Bronze Membership");
        level1.put("pointsRequired", 0);
        level1.put("benefits", "Basic member benefits");
        levels.add(level1);
        
        Map<String, Object> level2 = new HashMap<>();
        level2.put("code", "SILVER");
        level2.put("name", "Silver Membership");
        level2.put("pointsRequired", 500);
        level2.put("benefits", "Bronze benefits + 5% discount on gift shop purchases");
        levels.add(level2);
        
        Map<String, Object> level3 = new HashMap<>();
        level3.put("code", "GOLD");
        level3.put("name", "Gold Membership");
        level3.put("pointsRequired", 1000);
        level3.put("benefits", "Silver benefits + free audio guide + priority booking");
        levels.add(level3);
        
        Map<String, Object> level4 = new HashMap<>();
        level4.put("code", "PLATINUM");
        level4.put("name", "Platinum Membership");
        level4.put("pointsRequired", 2000);
        level4.put("benefits", "Gold benefits + free guest pass + exclusive events");
        levels.add(level4);

        Map<String, Object> data = new HashMap<>();
        data.put("levels", levels);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }
}
