package com.spup.contract.app;

import io.restassured.module.mockmvc.RestAssuredMockMvc;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * Contract tests for the goods API
 */
public class GoodsContractTest {

    private MockMvc mockMvc;
    private GoodsController goodsController;

    @BeforeEach
    public void setup() {
        goodsController = new GoodsController();
        mockMvc = MockMvcBuilders.standaloneSetup(goodsController).build();
        RestAssuredMockMvc.mockMvc(mockMvc);
    }

    // Success scenarios

    @Test
    public void shouldListGoods() throws Exception {
        mockMvc.perform(get("/api/goods")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].id").value(201))
                .andExpect(jsonPath("$.data.content[0].name").value("Museum Guide Book"))
                .andExpect(jsonPath("$.data.content[0].price").value(29.99))
                .andExpect(jsonPath("$.data.content[0].category").value("books"))
                .andExpect(jsonPath("$.data.content[0].inStock").value(true))
                .andExpect(jsonPath("$.data.content[1].id").value(202))
                .andExpect(jsonPath("$.data.content[1].name").value("Museum Souvenir Mug"))
                .andExpect(jsonPath("$.data.totalElements").value(2))
                .andExpect(jsonPath("$.data.totalPages").value(1))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));
    }

    @Test
    public void shouldListGoodsByCategory() throws Exception {
        mockMvc.perform(get("/api/goods")
                .param("category", "books")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].category").value("books"))
                .andExpect(jsonPath("$.data.totalElements").value(2));
    }

    @Test
    public void shouldGetGoodsDetails() throws Exception {
        mockMvc.perform(get("/api/goods/201")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.id").value(201))
                .andExpect(jsonPath("$.data.name").value("Museum Guide Book"))
                .andExpect(jsonPath("$.data.description").isString())
                .andExpect(jsonPath("$.data.price").value(29.99))
                .andExpect(jsonPath("$.data.category").value("books"))
                .andExpect(jsonPath("$.data.imageUrl").isString())
                .andExpect(jsonPath("$.data.inStock").value(true))
                .andExpect(jsonPath("$.data.stockQuantity").value(150))
                .andExpect(jsonPath("$.data.createdAt").isString())
                .andExpect(jsonPath("$.data.updatedAt").isString())
                .andExpect(jsonPath("$.data.relatedItems").isArray())
                .andExpect(jsonPath("$.data.relatedItems.length()").value(2));
    }

    @Test
    public void shouldGetCategories() throws Exception {
        mockMvc.perform(get("/api/goods/categories")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.categories").isArray())
                .andExpect(jsonPath("$.data.categories.length()").value(3))
                .andExpect(jsonPath("$.data.categories[0].id").value(1))
                .andExpect(jsonPath("$.data.categories[0].name").value("books"))
                .andExpect(jsonPath("$.data.categories[0].displayName").value("Books"));
    }

    @Test
    public void shouldSearchGoods() throws Exception {
        String requestBody = "{\n" +
                "  \"keyword\": \"museum\",\n" +
                "  \"category\": \"books\"\n" +
                "}";

        mockMvc.perform(post("/api/goods/search")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content[0].name").value("Museum Guide Book"))
                .andExpect(jsonPath("$.data.keyword").value("museum"))
                .andExpect(jsonPath("$.data.category").value("books"));
    }

    // Edge cases

    @Test
    public void shouldReturnEmptyListWhenNoGoodsInCategory() throws Exception {
        mockMvc.perform(get("/api/goods")
                .param("category", "non-existent")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content").isEmpty())
                .andExpect(jsonPath("$.data.totalElements").value(0))
                .andExpect(jsonPath("$.data.totalPages").value(0));
    }

    @Test
    public void shouldReturnEmptySearchResults() throws Exception {
        String requestBody = "{\n" +
                "  \"keyword\": \"nonexistent\"\n" +
                "}";

        mockMvc.perform(post("/api/goods/search")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("No results found"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content").isEmpty())
                .andExpect(jsonPath("$.data.totalElements").value(0));
    }

    // Error scenarios

    @Test
    public void shouldReturnNotFoundWhenGoodsDoNotExist() throws Exception {
        mockMvc.perform(get("/api/goods/999")
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Goods not found"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenAccessingWithInvalidCredentials() throws Exception {
        mockMvc.perform(get("/api/goods")
                .param("page", "-1")  // Trigger unauthorized scenario
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnBadRequestWhenPageSizeExceedsLimit() throws Exception {
        mockMvc.perform(get("/api/goods")
                .param("page", "0")
                .param("size", "101")  // Exceeds limit of 100
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Page size must not be greater than 100"));
    }

    @Test
    public void shouldReturnBadRequestWhenSearchMissingKeyword() throws Exception {
        String requestBody = "{\n" +
                "  \"category\": \"books\"\n" +
                "}";

        mockMvc.perform(post("/api/goods/search")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Keyword is required"));
    }
}
