package com.spup.contract.app;

import io.restassured.module.mockmvc.RestAssuredMockMvc;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * Contract tests for the Turnstiles API
 */
public class TurnstilesContractTest {

    private MockMvc mockMvc;
    private TurnstilesController turnstilesController;

    @BeforeEach
    public void setup() {
        turnstilesController = new TurnstilesController();
        mockMvc = MockMvcBuilders.standaloneSetup(turnstilesController).build();
        RestAssuredMockMvc.mockMvc(mockMvc);
    }

    // Success scenarios

    @Test
    public void shouldValidateTicket() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T123456789012\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/validate")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Ticket is valid"))
                .andExpect(jsonPath("$.data.ticketNo").value("T123456789012"))
                .andExpect(jsonPath("$.data.valid").value(true))
                .andExpect(jsonPath("$.data.type").value("GENERAL_ADMISSION"))
                .andExpect(jsonPath("$.data.visitorName").value("John Doe"))
                .andExpect(jsonPath("$.data.visitorCount").value(1))
                .andExpect(jsonPath("$.data.validDate").value("2023-07-01"))
                .andExpect(jsonPath("$.data.validTime").value("09:00-17:00"));
    }

    @Test
    public void shouldRecordEntry() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T123456789012\",\n" +
                "  \"turnstileId\": \"ENTRANCE-1\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/entry")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Entry recorded successfully"))
                .andExpect(jsonPath("$.data.entryId").value(5001))
                .andExpect(jsonPath("$.data.ticketNo").value("T123456789012"))
                .andExpect(jsonPath("$.data.turnstileId").value("ENTRANCE-1"))
                .andExpect(jsonPath("$.data.entryTime").isString())
                .andExpect(jsonPath("$.data.visitorCount").value(1));
    }

    @Test
    public void shouldRecordExit() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T123456789012\",\n" +
                "  \"turnstileId\": \"EXIT-1\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/exit")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Exit recorded successfully"))
                .andExpect(jsonPath("$.data.exitId").value(6001))
                .andExpect(jsonPath("$.data.entryId").value(5001))
                .andExpect(jsonPath("$.data.ticketNo").value("T123456789012"))
                .andExpect(jsonPath("$.data.turnstileId").value("EXIT-1"))
                .andExpect(jsonPath("$.data.entryTime").isString())
                .andExpect(jsonPath("$.data.exitTime").isString())
                .andExpect(jsonPath("$.data.duration").value(150));
    }

    @Test
    public void shouldGetTurnstilesStatus() throws Exception {
        mockMvc.perform(get("/api/turnstiles/status")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.turnstiles").isArray())
                .andExpect(jsonPath("$.data.turnstiles.length()").value(4))
                .andExpect(jsonPath("$.data.turnstiles[0].id").value("ENTRANCE-1"))
                .andExpect(jsonPath("$.data.turnstiles[0].location").value("Main Entrance"))
                .andExpect(jsonPath("$.data.turnstiles[0].type").value("ENTRY"))
                .andExpect(jsonPath("$.data.turnstiles[0].status").value("ACTIVE"))
                .andExpect(jsonPath("$.data.turnstiles[3].id").value("EXIT-2"))
                .andExpect(jsonPath("$.data.turnstiles[3].status").value("INACTIVE"))
                .andExpect(jsonPath("$.data.activeCount").value(3))
                .andExpect(jsonPath("$.data.inactiveCount").value(1))
                .andExpect(jsonPath("$.data.totalCount").value(4))
                .andExpect(jsonPath("$.data.timestamp").isString());
    }

    @Test
    public void shouldGetTurnstilesStatisticsWithDefaultParameters() throws Exception {
        mockMvc.perform(get("/api/turnstiles/statistics")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.date").value("2023-07-01"))
                .andExpect(jsonPath("$.data.interval").value("hourly"))
                .andExpect(jsonPath("$.data.statistics").isArray())
                .andExpect(jsonPath("$.data.statistics.length()").value(9))
                .andExpect(jsonPath("$.data.summary.totalEntries").value(2500))
                .andExpect(jsonPath("$.data.summary.totalExits").value(2450))
                .andExpect(jsonPath("$.data.summary.averageVisitDuration").value(125))
                .andExpect(jsonPath("$.data.summary.peakEntryTime").value("11:00"))
                .andExpect(jsonPath("$.data.summary.peakExitTime").value("16:00"));
    }

    @Test
    public void shouldGetTurnstilesStatisticsWithCustomParameters() throws Exception {
        mockMvc.perform(get("/api/turnstiles/statistics")
                .param("date", "2023-07-01")
                .param("interval", "daily")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.date").value("2023-07-01"))
                .andExpect(jsonPath("$.data.interval").value("daily"))
                .andExpect(jsonPath("$.data.statistics").isArray())
                .andExpect(jsonPath("$.data.statistics.length()").value(7))
                .andExpect(jsonPath("$.data.statistics[0].date").value("2023-07-01"));
    }

    // Error scenarios

    @Test
    public void shouldReturnBadRequestWhenValidateTicketMissingTicketNo() throws Exception {
        String requestBody = "{}";

        mockMvc.perform(post("/api/turnstiles/validate")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Ticket number is required"));
    }

    @Test
    public void shouldReturnBadRequestWhenValidateTicketInvalidFormat() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T1234\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/validate")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid ticket number format"));
    }

    @Test
    public void shouldReturnNotFoundWhenValidateTicketNotFound() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T00000000000\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/validate")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Ticket not found"));
    }

    @Test
    public void shouldReturnBadRequestWhenValidateTicketAlreadyUsed() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T00000000001\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/validate")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Ticket already used"));
    }

    @Test
    public void shouldReturnBadRequestWhenValidateTicketExpired() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T00000000002\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/validate")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Ticket expired"));
    }

    @Test
    public void shouldReturnBadRequestWhenValidateTicketNotValidForToday() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T00000000003\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/validate")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Ticket not valid for today"));
    }

    @Test
    public void shouldReturnBadRequestWhenRecordEntryMissingRequiredFields() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T123456789012\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/entry")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Ticket number and turnstile ID are required"));
    }

    @Test
    public void shouldReturnBadRequestWhenRecordEntryInvalidTicketFormat() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T1234\",\n" +
                "  \"turnstileId\": \"ENTRANCE-1\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/entry")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid ticket number format"));
    }

    @Test
    public void shouldReturnNotFoundWhenRecordEntryTicketNotFound() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T00000000000\",\n" +
                "  \"turnstileId\": \"ENTRANCE-1\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/entry")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Ticket not found"));
    }

    @Test
    public void shouldReturnBadRequestWhenRecordEntryTicketAlreadyUsed() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T00000000001\",\n" +
                "  \"turnstileId\": \"ENTRANCE-1\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/entry")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Ticket already used"));
    }

    @Test
    public void shouldReturnBadRequestWhenRecordEntryInvalidTurnstileId() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T123456789012\",\n" +
                "  \"turnstileId\": \"INVALID\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/entry")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid turnstile ID"));
    }

    @Test
    public void shouldReturnBadRequestWhenRecordExitMissingRequiredFields() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T123456789012\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/exit")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Ticket number and turnstile ID are required"));
    }

    @Test
    public void shouldReturnNotFoundWhenRecordExitTicketNotFound() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T00000000000\",\n" +
                "  \"turnstileId\": \"EXIT-1\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/exit")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Ticket not found"));
    }

    @Test
    public void shouldReturnBadRequestWhenRecordExitNoEntryRecord() throws Exception {
        String requestBody = "{\n" +
                "  \"ticketNo\": \"T00000000004\",\n" +
                "  \"turnstileId\": \"EXIT-1\"\n" +
                "}";

        mockMvc.perform(post("/api/turnstiles/exit")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("No entry record found for this ticket"));
    }

    @Test
    public void shouldReturnBadRequestWhenGetStatisticsInvalidDateFormat() throws Exception {
        mockMvc.perform(get("/api/turnstiles/statistics")
                .param("date", "2023/07/01")
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid date format. Use YYYY-MM-DD"));
    }

    @Test
    public void shouldReturnBadRequestWhenGetStatisticsInvalidInterval() throws Exception {
        mockMvc.perform(get("/api/turnstiles/statistics")
                .param("interval", "invalid")
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid interval. Must be one of: hourly, daily, weekly, monthly"));
    }
}
