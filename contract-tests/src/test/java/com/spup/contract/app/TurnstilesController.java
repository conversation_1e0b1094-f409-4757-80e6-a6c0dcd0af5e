package com.spup.contract.app;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test controller for turnstiles contracts
 */
@RestController
@RequestMapping("/api/turnstiles")
public class TurnstilesController {

    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateTicket(@RequestBody Map<String, Object> request) {
        // Validate required fields
        if (!request.containsKey("ticketNo")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Ticket number is required");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        String ticketNo = (String) request.get("ticketNo");

        // Simulate invalid ticket format
        if (ticketNo.length() != 12) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid ticket number format");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate ticket not found
        if (ticketNo.equals("T00000000000")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Ticket not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Simulate ticket already used
        if (ticketNo.equals("T00000000001")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Ticket already used");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate ticket expired
        if (ticketNo.equals("T00000000002")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Ticket expired");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate ticket not valid for today
        if (ticketNo.equals("T00000000003")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Ticket not valid for today");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Successful validation
        Map<String, Object> data = new HashMap<>();
        data.put("ticketNo", ticketNo);
        data.put("valid", true);
        data.put("type", "GENERAL_ADMISSION");
        data.put("visitorName", "John Doe");
        data.put("visitorCount", 1);
        data.put("validDate", "2023-07-01");
        data.put("validTime", "09:00-17:00");
        data.put("specialExhibitions", new ArrayList<>());

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Ticket is valid");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PostMapping("/entry")
    public ResponseEntity<Map<String, Object>> recordEntry(@RequestBody Map<String, Object> request) {
        // Validate required fields
        if (!request.containsKey("ticketNo") || !request.containsKey("turnstileId")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Ticket number and turnstile ID are required");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        String ticketNo = (String) request.get("ticketNo");
        String turnstileId = (String) request.get("turnstileId");

        // Simulate invalid ticket format
        if (ticketNo.length() != 12) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid ticket number format");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate ticket not found
        if (ticketNo.equals("T00000000000")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Ticket not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Simulate ticket already used
        if (ticketNo.equals("T00000000001")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Ticket already used");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate invalid turnstile ID
        if (turnstileId.equals("INVALID")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid turnstile ID");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Successful entry
        Map<String, Object> data = new HashMap<>();
        data.put("entryId", 5001);
        data.put("ticketNo", ticketNo);
        data.put("turnstileId", turnstileId);
        data.put("entryTime", "2023-07-01T10:15:30Z");
        data.put("visitorCount", 1);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Entry recorded successfully");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PostMapping("/exit")
    public ResponseEntity<Map<String, Object>> recordExit(@RequestBody Map<String, Object> request) {
        // Validate required fields
        if (!request.containsKey("ticketNo") || !request.containsKey("turnstileId")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Ticket number and turnstile ID are required");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        String ticketNo = (String) request.get("ticketNo");
        String turnstileId = (String) request.get("turnstileId");

        // Simulate invalid ticket format
        if (ticketNo.length() != 12) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid ticket number format");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate ticket not found
        if (ticketNo.equals("T00000000000")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Ticket not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // Simulate no entry record found
        if (ticketNo.equals("T00000000004")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "No entry record found for this ticket");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate invalid turnstile ID
        if (turnstileId.equals("INVALID")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid turnstile ID");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Successful exit
        Map<String, Object> data = new HashMap<>();
        data.put("exitId", 6001);
        data.put("entryId", 5001);
        data.put("ticketNo", ticketNo);
        data.put("turnstileId", turnstileId);
        data.put("entryTime", "2023-07-01T10:15:30Z");
        data.put("exitTime", "2023-07-01T12:45:15Z");
        data.put("duration", 150); // in minutes

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Exit recorded successfully");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getTurnstilesStatus() {
        List<Map<String, Object>> turnstiles = new ArrayList<>();
        
        Map<String, Object> turnstile1 = new HashMap<>();
        turnstile1.put("id", "ENTRANCE-1");
        turnstile1.put("location", "Main Entrance");
        turnstile1.put("type", "ENTRY");
        turnstile1.put("status", "ACTIVE");
        turnstile1.put("lastPing", "2023-07-01T12:55:30Z");
        turnstiles.add(turnstile1);
        
        Map<String, Object> turnstile2 = new HashMap<>();
        turnstile2.put("id", "ENTRANCE-2");
        turnstile2.put("location", "Main Entrance");
        turnstile2.put("type", "ENTRY");
        turnstile2.put("status", "ACTIVE");
        turnstile2.put("lastPing", "2023-07-01T12:56:15Z");
        turnstiles.add(turnstile2);
        
        Map<String, Object> turnstile3 = new HashMap<>();
        turnstile3.put("id", "EXIT-1");
        turnstile3.put("location", "Main Exit");
        turnstile3.put("type", "EXIT");
        turnstile3.put("status", "ACTIVE");
        turnstile3.put("lastPing", "2023-07-01T12:54:45Z");
        turnstiles.add(turnstile3);
        
        Map<String, Object> turnstile4 = new HashMap<>();
        turnstile4.put("id", "EXIT-2");
        turnstile4.put("location", "Main Exit");
        turnstile4.put("type", "EXIT");
        turnstile4.put("status", "INACTIVE");
        turnstile4.put("lastPing", "2023-07-01T10:30:00Z");
        turnstiles.add(turnstile4);

        Map<String, Object> data = new HashMap<>();
        data.put("turnstiles", turnstiles);
        data.put("activeCount", 3);
        data.put("inactiveCount", 1);
        data.put("totalCount", 4);
        data.put("timestamp", "2023-07-01T13:00:00Z");

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getTurnstilesStatistics(
            @RequestParam(required = false) String date,
            @RequestParam(defaultValue = "hourly") String interval) {
        
        // Simulate invalid date format
        if (date != null && !date.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid date format. Use YYYY-MM-DD");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate invalid interval
        if (!interval.equals("hourly") && !interval.equals("daily") && !interval.equals("weekly") && !interval.equals("monthly")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid interval. Must be one of: hourly, daily, weekly, monthly");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Use current date if not provided
        String effectiveDate = date != null ? date : "2023-07-01";

        // Generate statistics based on interval
        List<Map<String, Object>> statistics = new ArrayList<>();
        
        if (interval.equals("hourly")) {
            for (int i = 9; i <= 17; i++) {
                Map<String, Object> hourStat = new HashMap<>();
                hourStat.put("time", String.format("%02d:00", i));
                hourStat.put("entries", 50 + (int)(Math.random() * 50));
                hourStat.put("exits", 30 + (int)(Math.random() * 50));
                statistics.add(hourStat);
            }
        } else if (interval.equals("daily")) {
            for (int i = 1; i <= 7; i++) {
                Map<String, Object> dayStat = new HashMap<>();
                dayStat.put("date", String.format("2023-07-%02d", i));
                dayStat.put("entries", 300 + (int)(Math.random() * 200));
                dayStat.put("exits", 280 + (int)(Math.random() * 200));
                statistics.add(dayStat);
            }
        }

        Map<String, Object> summary = new HashMap<>();
        summary.put("totalEntries", 2500);
        summary.put("totalExits", 2450);
        summary.put("averageVisitDuration", 125); // in minutes
        summary.put("peakEntryTime", "11:00");
        summary.put("peakExitTime", "16:00");

        Map<String, Object> data = new HashMap<>();
        data.put("date", effectiveDate);
        data.put("interval", interval);
        data.put("statistics", statistics);
        data.put("summary", summary);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }
}
