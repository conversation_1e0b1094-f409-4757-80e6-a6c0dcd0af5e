package com.spup.contract.app;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test controller for user contracts
 */
@RestController
@RequestMapping("/api/users")
public class UserController {

    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, Object> request) {
        // Validate required fields
        if (!request.containsKey("username") || !request.containsKey("password")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Username and password are required");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        String username = (String) request.get("username");
        String password = (String) request.get("password");

        // Simulate invalid credentials
        if ("invalid".equals(username) || "invalid".equals(password)) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Invalid username or password");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Successful login
        Map<String, Object> data = new HashMap<>();
        data.put("userId", 1001);
        data.put("username", username);
        data.put("token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ");
        data.put("expiresIn", 3600);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Login successful");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> register(@RequestBody Map<String, Object> request) {
        // Validate required fields
        if (!request.containsKey("username") || !request.containsKey("password") || 
            !request.containsKey("phone") || !request.containsKey("email")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Missing required fields");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        String username = (String) request.get("username");
        String phone = (String) request.get("phone");
        String email = (String) request.get("email");

        // Validate phone number format
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid phone number format");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Validate email format
        if (!email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Invalid email format");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate username already exists
        if ("existing".equals(username)) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 409);
            response.put("message", "Username already exists");
            return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
        }

        // Successful registration
        Map<String, Object> data = new HashMap<>();
        data.put("userId", 1001);
        data.put("username", username);
        data.put("phone", phone);
        data.put("email", email);
        data.put("createdAt", "2023-10-15T10:00:00Z");

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 201);
        response.put("message", "Registration successful");
        response.put("data", data);

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping("/profile")
    public ResponseEntity<Map<String, Object>> getProfile(@RequestHeader(value = "Authorization", required = false) String authHeader) {
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate invalid token
        if (authHeader.equals("Bearer invalid")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Invalid token");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Successful profile retrieval
        Map<String, Object> data = new HashMap<>();
        data.put("userId", 1001);
        data.put("username", "johndoe");
        data.put("fullName", "John Doe");
        data.put("phone", "13800138000");
        data.put("email", "<EMAIL>");
        data.put("avatar", "https://example.com/avatars/johndoe.jpg");
        data.put("createdAt", "2023-01-15T10:00:00Z");
        data.put("lastLogin", "2023-10-15T08:30:00Z");

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PutMapping("/profile")
    public ResponseEntity<Map<String, Object>> updateProfile(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @RequestBody Map<String, Object> request) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate invalid token
        if (authHeader.equals("Bearer invalid")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Invalid token");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Validate phone number format if provided
        if (request.containsKey("phone")) {
            String phone = (String) request.get("phone");
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                Map<String, Object> response = new HashMap<>();
                response.put("status", false);
                response.put("code", 400);
                response.put("message", "Invalid phone number format");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
        }

        // Validate email format if provided
        if (request.containsKey("email")) {
            String email = (String) request.get("email");
            if (!email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
                Map<String, Object> response = new HashMap<>();
                response.put("status", false);
                response.put("code", 400);
                response.put("message", "Invalid email format");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
        }

        // Successful profile update
        Map<String, Object> data = new HashMap<>();
        data.put("userId", 1001);
        data.put("username", "johndoe");
        data.put("fullName", request.containsKey("fullName") ? request.get("fullName") : "John Doe");
        data.put("phone", request.containsKey("phone") ? request.get("phone") : "13800138000");
        data.put("email", request.containsKey("email") ? request.get("email") : "<EMAIL>");
        data.put("avatar", request.containsKey("avatar") ? request.get("avatar") : "https://example.com/avatars/johndoe.jpg");
        data.put("updatedAt", "2023-10-15T10:30:00Z");

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Profile updated successfully");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PostMapping("/change-password")
    public ResponseEntity<Map<String, Object>> changePassword(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @RequestBody Map<String, Object> request) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Validate required fields
        if (!request.containsKey("currentPassword") || !request.containsKey("newPassword")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Current password and new password are required");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        String currentPassword = (String) request.get("currentPassword");

        // Simulate incorrect current password
        if ("incorrect".equals(currentPassword)) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Current password is incorrect");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Successful password change
        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Password changed successfully");

        return ResponseEntity.ok(response);
    }

    @GetMapping("/orders")
    public ResponseEntity<Map<String, Object>> getOrders(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        // Validate authorization header
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate invalid parameters
        if (size > 100) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Page size must not be greater than 100");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate empty result for a specific page
        if (page > 10) {
            Map<String, Object> data = new HashMap<>();
            data.put("content", new ArrayList<>());
            data.put("totalElements", 0);
            data.put("totalPages", 0);
            data.put("size", size);
            data.put("number", page);

            Map<String, Object> response = new HashMap<>();
            response.put("status", true);
            response.put("code", 200);
            response.put("message", "Success");
            response.put("data", data);

            return ResponseEntity.ok(response);
        }

        // Normal response with orders
        List<Map<String, Object>> content = new ArrayList<>();
        
        Map<String, Object> order1 = new HashMap<>();
        order1.put("id", 5001);
        order1.put("orderNo", "ORDER123456789012");
        order1.put("type", "APPOINTMENT");
        order1.put("status", "CONFIRMED");
        order1.put("amount", 0.00);
        order1.put("createdAt", "2023-10-15T10:00:00Z");
        content.add(order1);
        
        Map<String, Object> order2 = new HashMap<>();
        order2.put("id", 5002);
        order2.put("orderNo", "ORDER987654321098");
        order2.put("type", "GOODS");
        order2.put("status", "PAID");
        order2.put("amount", 49.98);
        order2.put("createdAt", "2023-10-10T14:30:00Z");
        content.add(order2);

        Map<String, Object> data = new HashMap<>();
        data.put("content", content);
        data.put("totalElements", 2);
        data.put("totalPages", 1);
        data.put("size", size);
        data.put("number", page);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }
}
