// contract-tests/src/test/resources/contracts/admin/appointments/shouldListAppointments.groovy
package contracts.admin.appointments

import org.springframework.cloud.contract.spec.Contract

Contract.make {
    name("list appointments")
    description("should list all appointments with pagination")
    
    request {
        method GET()
        url("/manage/appointments") {
            queryParameters {
                parameter("page", "0")
                parameter("size", "10")
            }
        }
        headers {
            accept(applicationJson())
        }
    }
    
    response {
        status 200
        headers {
            contentType(applicationJson())
        }
        body([
            "status": true,
            "code": 200,
            "message": "Success",
            "data": [
                "content": [
                    [
                        "id": $(producer(anyNumber()), consumer(123)),
                        "orderNo": $(producer(regex('[A-Z0-9]{16}')), consumer("ORDER123456789012")),
                        "batchDate": $(producer(regex('\\d{4}-\\d{2}-\\d{2}')), consumer("2023-10-15")),
                        "batchStartTime": $(producer(regex('\\d{2}:\\d{2}')), consumer("10:00")),
                        "batchEndTime": $(producer(regex('\\d{2}:\\d{2}')), consumer("11:00")),
                        "status": $(producer(regex('CONFIRMED|PENDING|CANCELLED')), consumer("CONFIRMED")),
                        "contactName": $(producer(regex('.+')), consumer("John Doe")),
                        "contactPhone": $(producer(regex('\\d{11}')), consumer("13800138000"))
                    ]
                ],
                "totalElements": $(producer(anyNumber()), consumer(1)),
                "totalPages": $(producer(anyNumber()), consumer(1)),
                "size": 10,
                "number": 0
            ]
        ])
    }
}