package contracts.app.appointments

import org.springframework.cloud.contract.spec.Contract

Contract.make {
    name("get appointment details")
    description("should return appointment details when ID is provided")
    
    request {
        method GET()
        url("/api/appointments/123")
        headers {
            accept(applicationJson())
        }
    }
    
    response {
        status 200
        headers {
            contentType(applicationJson())
        }
        body([
            "status": true,
            "code": 200,
            "message": "Success",
            "data": [
                "id": 123,
                "orderNo": "ORDER123456789012",
                "batchDate": "2023-10-15",
                "batchStartTime": "10:00",
                "batchEndTime": "11:00",
                "status": "CONFIRMED"
            ]
        ])
        bodyMatchers {
            jsonPath('$.data.id', byEquality())
            jsonPath('$.data.orderNo', byRegex('[A-Z0-9]{16}'))
            jsonPath('$.data.batchDate', byRegex('\\d{4}-\\d{2}-\\d{2}'))
            jsonPath('$.data.batchStartTime', byRegex('\\d{2}:\\d{2}'))
            jsonPath('$.data.batchEndTime', byRegex('\\d{2}:\\d{2}'))
            jsonPath('$.data.status', byRegex('CONFIRMED|PENDING|CANCELLED'))
        }
    }
}
