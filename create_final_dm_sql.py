#!/usr/bin/env python3
"""
Create final DM-compatible SQL file with proper syntax
"""

import re

def create_final_dm_sql():
    """Create the final DM-compatible SQL file"""
    
    # Read the original file
    with open('dump-web_spup_test-202505222301.sql.bak', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Start with DM header
    dm_sql = """-- DM Database dump converted from MySQL 8.0.42
--
-- Host: localhost    Database: web_spup_test
-- ------------------------------------------------------
-- Converted for DM Database compatibility

-- Set basic session parameters for DM
SET IDENTITY_INSERT OFF;

"""
    
    # Extract table definitions
    table_pattern = r'-- Table structure for table (\w+).*?CREATE TABLE `(\w+)` \((.*?)\) ENGINE=.*?;'
    tables = re.findall(table_pattern, content, re.DOTALL)
    
    for table_name, table_name2, table_def in tables:
        # Clean table definition
        dm_sql += f"--\n-- Table structure for table {table_name}\n--\n\n"
        dm_sql += f"DROP TABLE IF EXISTS {table_name};\n\n"
        dm_sql += f"CREATE TABLE {table_name} (\n"
        
        # Process each column
        lines = table_def.strip().split('\n')
        processed_lines = []
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('PRIMARY KEY') or line.startswith('KEY'):
                continue
                
            # Remove backticks
            line = re.sub(r'`([^`]+)`', r'\1', line)
            
            # Convert data types
            line = re.sub(r'\bbigint\b', 'BIGINT', line, flags=re.IGNORECASE)
            line = re.sub(r'\bint\b(?!\s*\()', 'INT', line, flags=re.IGNORECASE)
            line = re.sub(r'\bsmallint\b', 'SMALLINT', line, flags=re.IGNORECASE)
            line = re.sub(r'\btinyint\b', 'TINYINT', line, flags=re.IGNORECASE)
            line = re.sub(r'\bdatetime\(6\)', 'TIMESTAMP(6)', line, flags=re.IGNORECASE)
            line = re.sub(r'\bdate\b', 'DATE', line, flags=re.IGNORECASE)
            line = re.sub(r'\btinyblob\b', 'BLOB', line, flags=re.IGNORECASE)
            
            # Handle AUTO_INCREMENT
            if 'AUTO_INCREMENT' in line:
                line = re.sub(r'(\w+)\s+BIGINT\s+NOT\s+NULL\s+AUTO_INCREMENT', r'\1 BIGINT IDENTITY(1,1) NOT NULL', line, flags=re.IGNORECASE)
            else:
                line = re.sub(r'\s+AUTO_INCREMENT', '', line, flags=re.IGNORECASE)
            
            # Remove collation
            line = re.sub(r'\s+COLLATE\s+\w+', '', line, flags=re.IGNORECASE)
            
            # Convert comments
            line = re.sub(r'\s+COMMENT\s+\'([^\']+)\'', r' -- \1', line)
            
            # Ensure proper comma placement
            if line and not line.endswith(',') and not line.startswith('PRIMARY'):
                line += ','
                
            processed_lines.append(f"  {line}")
        
        # Add processed lines
        for i, line in enumerate(processed_lines):
            if i == len(processed_lines) - 1:
                # Remove comma from last line
                line = line.rstrip(',')
            dm_sql += line + '\n'
        
        # Add primary key
        primary_key_match = re.search(r'PRIMARY KEY \(`(\w+)`\)', table_def)
        if primary_key_match:
            pk_column = primary_key_match.group(1)
            dm_sql += f"  PRIMARY KEY ({pk_column})\n"
        
        dm_sql += ");\n\n"
        dm_sql += f"--\n-- Dumping data for table {table_name}\n--\n\n"
    
    # Write final file
    with open('dump-web_spup_test-dm-compatible-final.sql', 'w', encoding='utf-8') as f:
        f.write(dm_sql)
    
    print("Final DM-compatible SQL file created: dump-web_spup_test-dm-compatible-final.sql")

if __name__ == "__main__":
    create_final_dm_sql()
