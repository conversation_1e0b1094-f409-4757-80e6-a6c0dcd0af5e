-- 达梦数据库 art_center_info 简化测试

-- 方案1: 重命名section列避免保留字冲突
CREATE TABLE art_center_info (
  id BIGINT PRIMARY KEY,
  address VARCHAR(255),
  introduction VARCHAR(2000),
  metro VARCHAR(255),
  open_time VARCHAR(255),
  pic_info VARCHAR(255),
  section_info VARCHAR(255),  -- 重命名避免保留字
  traffic VARCHAR(255)
);

-- 测试插入
INSERT INTO art_center_info (id, address, section_info) 
VALUES (1, '测试地址', '测试区域');

-- 测试查询
SELECT id, address, section_info FROM art_center_info;

-- 清理
DROP TABLE art_center_info;

-- 方案2: 使用双引号的section列
CREATE TABLE art_center_info2 (
  id BIGINT PRIMARY KEY,
  address VARCHAR(255),
  "section" VARCHAR(255)
);

-- 测试插入
INSERT INTO art_center_info2 (id, address, "section") 
VALUES (1, '测试地址', '测试区域');

-- 测试查询
SELECT id, address, "section" FROM art_center_info2;

-- 清理
DROP TABLE art_center_info2;

SELECT '艺术中心表测试完成' AS result;
