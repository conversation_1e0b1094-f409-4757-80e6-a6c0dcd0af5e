-- DM Database Basic Compatibility Test
-- Test the most basic table creation syntax

-- Test 1: Simple table without IDENTITY
CREATE TABLE test_simple (
  id INT NOT NULL,
  name VARCHAR(50)
);

-- Test if basic table creation works
INSERT INTO test_simple (id, name) VALUES (1, 'test');
SELECT * FROM test_simple;
DROP TABLE test_simple;

-- Test 2: Table with quoted names (if case sensitivity is an issue)
CREATE TABLE "test_quoted" (
  "id" INT NOT NULL,
  "name" VARCHAR(50)
);

INSERT INTO "test_quoted" ("id", "name") VALUES (1, 'test');
SELECT * FROM "test_quoted";
DROP TABLE "test_quoted";

-- Test 3: Check DM database version and capabilities
SELECT VERSION();
