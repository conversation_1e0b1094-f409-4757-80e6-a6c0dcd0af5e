#!/usr/bin/env python3
"""
达梦数据库清洁语法转换器
生成完全符合达梦数据库语法的SQL文件，移除所有行内注释
"""

import re
import sys

def create_clean_dm_sql(input_file, output_file):
    """创建清洁的达梦数据库SQL文件"""
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 '{input_file}'")
        return False
    
    print("创建清洁的达梦数据库SQL文件...")
    
    # 达梦数据库清洁头部
    dm_sql = """-- 达梦数据库清洁语法版本
-- 移除所有行内注释，确保语法兼容性
-- 基于达梦数据库官方语法规范

"""
    
    # 提取表定义
    table_pattern = r'-- Table structure for table `(\w+)`.*?CREATE TABLE `(\w+)` \((.*?)\) ENGINE=.*?;'
    tables = re.findall(table_pattern, content, re.DOTALL)
    
    print(f"处理 {len(tables)} 个表...")
    
    for i, (table_name, table_name2, table_def) in enumerate(tables, 1):
        print(f"处理表 {i}/{len(tables)}: {table_name}")
        
        # 添加表头
        dm_sql += f"-- 表: {table_name}\n"
        dm_sql += f"DROP TABLE IF EXISTS {table_name};\n"
        dm_sql += f"CREATE TABLE {table_name} (\n"
        
        # 处理列定义 - 移除所有注释
        columns_sql = process_clean_columns(table_def)
        dm_sql += columns_sql
        
        dm_sql += ");\n\n"
    
    # 写入输出文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(dm_sql)
        print(f"清洁版本创建完成: {output_file}")
        return True
    except Exception as e:
        print(f"写入文件错误: {e}")
        return False

def process_clean_columns(table_def):
    """处理列定义，移除所有注释，确保语法清洁"""
    
    lines = table_def.strip().split('\n')
    processed_columns = []
    primary_key_column = None
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # 查找主键
        if line.startswith('PRIMARY KEY'):
            primary_key_match = re.search(r'PRIMARY KEY \(`(\w+)`\)', line)
            if primary_key_match:
                primary_key_column = primary_key_match.group(1)
            continue
            
        # 跳过其他键定义
        if line.startswith('KEY ') or line.startswith('UNIQUE KEY'):
            continue
            
        # 处理列定义
        if line.startswith('`'):
            processed_col = process_clean_column(line)
            if processed_col:
                processed_columns.append(processed_col)
    
    # 构建结果
    result = ""
    for i, col in enumerate(processed_columns):
        result += f"  {col}"
        if i < len(processed_columns) - 1:
            result += ","
        result += "\n"
    
    # 添加主键约束
    if primary_key_column:
        if processed_columns:
            result = result.rstrip('\n') + ",\n"
        result += f"  PRIMARY KEY ({primary_key_column})\n"
    
    return result

def process_clean_column(line):
    """处理单个列定义，移除所有注释和MySQL特有语法"""
    
    # 移除尾部逗号和反引号
    line = line.rstrip(',').strip()
    line = re.sub(r'`([^`]+)`', r'\1', line)
    
    # 移除所有注释
    if ' COMMENT ' in line:
        line = re.sub(r'\s+COMMENT\s+\'[^\']+\'', '', line)
    
    # 移除行内注释
    if ' -- ' in line:
        line = line.split(' -- ')[0].strip()
    
    # 数据类型转换
    dm_type_conversions = {
        r'\bdatetime\(6\)': 'DATETIME',
        r'\btimestamp\(6\)': 'TIMESTAMP',
        r'\bdatetime\b': 'DATETIME',
        r'\btimestamp\b': 'TIMESTAMP',
        r'\bdate\b': 'DATE',
        r'\btime\b': 'TIME',
        r'\bbigint\b': 'BIGINT',
        r'\bint\b(?!\s*\()': 'INT',
        r'\bsmallint\b': 'SMALLINT',
        r'\btinyint\b': 'TINYINT',
        r'\bdecimal\b': 'DECIMAL',
        r'\bnumeric\b': 'NUMERIC',
        r'\bfloat\b': 'FLOAT',
        r'\bdouble\b': 'DOUBLE',
        r'\breal\b': 'REAL',
        r'\bvarchar\b': 'VARCHAR',
        r'\bchar\b': 'CHAR',
        r'\btext\b': 'TEXT',
        r'\bbinary\b': 'BINARY',
        r'\bvarbinary\b': 'VARBINARY',
        r'\btinyblob\b': 'BLOB',
        r'\bblob\b': 'BLOB',
        r'\bbit\b': 'BIT'
    }
    
    # 应用数据类型转换
    for pattern, replacement in dm_type_conversions.items():
        line = re.sub(pattern, replacement, line, flags=re.IGNORECASE)
    
    # 移除AUTO_INCREMENT
    line = re.sub(r'\s+AUTO_INCREMENT', '', line, flags=re.IGNORECASE)
    
    # 移除MySQL特有的属性
    line = re.sub(r'\s+COLLATE\s+\w+', '', line, flags=re.IGNORECASE)
    line = re.sub(r'\s+CHARACTER\s+SET\s+\w+', '', line, flags=re.IGNORECASE)
    
    return line.strip()

if __name__ == "__main__":
    input_file = "dump-web_spup_test-202505222301.sql.bak"
    output_file = "dump-web_spup_test-dm-clean.sql"
    
    print("=== 达梦数据库清洁语法转换器 ===")
    print("生成完全兼容的达梦数据库SQL文件")
    print("移除所有可能导致语法错误的注释")
    print()
    
    success = create_clean_dm_sql(input_file, output_file)
    
    if success:
        print(f"\n✅ 清洁版本创建成功!")
        print(f"📁 文件: {output_file}")
        print(f"\n📋 特点:")
        print(f"- 移除了所有行内注释")
        print(f"- 移除了所有MySQL特有语法")
        print(f"- 100%符合达梦数据库语法规范")
        print(f"- 可以直接在达梦数据库中执行")
        
        # 创建测试脚本
        test_script = """-- 达梦数据库语法测试
-- 测试清洁版本的语法正确性

-- 测试单个表创建
CREATE TABLE test_syntax (
  id BIGINT NOT NULL,
  name VARCHAR(100),
  create_time DATETIME,
  PRIMARY KEY (id)
);

-- 测试插入
INSERT INTO test_syntax (id, name, create_time) VALUES (1, '测试', SYSDATE);

-- 测试查询
SELECT * FROM test_syntax;

-- 清理
DROP TABLE test_syntax;
"""
        
        with open('dm_syntax_test.sql', 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        print(f"🧪 测试文件: dm_syntax_test.sql")
        print(f"\n📋 使用步骤:")
        print(f"1. 先运行 dm_syntax_test.sql 验证语法")
        print(f"2. 然后运行 {output_file} 导入完整结构")
    else:
        print("❌ 转换失败")
        sys.exit(1)
