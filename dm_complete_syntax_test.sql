-- 达梦数据库完整语法测试脚本
-- 请在达梦数据库客户端中执行此脚本
-- 连接信息: localhost:5236, 用户: SYSDBA, 密码: Dameng123

-- ========================================
-- 第一部分: 环境检查
-- ========================================

PRINT '=== 达梦数据库环境检查 ===';

-- 检查数据库版本
SELECT '数据库版本:' AS info, VERSION() AS value;

-- 检查当前用户
SELECT '当前用户:' AS info, USER AS value;

-- 检查当前模式
SELECT '当前模式:' AS info, SYS_CONTEXT('USERENV', 'CURRENT_SCHEMA') AS value;

-- 检查当前时间
SELECT '当前时间:' AS info, SYSDATE AS value;

-- ========================================
-- 第二部分: 基础语法测试
-- ========================================

PRINT '=== 基础语法测试 ===';

-- 测试1: 最简单的表创建
DROP TABLE IF EXISTS test_basic;
CREATE TABLE test_basic (
    id INT PRIMARY KEY
);
INSERT INTO test_basic VALUES (1);
SELECT '基础表测试:' AS test, COUNT(*) AS result FROM test_basic;
DROP TABLE test_basic;

-- 测试2: BIGINT和VARCHAR类型
DROP TABLE IF EXISTS test_types;
CREATE TABLE test_types (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100),
    create_time DATETIME
);
INSERT INTO test_types VALUES (1, '类型测试', SYSDATE);
SELECT '数据类型测试:' AS test, COUNT(*) AS result FROM test_types;
DROP TABLE test_types;

-- ========================================
-- 第三部分: 保留字测试
-- ========================================

PRINT '=== 保留字测试 ===';

-- 测试3: section保留字（不带引号）
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE IF EXISTS test_section_plain';
    EXECUTE IMMEDIATE 'CREATE TABLE test_section_plain (id BIGINT PRIMARY KEY, section VARCHAR(100))';
    EXECUTE IMMEDIATE 'INSERT INTO test_section_plain VALUES (1, ''section测试'')';
    SELECT 'section不带引号:' AS test, '成功' AS result FROM DUAL;
    EXECUTE IMMEDIATE 'DROP TABLE test_section_plain';
EXCEPTION
    WHEN OTHERS THEN
        SELECT 'section不带引号:' AS test, '失败-是保留字' AS result FROM DUAL;
END;
/

-- 测试4: section保留字（带引号）
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE IF EXISTS test_section_quoted';
    EXECUTE IMMEDIATE 'CREATE TABLE test_section_quoted (id BIGINT PRIMARY KEY, "section" VARCHAR(100))';
    EXECUTE IMMEDIATE 'INSERT INTO test_section_quoted VALUES (1, ''section引号测试'')';
    SELECT 'section带引号:' AS test, '成功' AS result FROM DUAL;
    EXECUTE IMMEDIATE 'DROP TABLE test_section_quoted';
EXCEPTION
    WHEN OTHERS THEN
        SELECT 'section带引号:' AS test, '失败' AS result FROM DUAL;
END;
/

-- ========================================
-- 第四部分: 实际业务表测试
-- ========================================

PRINT '=== 业务表测试 ===';

-- 测试5: art_center_info表（重命名section方案）
DROP TABLE IF EXISTS art_center_info_v1;
CREATE TABLE art_center_info_v1 (
    id BIGINT PRIMARY KEY,
    address VARCHAR(255),
    introduction VARCHAR(2000),
    metro VARCHAR(255),
    open_time VARCHAR(255),
    pic_info VARCHAR(255),
    section_info VARCHAR(255),  -- 重命名避免保留字
    traffic VARCHAR(255)
);

INSERT INTO art_center_info_v1 (id, address, section_info) 
VALUES (1, '测试地址', '测试区域');

SELECT 'art_center_info重命名方案:' AS test, COUNT(*) AS result FROM art_center_info_v1;

-- 测试6: art_center_info表（双引号section方案）
DROP TABLE IF EXISTS art_center_info_v2;
CREATE TABLE art_center_info_v2 (
    id BIGINT PRIMARY KEY,
    address VARCHAR(255),
    introduction VARCHAR(2000),
    metro VARCHAR(255),
    open_time VARCHAR(255),
    pic_info VARCHAR(255),
    "section" VARCHAR(255),  -- 使用双引号
    traffic VARCHAR(255)
);

INSERT INTO art_center_info_v2 (id, address, "section") 
VALUES (1, '测试地址', '测试区域');

SELECT 'art_center_info双引号方案:' AS test, COUNT(*) AS result FROM art_center_info_v2;

-- 测试查询双引号列
SELECT id, address, "section" FROM art_center_info_v2 WHERE id = 1;

-- ========================================
-- 第五部分: 其他核心表测试
-- ========================================

PRINT '=== 核心表测试 ===';

-- 测试7: app_activity表
DROP TABLE IF EXISTS app_activity_test;
CREATE TABLE app_activity_test (
    id BIGINT PRIMARY KEY,
    address VARCHAR(255),
    content VARCHAR(255),
    title VARCHAR(255),
    activity_status INT,
    activity_type TINYINT,
    create_time DATETIME
);

INSERT INTO app_activity_test (id, title, activity_status, activity_type, create_time)
VALUES (1, '测试活动', 1, 1, SYSDATE);

SELECT 'app_activity测试:' AS test, COUNT(*) AS result FROM app_activity_test;

-- 测试8: app_customer表
DROP TABLE IF EXISTS app_customer_test;
CREATE TABLE app_customer_test (
    id BIGINT PRIMARY KEY,
    real_name VARCHAR(255),
    phone VARCHAR(255),
    unionid VARCHAR(255),
    create_time DATETIME
);

INSERT INTO app_customer_test (id, real_name, phone, create_time)
VALUES (1, '测试用户', '13800138000', SYSDATE);

SELECT 'app_customer测试:' AS test, COUNT(*) AS result FROM app_customer_test;

-- ========================================
-- 第六部分: 清理和总结
-- ========================================

PRINT '=== 清理测试表 ===';

DROP TABLE IF EXISTS art_center_info_v1;
DROP TABLE IF EXISTS art_center_info_v2;
DROP TABLE IF EXISTS app_activity_test;
DROP TABLE IF EXISTS app_customer_test;

-- 总结
SELECT '=== 测试完成 ===' AS summary, SYSDATE AS completion_time FROM DUAL;

PRINT '请检查以上测试结果，确认哪些语法可以正常工作。';
