#!/usr/bin/env python3
"""
达梦数据库连接测试脚本
测试连接并验证基础SQL语法
"""

import sys

def test_dm_connection():
    """测试达梦数据库连接"""
    
    try:
        # 尝试导入达梦数据库驱动
        try:
            import dmPython
            print("✅ 达梦数据库Python驱动已安装")
        except ImportError:
            print("❌ 达梦数据库Python驱动未安装")
            print("请安装: pip install dmPython")
            return False
        
        # 连接参数
        connection_params = {
            'server': 'localhost',
            'port': 5236,
            'user': 'SYSDBA',
            'password': 'Dameng123'
        }
        
        print(f"🔗 尝试连接达梦数据库...")
        print(f"   服务器: {connection_params['server']}:{connection_params['port']}")
        print(f"   用户: {connection_params['user']}")
        
        # 建立连接
        conn = dmPython.connect(
            server=connection_params['server'],
            port=connection_params['port'],
            user=connection_params['user'],
            password=connection_params['password']
        )
        
        print("✅ 数据库连接成功！")
        
        # 创建游标
        cursor = conn.cursor()
        
        # 测试基础查询
        print("\n📋 执行基础测试...")
        
        # 测试1: 版本查询
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"✅ 数据库版本: {version}")
        
        # 测试2: 当前用户
        cursor.execute("SELECT USER")
        user = cursor.fetchone()[0]
        print(f"✅ 当前用户: {user}")
        
        # 测试3: 当前时间
        cursor.execute("SELECT SYSDATE")
        current_time = cursor.fetchone()[0]
        print(f"✅ 当前时间: {current_time}")
        
        # 测试4: 简单表创建
        print("\n🧪 测试表创建...")
        
        try:
            # 删除可能存在的测试表
            cursor.execute("DROP TABLE IF EXISTS dm_test_simple")
            print("✅ 清理旧测试表")
        except:
            pass
        
        # 创建测试表
        cursor.execute("""
            CREATE TABLE dm_test_simple (
                id BIGINT PRIMARY KEY,
                name VARCHAR(100),
                create_time DATETIME
            )
        """)
        print("✅ 测试表创建成功")
        
        # 插入测试数据
        cursor.execute("""
            INSERT INTO dm_test_simple (id, name, create_time) 
            VALUES (1, '测试数据', SYSDATE)
        """)
        print("✅ 测试数据插入成功")
        
        # 查询测试数据
        cursor.execute("SELECT * FROM dm_test_simple")
        result = cursor.fetchone()
        print(f"✅ 查询结果: {result}")
        
        # 测试保留字section
        print("\n🔍 测试保留字section...")
        
        try:
            cursor.execute("DROP TABLE IF EXISTS dm_test_section")
        except:
            pass
        
        # 测试不带引号的section
        try:
            cursor.execute("""
                CREATE TABLE dm_test_section (
                    id BIGINT PRIMARY KEY,
                    section VARCHAR(100)
                )
            """)
            print("✅ section不是保留字，可以直接使用")
            section_is_reserved = False
        except Exception as e:
            print(f"❌ section是保留字: {e}")
            section_is_reserved = True
        
        # 如果section是保留字，测试带引号的版本
        if section_is_reserved:
            try:
                cursor.execute("""
                    CREATE TABLE dm_test_section (
                        id BIGINT PRIMARY KEY,
                        "section" VARCHAR(100)
                    )
                """)
                print("✅ 使用双引号的section可以正常使用")
            except Exception as e:
                print(f"❌ 即使使用双引号也无法使用section: {e}")
        
        # 清理测试表
        cursor.execute("DROP TABLE dm_test_simple")
        if section_is_reserved:
            try:
                cursor.execute("DROP TABLE dm_test_section")
            except:
                pass
        
        print("✅ 测试表清理完成")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        print("\n🎉 所有测试完成！数据库连接和基础功能正常。")
        return True
        
    except Exception as e:
        print(f"❌ 连接或测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=== 达梦数据库连接测试 ===")
    print("测试连接: localhost:5236")
    print("用户: SYSDBA")
    print()
    
    success = test_dm_connection()
    
    if success:
        print("\n✅ 测试成功！可以继续进行SQL脚本测试。")
    else:
        print("\n❌ 测试失败！请检查连接参数或数据库状态。")
        sys.exit(1)
