#!/usr/bin/env python3
"""
DM Database Conservative Converter
Uses the most basic SQL syntax for maximum DM compatibility
"""

import re
import sys

def convert_to_dm_conservative(input_file, output_file):
    """Convert using the most conservative DM-compatible syntax"""
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
        return False
    
    print("Creating conservative DM-compatible database schema...")
    
    # Very basic DM header
    dm_sql = """-- DM Database Schema - Conservative Syntax
-- Maximum compatibility approach

"""
    
    # Extract table definitions
    table_pattern = r'-- Table structure for table `(\w+)`.*?CREATE TABLE `(\w+)` \((.*?)\) ENGINE=.*?;'
    tables = re.findall(table_pattern, content, re.DOTALL)
    
    print(f"Converting {len(tables)} tables with conservative syntax...")
    
    for i, (table_name, table_name2, table_def) in enumerate(tables, 1):
        print(f"Processing table {i}/{len(tables)}: {table_name}")
        
        # Use very basic table creation syntax
        dm_sql += f"-- Table: {table_name}\n"
        dm_sql += f"DROP TABLE IF EXISTS {table_name};\n"
        dm_sql += f"CREATE TABLE {table_name} (\n"
        
        # Process columns with conservative approach
        columns = process_conservative_columns(table_def)
        dm_sql += columns
        
        dm_sql += ");\n\n"
    
    # Write output
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(dm_sql)
        print(f"Conservative conversion completed: {output_file}")
        return True
    except Exception as e:
        print(f"Error writing output: {e}")
        return False

def process_conservative_columns(table_def):
    """Process columns with the most conservative approach"""
    
    lines = table_def.strip().split('\n')
    processed_columns = []
    primary_key_column = None
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # Find PRIMARY KEY
        if line.startswith('PRIMARY KEY'):
            primary_key_match = re.search(r'PRIMARY KEY \(`(\w+)`\)', line)
            if primary_key_match:
                primary_key_column = primary_key_match.group(1)
            continue
            
        # Skip other keys
        if line.startswith('KEY ') or line.startswith('UNIQUE KEY'):
            continue
            
        # Process column definitions
        if line.startswith('`'):
            processed_col = process_conservative_column(line, primary_key_column)
            if processed_col:
                processed_columns.append(processed_col)
    
    # Build result with conservative syntax
    result = ""
    for i, col in enumerate(processed_columns):
        result += f"  {col}"
        if i < len(processed_columns) - 1:
            result += ","
        result += "\n"
    
    return result

def process_conservative_column(line, primary_key_column):
    """Process individual column with most conservative syntax"""
    
    # Remove trailing comma and backticks
    line = line.rstrip(',').strip()
    line = re.sub(r'`([^`]+)`', r'\1', line)
    
    # Extract column name
    col_match = re.match(r'(\w+)', line)
    if not col_match:
        return None
    
    col_name = col_match.group(1)
    
    # Conservative data type conversions - use most basic types
    conservative_conversions = {
        r'\bdatetime\(6\)': 'DATETIME',
        r'\btimestamp\(6\)': 'DATETIME',
        r'\bdatetime\b': 'DATETIME',
        r'\btimestamp\b': 'DATETIME',
        r'\btinyblob\b': 'BLOB',
        r'\bbigint\b': 'BIGINT',
        r'\bint\b(?!\s*\()': 'INT',
        r'\bsmallint\b': 'INT',  # Use INT instead of SMALLINT for safety
        r'\btinyint\b': 'INT',   # Use INT instead of TINYINT for safety
        r'\bdate\b': 'DATE',
        r'\bvarchar\b': 'VARCHAR',
        r'\bchar\b': 'CHAR',
        r'\btext\b': 'VARCHAR(4000)',  # Convert TEXT to VARCHAR for compatibility
        r'\blongtext\b': 'VARCHAR(4000)'
    }
    
    for pattern, replacement in conservative_conversions.items():
        line = re.sub(pattern, replacement, line, flags=re.IGNORECASE)
    
    # Handle AUTO_INCREMENT - remove it and handle manually later
    if 'AUTO_INCREMENT' in line.upper():
        # For primary key columns, just mark as NOT NULL
        if col_name == primary_key_column:
            line = re.sub(
                r'(\w+)\s+\w+\s+NOT\s+NULL\s+AUTO_INCREMENT',
                r'\1 BIGINT NOT NULL',
                line,
                flags=re.IGNORECASE
            )
        line = re.sub(r'\s+AUTO_INCREMENT', '', line, flags=re.IGNORECASE)
    
    # Remove all MySQL-specific attributes
    line = re.sub(r'\s+COLLATE\s+\w+', '', line, flags=re.IGNORECASE)
    line = re.sub(r'\s+CHARACTER\s+SET\s+\w+', '', line, flags=re.IGNORECASE)
    
    # Remove comments for maximum compatibility
    line = re.sub(r'\s+COMMENT\s+\'[^\']+\'', '', line)
    
    # Add PRIMARY KEY constraint if this is the primary key column
    if col_name == primary_key_column:
        line += ' PRIMARY KEY'
    
    return line

def create_minimal_test():
    """Create the most minimal test possible"""
    
    test_script = """-- Minimal DM Database Test
-- Test absolute basic functionality

CREATE TABLE simple_test (
  id BIGINT NOT NULL PRIMARY KEY,
  name VARCHAR(100)
);

INSERT INTO simple_test (id, name) VALUES (1, 'test');
SELECT * FROM simple_test;
DROP TABLE simple_test;
"""
    
    with open('dm_minimal_test.sql', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("Created minimal test: dm_minimal_test.sql")

if __name__ == "__main__":
    input_file = "dump-web_spup_test-202505222301.sql.bak"
    output_file = "dump-web_spup_test-dm-conservative.sql"
    
    print("=== DM Database Conservative Converter ===")
    print("Using most basic SQL syntax for maximum compatibility...")
    print()
    
    success = convert_to_dm_conservative(input_file, output_file)
    
    if success:
        create_minimal_test()
        print(f"\n✅ Conservative conversion completed!")
        print(f"📁 Schema file: {output_file}")
        print(f"🧪 Minimal test: dm_minimal_test.sql")
        print(f"📋 Basic test: dm_basic_test.sql")
        print(f"\n📋 Testing order:")
        print(f"1. Run dm_minimal_test.sql first (most basic)")
        print(f"2. Run dm_basic_test.sql (slightly more complex)")
        print(f"3. If both pass, try {output_file}")
    else:
        print("❌ Conversion failed")
        sys.exit(1)
