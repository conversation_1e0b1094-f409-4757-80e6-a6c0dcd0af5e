# MySQL 8 to DM Database Conversion Plan

## Key Differences to Address:

### 1. MySQL-specific Comments and Settings
- Remove MySQL version comments (/*!40101 ... */)
- Remove MySQL-specific SET statements
- Remove LOCK TABLES statements
- Remove ENGINE=InnoDB specifications

### 2. Data Types Conversion
- `bigint NOT NULL AUTO_INCREMENT` → `BIGINT IDENTITY(1,1) NOT NULL`
- `datetime(6)` → `TIMESTAMP(6)` or `DATETIME`
- `tinyint` → `TINYINT` or `SMALLINT`
- `varchar(255) COLLATE utf8mb4_general_ci` → `VARCHAR(255)`
- `tinyblob` → `BLOB`

### 3. Character Set and Collation
- Remove COLLATE utf8mb4_general_ci
- Remove DEFAULT CHARSET=utf8mb4

### 4. Primary Key and Auto Increment
- Convert AUTO_INCREMENT to IDENTITY
- Adjust PRIMARY KEY syntax if needed

### 5. Comments
- Convert MySQL COMMENT syntax to DM compatible format

### 6. Table Creation
- Remove backticks around table/column names
- Adjust CREATE TABLE syntax

## Conversion Steps:
1. Extract header and create DM-compatible header
2. Process each CREATE TABLE statement
3. Convert data types
4. Remove MySQL-specific syntax
5. Convert INSERT statements
6. Create final DM-compatible SQL file
