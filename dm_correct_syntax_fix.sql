-- 达梦数据库正确语法修复版本
-- 只修复真正的保留字冲突，不破坏基本SQL语法

-- 测试基本语法
DROP TABLE IF EXISTS dm_syntax_test;
CREATE TABLE dm_syntax_test (
  id BIGINT NOT NULL,
  test_name VARCHAR(100),
  PRIMARY KEY (id)
);

-- 核心表1: art_center_info (修复section保留字)
DROP TABLE IF EXISTS art_center_info;
CREATE TABLE art_center_info (
  id BIGINT NOT NULL,
  address VARCHAR(255) DEFAULT NULL,
  introduction VARCHAR(2000) DEFAULT NULL,
  metro VARCHAR(255) DEFAULT NULL,
  open_time VARCHAR(255) DEFAULT NULL,
  pic_info VARCHAR(255) DEFAULT NULL,
  "section" VARCHAR(255) DEFAULT NULL,
  traffic VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 核心表2: app_activity (修复status, type保留字)
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT NOT NULL,
  address VARCHAR(255) DEFAULT NULL,
  content VARCHAR(255) DEFAULT NULL,
  conver_picture VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  end_time DATETIME DEFAULT NULL,
  sort_order INT DEFAULT NULL,
  start_time DATETIME DEFAULT NULL,
  activity_status INT DEFAULT NULL,
  title VARCHAR(255) DEFAULT NULL,
  activity_type TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 核心表3: app_customer
DROP TABLE IF EXISTS app_customer;
CREATE TABLE app_customer (
  id BIGINT NOT NULL,
  breaked_num INT DEFAULT NULL,
  breaked_total_num INT DEFAULT NULL,
  card_category TINYINT DEFAULT NULL,
  card_no VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  customer_id VARCHAR(255) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  job VARCHAR(255) DEFAULT NULL,
  mini_openid VARCHAR(255) DEFAULT NULL,
  phone VARCHAR(255) DEFAULT NULL,
  real_name VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  user_avatar_src VARCHAR(255) DEFAULT NULL,
  user_birthdate VARCHAR(255) DEFAULT NULL,
  user_gender TINYINT DEFAULT NULL,
  user_name VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 核心表4: app_config
DROP TABLE IF EXISTS app_config;
CREATE TABLE app_config (
  id BIGINT NOT NULL,
  group_no VARCHAR(255) DEFAULT NULL,
  rule_name VARCHAR(255) DEFAULT NULL,
  rule_value VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 核心表5: app_appointment_order
DROP TABLE IF EXISTS app_appointment_order;
CREATE TABLE app_appointment_order (
  id BIGINT NOT NULL,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  order_category TINYINT DEFAULT NULL,
  order_no VARCHAR(255) DEFAULT NULL,
  order_remark VARCHAR(255) DEFAULT NULL,
  order_status SMALLINT DEFAULT NULL,
  owner_name VARCHAR(255) DEFAULT NULL,
  owner_phone VARCHAR(255) DEFAULT NULL,
  owner_unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 测试插入数据
INSERT INTO dm_syntax_test (id, test_name) VALUES (1, '语法测试');
INSERT INTO art_center_info (id, address, "section") VALUES (1, '测试地址', '测试区域');
INSERT INTO app_activity (id, title, activity_status, activity_type) VALUES (1, '测试活动', 1, 1);
INSERT INTO app_customer (id, real_name, phone) VALUES (1, '测试用户', '13800138000');
INSERT INTO app_config (id, group_no, rule_name, rule_value) VALUES (1, 'test', 'test_rule', 'test_value');

-- 测试查询
SELECT * FROM dm_syntax_test;
SELECT id, address, "section" FROM art_center_info;
SELECT id, title, activity_status, activity_type FROM app_activity;
SELECT id, real_name, phone FROM app_customer;
SELECT * FROM app_config;

-- 验证成功
SELECT '达梦数据库语法修复成功！' AS result;
