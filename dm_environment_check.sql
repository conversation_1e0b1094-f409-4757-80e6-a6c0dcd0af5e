-- 达梦数据库环境检查脚本

-- 检查数据库版本
SELECT VERSION() AS dm_version;

-- 检查当前用户
SELECT USER AS current_user;

-- 检查当前数据库/模式
SELECT SYS_CONTEXT('USERENV', 'CURRENT_SCHEMA') AS current_schema;

-- 检查用户权限
SELECT * FROM USER_SYS_PRIVS WHERE PRIVILEGE LIKE '%CREATE%';

-- 检查表空间
SELECT TABLESPACE_NAME, STATUS FROM USER_TABLESPACES;

-- 检查现有表
SELECT TABLE_NAME FROM USER_TABLES;

-- 检查数据库参数
SELECT NAME, VALUE FROM V$PARAMETER WHERE NAME LIKE '%case%' OR NAME LIKE '%name%';

-- 测试基本SQL功能
SELECT 1 AS test_number;
SELECT SYSDATE AS current_time;
SELECT 'Hello DM' AS test_string;
