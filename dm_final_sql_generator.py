#!/usr/bin/env python3
"""
达梦数据库最终SQL生成器
根据测试结果生成正确的SQL脚本
"""

import re
import sys

def generate_dm_sql_based_on_test_results():
    """根据测试结果生成最终的SQL脚本"""
    
    print("=== 达梦数据库最终SQL生成器 ===")
    print("请先在达梦数据库中执行 dm_simple_syntax_test.sql")
    print("然后根据测试结果选择合适的方案")
    print()
    
    # 询问测试结果
    print("📋 请回答以下问题（基于您的测试结果）:")
    print()
    
    # 问题1: section是否是保留字
    while True:
        section_reserved = input("1. section是否是保留字？(y/n): ").lower().strip()
        if section_reserved in ['y', 'yes', 'n', 'no']:
            section_is_reserved = section_reserved in ['y', 'yes']
            break
        print("请输入 y 或 n")
    
    # 问题2: 双引号是否有效
    if section_is_reserved:
        while True:
            quotes_work = input("2. 双引号包围的\"section\"是否有效？(y/n): ").lower().strip()
            if quotes_work in ['y', 'yes', 'n', 'no']:
                quotes_effective = quotes_work in ['y', 'yes']
                break
            print("请输入 y 或 n")
    else:
        quotes_effective = False
    
    # 问题3: 基础表创建是否成功
    while True:
        basic_works = input("3. 基础表创建是否成功？(y/n): ").lower().strip()
        if basic_works in ['y', 'yes', 'n', 'no']:
            basic_table_works = basic_works in ['y', 'yes']
            break
        print("请输入 y 或 n")
    
    if not basic_table_works:
        print("❌ 基础表创建失败，请检查数据库连接和权限")
        return False
    
    # 根据测试结果生成SQL
    print(f"\n📊 测试结果分析:")
    print(f"- section是保留字: {'是' if section_is_reserved else '否'}")
    if section_is_reserved:
        print(f"- 双引号有效: {'是' if quotes_effective else '否'}")
    print(f"- 基础表功能: {'正常' if basic_table_works else '异常'}")
    
    # 选择方案
    if not section_is_reserved:
        solution = "direct"
        print(f"\n✅ 推荐方案: 直接使用section列名")
    elif section_is_reserved and quotes_effective:
        solution = "quotes"
        print(f"\n✅ 推荐方案: 使用双引号包围section")
    else:
        solution = "rename"
        print(f"\n✅ 推荐方案: 重命名section为section_info")
    
    # 生成最终SQL
    generate_final_sql(solution)
    
    return True

def generate_final_sql(solution):
    """生成最终的SQL脚本"""
    
    if solution == "direct":
        # 方案1: 直接使用section
        sql_content = generate_direct_solution()
        filename = "dump-web_spup_test-dm-direct.sql"
        
    elif solution == "quotes":
        # 方案2: 使用双引号
        sql_content = generate_quotes_solution()
        filename = "dump-web_spup_test-dm-quotes.sql"
        
    else:  # rename
        # 方案3: 重命名section
        sql_content = generate_rename_solution()
        filename = "dump-web_spup_test-dm-rename.sql"
    
    # 写入文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(sql_content)
    
    print(f"\n✅ 最终SQL脚本已生成: {filename}")
    print(f"📋 使用方法:")
    print(f"   在达梦数据库中执行: SOURCE {filename};")

def generate_direct_solution():
    """生成直接使用section的方案"""
    
    return """-- 达梦数据库最终脚本 - 直接方案
-- section不是保留字，可以直接使用

-- art_center_info表
DROP TABLE IF EXISTS art_center_info;
CREATE TABLE art_center_info (
  id BIGINT PRIMARY KEY,
  address VARCHAR(255),
  introduction VARCHAR(2000),
  metro VARCHAR(255),
  open_time VARCHAR(255),
  pic_info VARCHAR(255),
  section VARCHAR(255),
  traffic VARCHAR(255)
);

-- 其他核心表...
-- app_activity表
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT PRIMARY KEY,
  address VARCHAR(255),
  content VARCHAR(255),
  title VARCHAR(255),
  activity_status INT,
  activity_type TINYINT,
  create_time DATETIME
);

-- app_customer表
DROP TABLE IF EXISTS app_customer;
CREATE TABLE app_customer (
  id BIGINT PRIMARY KEY,
  real_name VARCHAR(255),
  phone VARCHAR(255),
  unionid VARCHAR(255),
  create_time DATETIME
);

-- app_config表
DROP TABLE IF EXISTS app_config;
CREATE TABLE app_config (
  id BIGINT PRIMARY KEY,
  group_no VARCHAR(255),
  rule_name VARCHAR(255),
  rule_value VARCHAR(255)
);

SELECT '直接方案脚本执行完成' AS result;
"""

def generate_quotes_solution():
    """生成使用双引号的方案"""
    
    return """-- 达梦数据库最终脚本 - 双引号方案
-- section是保留字，使用双引号包围

-- art_center_info表
DROP TABLE IF EXISTS art_center_info;
CREATE TABLE art_center_info (
  id BIGINT PRIMARY KEY,
  address VARCHAR(255),
  introduction VARCHAR(2000),
  metro VARCHAR(255),
  open_time VARCHAR(255),
  pic_info VARCHAR(255),
  "section" VARCHAR(255),
  traffic VARCHAR(255)
);

-- 测试插入（注意双引号）
INSERT INTO art_center_info (id, address, "section") 
VALUES (1, '测试地址', '测试区域');

-- 测试查询（注意双引号）
SELECT id, address, "section" FROM art_center_info;

-- 其他核心表...
-- app_activity表
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT PRIMARY KEY,
  address VARCHAR(255),
  content VARCHAR(255),
  title VARCHAR(255),
  activity_status INT,
  activity_type TINYINT,
  create_time DATETIME
);

-- app_customer表
DROP TABLE IF EXISTS app_customer;
CREATE TABLE app_customer (
  id BIGINT PRIMARY KEY,
  real_name VARCHAR(255),
  phone VARCHAR(255),
  unionid VARCHAR(255),
  create_time DATETIME
);

-- app_config表
DROP TABLE IF EXISTS app_config;
CREATE TABLE app_config (
  id BIGINT PRIMARY KEY,
  group_no VARCHAR(255),
  rule_name VARCHAR(255),
  rule_value VARCHAR(255)
);

SELECT '双引号方案脚本执行完成' AS result;
"""

def generate_rename_solution():
    """生成重命名section的方案"""
    
    return """-- 达梦数据库最终脚本 - 重命名方案
-- section是保留字且双引号无效，重命名为section_info

-- art_center_info表
DROP TABLE IF EXISTS art_center_info;
CREATE TABLE art_center_info (
  id BIGINT PRIMARY KEY,
  address VARCHAR(255),
  introduction VARCHAR(2000),
  metro VARCHAR(255),
  open_time VARCHAR(255),
  pic_info VARCHAR(255),
  section_info VARCHAR(255),
  traffic VARCHAR(255)
);

-- 测试插入
INSERT INTO art_center_info (id, address, section_info) 
VALUES (1, '测试地址', '测试区域');

-- 测试查询
SELECT id, address, section_info FROM art_center_info;

-- 其他核心表...
-- app_activity表
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT PRIMARY KEY,
  address VARCHAR(255),
  content VARCHAR(255),
  title VARCHAR(255),
  activity_status INT,
  activity_type TINYINT,
  create_time DATETIME
);

-- app_customer表
DROP TABLE IF EXISTS app_customer;
CREATE TABLE app_customer (
  id BIGINT PRIMARY KEY,
  real_name VARCHAR(255),
  phone VARCHAR(255),
  unionid VARCHAR(255),
  create_time DATETIME
);

-- app_config表
DROP TABLE IF EXISTS app_config;
CREATE TABLE app_config (
  id BIGINT PRIMARY KEY,
  group_no VARCHAR(255),
  rule_name VARCHAR(255),
  rule_value VARCHAR(255)
);

SELECT '重命名方案脚本执行完成' AS result;
"""

if __name__ == "__main__":
    success = generate_dm_sql_based_on_test_results()
    
    if not success:
        print("\n❌ 生成失败，请先解决基础连接问题")
        sys.exit(1)
    
    print(f"\n🎯 下一步:")
    print(f"1. 在达梦数据库中执行生成的SQL脚本")
    print(f"2. 如果成功，可以基于此方案转换完整的46个表")
    print(f"3. 如果失败，请提供错误信息以便进一步调整")
