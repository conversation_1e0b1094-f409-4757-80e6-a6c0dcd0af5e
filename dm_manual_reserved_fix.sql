-- 达梦数据库手动修复版本
-- 专门修复已知的保留字冲突问题

-- 测试保留字修复
DROP TABLE IF EXISTS test_reserved_fix;
CREATE TABLE test_reserved_fix (
  id BIGINT NOT NULL,
  test_name VARCHAR(100),
  PRIMARY KEY (id)
);

-- 修复 art_center_info 表的 section 保留字问题
DROP TABLE IF EXISTS art_center_info;
CREATE TABLE art_center_info (
  id BIGINT NOT NULL,
  address VARCHAR(255) DEFAULT NULL,
  introduction VARCHAR(2000) DEFAULT NULL,
  metro VARCHAR(255) DEFAULT NULL,
  open_time VARCHAR(255) DEFAULT NULL,
  pic_info VARCHAR(255) DEFAULT NULL,
  "section" VARCHAR(255) DEFAULT NULL,
  traffic VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 修复其他可能有保留字冲突的核心表

-- app_activity 表 (修复 status, type 等保留字)
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT NOT NULL,
  address VARCHAR(255) DEFAULT NULL,
  content VARCHAR(255) DEFAULT NULL,
  conver_picture VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  end_time DATETIME DEFAULT NULL,
  sort_order INT DEFAULT NULL,
  start_time DATETIME DEFAULT NULL,
  "status" INT DEFAULT NULL,
  title VARCHAR(255) DEFAULT NULL,
  "type" TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- app_customer 表
DROP TABLE IF EXISTS app_customer;
CREATE TABLE app_customer (
  id BIGINT NOT NULL,
  breaked_num INT DEFAULT NULL,
  breaked_total_num INT DEFAULT NULL,
  card_category TINYINT DEFAULT NULL,
  card_no VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  customer_id VARCHAR(255) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  job VARCHAR(255) DEFAULT NULL,
  mini_openid VARCHAR(255) DEFAULT NULL,
  phone VARCHAR(255) DEFAULT NULL,
  real_name VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  user_avatar_src VARCHAR(255) DEFAULT NULL,
  user_birthdate VARCHAR(255) DEFAULT NULL,
  user_gender TINYINT DEFAULT NULL,
  user_name VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- app_config 表
DROP TABLE IF EXISTS app_config;
CREATE TABLE app_config (
  id BIGINT NOT NULL,
  group_no VARCHAR(255) DEFAULT NULL,
  rule_name VARCHAR(255) DEFAULT NULL,
  rule_value VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- black_list 表 (修复 name, status 保留字)
DROP TABLE IF EXISTS black_list;
CREATE TABLE black_list (
  id BIGINT NOT NULL,
  category VARCHAR(255) DEFAULT NULL,
  locking_date_time DATETIME DEFAULT NULL,
  "name" VARCHAR(255) DEFAULT NULL,
  "status" VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  unlocking_date_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 测试插入数据
INSERT INTO test_reserved_fix (id, test_name) VALUES (1, '保留字修复测试');
INSERT INTO art_center_info (id, address, "section") VALUES (1, '测试地址', '测试区域');
INSERT INTO app_activity (id, title, "status", "type") VALUES (1, '测试活动', 1, 1);

-- 测试查询
SELECT * FROM test_reserved_fix;
SELECT * FROM art_center_info;
SELECT * FROM app_activity;

-- 验证成功信息
SELECT '保留字修复测试完成' AS result;
