#!/usr/bin/env python3
"""
达梦数据库官方语法转换器
基于达梦数据库官方文档的SQL语法规范
参考: https://eco.dameng.com/document/dm/zh-cn/pm/dm_sql-introduction.html
"""

import re
import sys

def convert_to_dm_official_syntax(input_file, output_file):
    """根据达梦数据库官方语法规范进行转换"""
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 '{input_file}'")
        return False
    
    print("基于达梦数据库官方语法规范进行转换...")
    
    # 达梦数据库标准头部
    dm_sql = """-- 达梦数据库 DM_SQL 兼容脚本
-- 基于达梦数据库官方语法规范转换
-- 参考文档: https://eco.dameng.com/document/dm/zh-cn/pm/dm_sql-introduction.html

-- 设置达梦数据库参数
-- SET IDENTITY_INSERT OFF;

"""
    
    # 提取表定义
    table_pattern = r'-- Table structure for table `(\w+)`.*?CREATE TABLE `(\w+)` \((.*?)\) ENGINE=.*?;'
    tables = re.findall(table_pattern, content, re.DOTALL)
    
    print(f"发现 {len(tables)} 个表，开始转换...")
    
    for i, (table_name, table_name2, table_def) in enumerate(tables, 1):
        print(f"转换表 {i}/{len(tables)}: {table_name}")
        
        # 添加表头注释
        dm_sql += f"-- 表: {table_name}\n"
        dm_sql += f"DROP TABLE IF EXISTS {table_name};\n"
        dm_sql += f"CREATE TABLE {table_name} (\n"
        
        # 处理列定义
        columns_sql = process_dm_official_columns(table_def)
        dm_sql += columns_sql
        
        dm_sql += ");\n\n"
    
    # 写入输出文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(dm_sql)
        print(f"转换完成! 输出文件: {output_file}")
        return True
    except Exception as e:
        print(f"写入文件错误: {e}")
        return False

def process_dm_official_columns(table_def):
    """根据达梦数据库官方语法处理列定义"""
    
    lines = table_def.strip().split('\n')
    processed_columns = []
    primary_key_column = None
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # 查找主键
        if line.startswith('PRIMARY KEY'):
            primary_key_match = re.search(r'PRIMARY KEY \(`(\w+)`\)', line)
            if primary_key_match:
                primary_key_column = primary_key_match.group(1)
            continue
            
        # 跳过其他键定义
        if line.startswith('KEY ') or line.startswith('UNIQUE KEY'):
            continue
            
        # 处理列定义
        if line.startswith('`'):
            processed_col = process_dm_official_column(line)
            if processed_col:
                processed_columns.append(processed_col)
    
    # 构建结果
    result = ""
    for i, col in enumerate(processed_columns):
        result += f"  {col}"
        if i < len(processed_columns) - 1:
            result += ","
        result += "\n"
    
    # 添加主键约束（达梦数据库标准语法）
    if primary_key_column:
        if processed_columns:
            result = result.rstrip('\n') + ",\n"
        result += f"  PRIMARY KEY ({primary_key_column})\n"
    
    return result

def process_dm_official_column(line):
    """根据达梦数据库官方语法处理单个列定义"""
    
    # 移除尾部逗号和反引号
    line = line.rstrip(',').strip()
    line = re.sub(r'`([^`]+)`', r'\1', line)
    
    # 根据达梦数据库官方文档进行数据类型转换
    dm_type_conversions = {
        # 日期时间类型 - 根据达梦文档1.4.2节
        r'\bdatetime\(6\)': 'DATETIME',  # 达梦支持DATETIME类型
        r'\btimestamp\(6\)': 'TIMESTAMP',  # 达梦支持TIMESTAMP类型
        r'\bdatetime\b': 'DATETIME',
        r'\btimestamp\b': 'TIMESTAMP',
        r'\bdate\b': 'DATE',
        r'\btime\b': 'TIME',
        
        # 数值类型 - 根据达梦文档1.4.1节
        r'\bbigint\b': 'BIGINT',      # 达梦支持BIGINT
        r'\bint\b(?!\s*\()': 'INT',   # 达梦支持INT
        r'\bsmallint\b': 'SMALLINT',  # 达梦支持SMALLINT
        r'\btinyint\b': 'TINYINT',    # 达梦支持TINYINT
        r'\bdecimal\b': 'DECIMAL',    # 达梦支持DECIMAL
        r'\bnumeric\b': 'NUMERIC',    # 达梦支持NUMERIC
        r'\bfloat\b': 'FLOAT',        # 达梦支持FLOAT
        r'\bdouble\b': 'DOUBLE',      # 达梦支持DOUBLE
        r'\breal\b': 'REAL',          # 达梦支持REAL
        
        # 字符类型 - 根据达梦文档1.4.1节
        r'\bvarchar\b': 'VARCHAR',    # 达梦支持VARCHAR
        r'\bchar\b': 'CHAR',          # 达梦支持CHAR
        r'\btext\b': 'TEXT',          # 达梦支持TEXT
        
        # 二进制类型 - 根据达梦文档1.4.1节
        r'\bbinary\b': 'BINARY',      # 达梦支持BINARY
        r'\bvarbinary\b': 'VARBINARY', # 达梦支持VARBINARY
        r'\btinyblob\b': 'BLOB',      # 转换为达梦的BLOB
        r'\bblob\b': 'BLOB',          # 达梦支持BLOB
        
        # 位类型 - 根据达梦文档1.4.1节
        r'\bbit\b': 'BIT'             # 达梦支持BIT
    }
    
    # 应用数据类型转换
    for pattern, replacement in dm_type_conversions.items():
        line = re.sub(pattern, replacement, line, flags=re.IGNORECASE)
    
    # 处理AUTO_INCREMENT - 达梦数据库不直接支持AUTO_INCREMENT
    # 根据达梦文档，可以使用序列(SEQUENCE)来实现自增功能
    if 'AUTO_INCREMENT' in line.upper():
        # 移除AUTO_INCREMENT关键字，后续可以通过序列实现
        line = re.sub(r'\s+AUTO_INCREMENT', '', line, flags=re.IGNORECASE)
        # 可以在这里添加注释说明需要创建序列
        if 'NOT NULL' in line.upper():
            line += ' -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增'
    
    # 移除MySQL特有的属性
    line = re.sub(r'\s+COLLATE\s+\w+', '', line, flags=re.IGNORECASE)
    line = re.sub(r'\s+CHARACTER\s+SET\s+\w+', '', line, flags=re.IGNORECASE)
    
    # 转换注释格式 - 达梦支持 -- 注释格式
    comment_match = re.search(r'\s+COMMENT\s+\'([^\']+)\'', line)
    if comment_match:
        comment_text = comment_match.group(1)
        line = re.sub(r'\s+COMMENT\s+\'[^\']+\'', f' -- {comment_text}', line)
    
    return line

def create_dm_sequence_script(tables):
    """为需要自增的表创建达梦数据库序列脚本"""
    
    sequence_script = """-- 达梦数据库序列创建脚本
-- 用于替代MySQL的AUTO_INCREMENT功能

"""
    
    # 这里可以根据需要为每个有AUTO_INCREMENT的表创建序列
    # 示例序列创建语法（根据达梦文档）
    sequence_script += """-- 序列创建示例:
-- CREATE SEQUENCE seq_table_name
--   START WITH 1
--   INCREMENT BY 1
--   MAXVALUE 999999999999999999
--   MINVALUE 1
--   NOCYCLE
--   CACHE 20;

-- 使用方法:
-- INSERT INTO table_name (id, other_columns) VALUES (seq_table_name.NEXTVAL, ...);

"""
    
    return sequence_script

def create_dm_test_basic():
    """创建达梦数据库基础测试脚本"""
    
    test_script = """-- 达梦数据库基础兼容性测试
-- 测试基本的表创建和数据操作

-- 测试1: 基本表创建
CREATE TABLE dm_test_basic (
  id BIGINT NOT NULL,
  name VARCHAR(100),
  create_time DATETIME,
  PRIMARY KEY (id)
);

-- 测试2: 插入数据
INSERT INTO dm_test_basic (id, name, create_time) 
VALUES (1, '测试数据', SYSDATE);

-- 测试3: 查询数据
SELECT * FROM dm_test_basic;

-- 测试4: 清理
DROP TABLE dm_test_basic;

-- 如果以上测试都能正常执行，说明达梦数据库环境正常
"""
    
    with open('dm_basic_compatibility_test.sql', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("已创建达梦数据库基础兼容性测试: dm_basic_compatibility_test.sql")

if __name__ == "__main__":
    input_file = "dump-web_spup_test-202505222301.sql.bak"
    output_file = "dump-web_spup_test-dm-official.sql"
    
    print("=== 达梦数据库官方语法转换器 ===")
    print("基于达梦数据库官方文档语法规范")
    print("参考: https://eco.dameng.com/document/dm/zh-cn/pm/dm_sql-introduction.html")
    print()
    
    success = convert_to_dm_official_syntax(input_file, output_file)
    
    if success:
        create_dm_test_basic()
        
        print(f"\n✅ 转换完成!")
        print(f"📁 主要文件: {output_file}")
        print(f"🧪 测试文件: dm_basic_compatibility_test.sql")
        print(f"\n📋 使用步骤:")
        print(f"1. 先运行测试文件验证达梦数据库环境")
        print(f"2. 然后导入主要的数据库结构文件")
        print(f"3. 根据需要创建序列来替代AUTO_INCREMENT功能")
        
        print(f"\n📖 重要说明:")
        print(f"- 已根据达梦数据库官方语法规范进行转换")
        print(f"- AUTO_INCREMENT已移除，建议使用序列(SEQUENCE)实现")
        print(f"- 所有数据类型已映射为达梦数据库支持的类型")
        print(f"- 保留了中文注释，达梦数据库完全支持")
    else:
        print("❌ 转换失败")
        sys.exit(1)
