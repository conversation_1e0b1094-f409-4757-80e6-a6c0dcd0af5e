#!/usr/bin/env python3
"""
达梦数据库保留字修复转换器
修复所有保留字冲突问题
"""

import re
import sys

def fix_reserved_words_in_dm_sql(input_file, output_file):
    """修复达梦数据库中的保留字冲突"""

    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 '{input_file}'")
        return False

    print("修复达梦数据库保留字冲突...")

    # 达梦数据库保留字列表（常见的）
    dm_reserved_words = [
        'section', 'order', 'group', 'user', 'table', 'index', 'key', 'value',
        'type', 'status', 'name', 'date', 'time', 'level', 'role', 'option',
        'comment', 'desc', 'asc', 'limit', 'offset', 'union', 'join', 'where',
        'select', 'from', 'insert', 'update', 'delete', 'create', 'drop', 'alter'
    ]

    # 修复保留字冲突
    fixed_content = content

    for word in dm_reserved_words:
        # 修复列定义中的保留字（在列名后面有空格和数据类型的情况）
        pattern = rf'\b{word}\s+(\w+(?:\(\d+\))?(?:\s+(?:NOT\s+)?NULL)?(?:\s+DEFAULT\s+\w+)?)'
        replacement = rf'"{word}" \1'
        fixed_content = re.sub(pattern, replacement, fixed_content, flags=re.IGNORECASE)

        # 修复PRIMARY KEY中的保留字
        pattern = rf'PRIMARY KEY \({word}\)'
        replacement = rf'PRIMARY KEY ("{word}")'
        fixed_content = re.sub(pattern, replacement, fixed_content, flags=re.IGNORECASE)

    # 写入修复后的文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        print(f"保留字修复完成: {output_file}")
        return True
    except Exception as e:
        print(f"写入文件错误: {e}")
        return False

def create_manual_fixed_version():
    """手动创建修复版本，专门处理已知的问题表"""

    manual_fixed_sql = """-- 达梦数据库手动修复版本
-- 专门修复已知的保留字冲突问题

-- 测试保留字修复
DROP TABLE IF EXISTS test_reserved_fix;
CREATE TABLE test_reserved_fix (
  id BIGINT NOT NULL,
  test_name VARCHAR(100),
  PRIMARY KEY (id)
);

-- 修复 art_center_info 表的 section 保留字问题
DROP TABLE IF EXISTS art_center_info;
CREATE TABLE art_center_info (
  id BIGINT NOT NULL,
  address VARCHAR(255) DEFAULT NULL,
  introduction VARCHAR(2000) DEFAULT NULL,
  metro VARCHAR(255) DEFAULT NULL,
  open_time VARCHAR(255) DEFAULT NULL,
  pic_info VARCHAR(255) DEFAULT NULL,
  "section" VARCHAR(255) DEFAULT NULL,
  traffic VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 修复其他可能有保留字冲突的核心表

-- app_activity 表 (修复 status, type 等保留字)
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT NOT NULL,
  address VARCHAR(255) DEFAULT NULL,
  content VARCHAR(255) DEFAULT NULL,
  conver_picture VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  end_time DATETIME DEFAULT NULL,
  sort_order INT DEFAULT NULL,
  start_time DATETIME DEFAULT NULL,
  "status" INT DEFAULT NULL,
  title VARCHAR(255) DEFAULT NULL,
  "type" TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- app_customer 表
DROP TABLE IF EXISTS app_customer;
CREATE TABLE app_customer (
  id BIGINT NOT NULL,
  breaked_num INT DEFAULT NULL,
  breaked_total_num INT DEFAULT NULL,
  card_category TINYINT DEFAULT NULL,
  card_no VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  customer_id VARCHAR(255) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  job VARCHAR(255) DEFAULT NULL,
  mini_openid VARCHAR(255) DEFAULT NULL,
  phone VARCHAR(255) DEFAULT NULL,
  real_name VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  user_avatar_src VARCHAR(255) DEFAULT NULL,
  user_birthdate VARCHAR(255) DEFAULT NULL,
  user_gender TINYINT DEFAULT NULL,
  user_name VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- app_config 表
DROP TABLE IF EXISTS app_config;
CREATE TABLE app_config (
  id BIGINT NOT NULL,
  group_no VARCHAR(255) DEFAULT NULL,
  rule_name VARCHAR(255) DEFAULT NULL,
  rule_value VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- black_list 表 (修复 name, status 保留字)
DROP TABLE IF EXISTS black_list;
CREATE TABLE black_list (
  id BIGINT NOT NULL,
  category VARCHAR(255) DEFAULT NULL,
  locking_date_time DATETIME DEFAULT NULL,
  "name" VARCHAR(255) DEFAULT NULL,
  "status" VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  unlocking_date_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 测试插入数据
INSERT INTO test_reserved_fix (id, test_name) VALUES (1, '保留字修复测试');
INSERT INTO art_center_info (id, address, "section") VALUES (1, '测试地址', '测试区域');
INSERT INTO app_activity (id, title, "status", "type") VALUES (1, '测试活动', 1, 1);

-- 测试查询
SELECT * FROM test_reserved_fix;
SELECT * FROM art_center_info;
SELECT * FROM app_activity;

-- 验证成功信息
SELECT '保留字修复测试完成' AS result;
"""

    with open('dm_manual_reserved_fix.sql', 'w', encoding='utf-8') as f:
        f.write(manual_fixed_sql)

    print("已创建手动修复版本: dm_manual_reserved_fix.sql")

if __name__ == "__main__":
    input_file = "dump-web_spup_test-dm-clean.sql"
    output_file = "dump-web_spup_test-dm-reserved-fixed.sql"

    print("=== 达梦数据库保留字修复转换器 ===")
    print("修复section等保留字冲突问题...")
    print()

    # 创建手动修复版本
    create_manual_fixed_version()

    success = fix_reserved_words_in_dm_sql(input_file, output_file)

    if success:
        print(f"\n✅ 保留字修复完成!")
        print(f"📁 自动修复版本: {output_file}")
        print(f"📁 手动修复版本: dm_manual_reserved_fix.sql")
        print(f"\n🎯 问题解决:")
        print(f"- 'section' 保留字已用双引号包围: \"section\"")
        print(f"- 'status', 'type', 'name' 等保留字已修复")
        print(f"- 所有可能的保留字冲突已处理")
        print(f"\n📋 使用建议:")
        print(f"1. 先测试 dm_manual_reserved_fix.sql (包含核心表)")
        print(f"2. 如果成功，再使用完整版本")
        print(f"3. 确保在达梦数据库中使用双引号包围保留字")
    else:
        print("❌ 修复失败")
        sys.exit(1)
