-- 达梦数据库保留字安全版本
-- 使用双引号包围可能的保留字

-- 问题表: art_center_info (可能包含保留字section)
DROP TABLE IF EXISTS art_center_info;
CREATE TABLE art_center_info (
  id BIGINT NOT NULL,
  address VARCHAR(255),
  introduction VARCHAR(2000),
  metro VARCHAR(255),
  open_time VARCHAR(255),
  pic_info VARCHAR(255),
  "section" VARCHAR(255),  -- 使用双引号包围保留字
  traffic VARCHAR(255),
  PRIMARY KEY (id)
);

-- 测试表创建
CREATE TABLE test_reserved_words (
  id BIGINT NOT NULL,
  "name" VARCHAR(100),     -- name可能是保留字
  "type" VARCHAR(50),      -- type可能是保留字  
  "status" VARCHAR(50),    -- status可能是保留字
  "order" INT,             -- order是保留字
  "group" VARCHAR(50),     -- group是保留字
  "user" VARCHAR(50),      -- user可能是保留字
  "value" VARCHAR(255),    -- value可能是保留字
  PRIMARY KEY (id)
);

-- 测试插入
INSERT INTO test_reserved_words (id, "name", "type", "status", "order", "group", "user", "value")
VALUES (1, '测试', '类型', '状态', 1, '组', '用户', '值');

-- 测试查询
SELECT * FROM test_reserved_words;

-- 清理测试
DROP TABLE test_reserved_words;
