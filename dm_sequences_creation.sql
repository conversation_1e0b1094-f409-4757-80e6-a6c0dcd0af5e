-- 达梦数据库序列创建脚本
-- 用于替代MySQL的AUTO_INCREMENT功能
-- 基于达梦数据库官方语法规范

-- 为所有需要自增ID的表创建序列

-- 1. app_activity 活动表序列
CREATE SEQUENCE seq_app_activity
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 2. app_activity_entry_rule 活动报名规则表序列
CREATE SEQUENCE seq_app_activity_entry_rule
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 3. app_activity_entry_user 活动报名用户表序列
CREATE SEQUENCE seq_app_activity_entry_user
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 4. app_appointment_analysis 预约分析表序列
CREATE SEQUENCE seq_app_appointment_analysis
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 5. app_appointment_instructions 预约说明表序列
CREATE SEQUENCE seq_app_appointment_instructions
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 6. app_appointment_item_order 预约项目订单表序列
CREATE SEQUENCE seq_app_appointment_item_order
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 7. app_appointment_item_suborder 预约项目子订单表序列
CREATE SEQUENCE seq_app_appointment_item_suborder
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 8. app_appointment_offline 线下预约表序列
CREATE SEQUENCE seq_app_appointment_offline
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 9. app_appointment_order 预约订单表序列
CREATE SEQUENCE seq_app_appointment_order
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 10. app_appointment_order_temporary_exhibition 临时展览预约订单表序列
CREATE SEQUENCE seq_app_appointment_order_temporary_exhibition
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 11. app_appointment_personal_offline 个人线下预约表序列
CREATE SEQUENCE seq_app_appointment_personal_offline
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 12. app_appointment_suborder 预约子订单表序列
CREATE SEQUENCE seq_app_appointment_suborder
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 13. app_appointment_suborder_temporary_exhibition 临时展览预约子订单表序列
CREATE SEQUENCE seq_app_appointment_suborder_temporary_exhibition
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 14. app_appointment_team_offline 团队线下预约表序列
CREATE SEQUENCE seq_app_appointment_team_offline
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 15. app_appointment_team_order 团队预约订单表序列
CREATE SEQUENCE seq_app_appointment_team_order
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 16. app_batch 批次表序列
CREATE SEQUENCE seq_app_batch
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 17. app_batch_set 批次设置表序列
CREATE SEQUENCE seq_app_batch_set
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 18. app_batch_set_detail 批次设置详情表序列
CREATE SEQUENCE seq_app_batch_set_detail
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 19. app_comments 评论表序列
CREATE SEQUENCE seq_app_comments
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 20. app_config 配置表序列
CREATE SEQUENCE seq_app_config
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 21. app_customer 客户表序列
CREATE SEQUENCE seq_app_customer
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 22. app_customer_contacts 客户联系人表序列
CREATE SEQUENCE seq_app_customer_contacts
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 23. app_manage_role 管理角色表序列
CREATE SEQUENCE seq_app_manage_role
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 24. app_manage_user 管理用户表序列
CREATE SEQUENCE seq_app_manage_user
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 25. app_media 媒体表序列
CREATE SEQUENCE seq_app_media
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 26. app_operate_log 操作日志表序列
CREATE SEQUENCE seq_app_operate_log
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 27. app_surrounding_goods 周边商品表序列
CREATE SEQUENCE seq_app_surrounding_goods
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 28. app_temporary_exhibition 临时展览表序列
CREATE SEQUENCE seq_app_temporary_exhibition
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 29. app_visit_guide 参观指南表序列
CREATE SEQUENCE seq_app_visit_guide
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 30. app_workday 工作日表序列
CREATE SEQUENCE seq_app_workday
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 31. app_workday_temporary_exhibition 临时展览工作日表序列
CREATE SEQUENCE seq_app_workday_temporary_exhibition
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 32. art_center_info 艺术中心信息表序列
CREATE SEQUENCE seq_art_center_info
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 33. black_list 黑名单表序列
CREATE SEQUENCE seq_black_list
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 34. comm_questionnaire 问卷表序列
CREATE SEQUENCE seq_comm_questionnaire
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 35. comm_questionnaire_answer 问卷答案表序列
CREATE SEQUENCE seq_comm_questionnaire_answer
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 36. exhibition_info 展览信息表序列
CREATE SEQUENCE seq_exhibition_info
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 37. mp_datacube_everday 每日数据立方体表序列
CREATE SEQUENCE seq_mp_datacube_everday
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 38. mp_datacube_everday_article_summary 每日文章汇总表序列
CREATE SEQUENCE seq_mp_datacube_everday_article_summary
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 39. mp_datacube_everday_user_summary 每日用户汇总表序列
CREATE SEQUENCE seq_mp_datacube_everday_user_summary
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 40. one_time_tasks 一次性任务表序列
CREATE SEQUENCE seq_one_time_tasks
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 41. round_config 轮次配置表序列
CREATE SEQUENCE seq_round_config
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 42. volunteer_info 志愿者信息表序列
CREATE SEQUENCE seq_volunteer_info
  START WITH 1
  INCREMENT BY 1
  MAXVALUE 999999999999999999
  MINVALUE 1
  NOCYCLE
  CACHE 20;

-- 序列使用示例:
-- INSERT INTO app_activity (id, title, address) VALUES (seq_app_activity.NEXTVAL, '活动标题', '活动地址');
-- INSERT INTO app_customer (id, real_name, phone) VALUES (seq_app_customer.NEXTVAL, '客户姓名', '手机号');

-- 查看所有序列:
-- SELECT * FROM USER_SEQUENCES;

-- 查看序列当前值:
-- SELECT seq_app_activity.CURRVAL FROM DUAL;

-- 查看序列下一个值:
-- SELECT seq_app_activity.NEXTVAL FROM DUAL;
