-- 达梦数据库简化语法测试脚本
-- 请在达梦数据库客户端中逐步执行

-- 环境检查
SELECT '=== 环境检查 ===' AS step;
SELECT VERSION() AS database_version;
SELECT USER AS current_user;
SELECT SYSDATE AS current_time;

-- 基础表测试
SELECT '=== 基础表测试 ===' AS step;

DROP TABLE IF EXISTS test_basic;
CREATE TABLE test_basic (id INT PRIMARY KEY);
INSERT INTO test_basic VALUES (1);
SELECT * FROM test_basic;
DROP TABLE test_basic;

-- 数据类型测试
SELECT '=== 数据类型测试 ===' AS step;

DROP TABLE IF EXISTS test_types;
CREATE TABLE test_types (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100),
    create_time DATETIME
);
INSERT INTO test_types VALUES (1, '类型测试', SYSDATE);
SELECT * FROM test_types;
DROP TABLE test_types;

-- section保留字测试（方案1：不带引号）
SELECT '=== section保留字测试1 ===' AS step;

-- 如果这个失败，说明section是保留字
DROP TABLE IF EXISTS test_section1;
CREATE TABLE test_section1 (
    id BIGINT PRIMARY KEY,
    section VARCHAR(100)
);
INSERT INTO test_section1 VALUES (1, 'section测试');
SELECT * FROM test_section1;
DROP TABLE test_section1;

-- section保留字测试（方案2：带引号）
SELECT '=== section保留字测试2 ===' AS step;

DROP TABLE IF EXISTS test_section2;
CREATE TABLE test_section2 (
    id BIGINT PRIMARY KEY,
    "section" VARCHAR(100)
);
INSERT INTO test_section2 VALUES (1, 'section引号测试');
SELECT id, "section" FROM test_section2;
DROP TABLE test_section2;

-- art_center_info表测试（重命名方案）
SELECT '=== art_center_info重命名方案 ===' AS step;

DROP TABLE IF EXISTS art_center_info;
CREATE TABLE art_center_info (
    id BIGINT PRIMARY KEY,
    address VARCHAR(255),
    introduction VARCHAR(2000),
    metro VARCHAR(255),
    open_time VARCHAR(255),
    pic_info VARCHAR(255),
    section_info VARCHAR(255),
    traffic VARCHAR(255)
);

INSERT INTO art_center_info (id, address, section_info) 
VALUES (1, '测试地址', '测试区域');

SELECT * FROM art_center_info;

-- art_center_info表测试（双引号方案）
SELECT '=== art_center_info双引号方案 ===' AS step;

DROP TABLE art_center_info;
CREATE TABLE art_center_info (
    id BIGINT PRIMARY KEY,
    address VARCHAR(255),
    introduction VARCHAR(2000),
    metro VARCHAR(255),
    open_time VARCHAR(255),
    pic_info VARCHAR(255),
    "section" VARCHAR(255),
    traffic VARCHAR(255)
);

INSERT INTO art_center_info (id, address, "section") 
VALUES (1, '测试地址', '测试区域');

SELECT id, address, "section" FROM art_center_info;

-- 其他核心表测试
SELECT '=== 其他核心表测试 ===' AS step;

-- app_activity表
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
    id BIGINT PRIMARY KEY,
    address VARCHAR(255),
    content VARCHAR(255),
    title VARCHAR(255),
    activity_status INT,
    activity_type TINYINT,
    create_time DATETIME
);

INSERT INTO app_activity (id, title, activity_status, activity_type, create_time)
VALUES (1, '测试活动', 1, 1, SYSDATE);

SELECT * FROM app_activity;

-- app_customer表
DROP TABLE IF EXISTS app_customer;
CREATE TABLE app_customer (
    id BIGINT PRIMARY KEY,
    real_name VARCHAR(255),
    phone VARCHAR(255),
    unionid VARCHAR(255),
    create_time DATETIME
);

INSERT INTO app_customer (id, real_name, phone, create_time)
VALUES (1, '测试用户', '13800138000', SYSDATE);

SELECT * FROM app_customer;

-- 清理
SELECT '=== 清理测试表 ===' AS step;
DROP TABLE IF EXISTS art_center_info;
DROP TABLE IF EXISTS app_activity;
DROP TABLE IF EXISTS app_customer;

SELECT '测试完成' AS result, SYSDATE AS completion_time;
