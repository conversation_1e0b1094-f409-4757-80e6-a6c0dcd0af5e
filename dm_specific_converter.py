#!/usr/bin/env python3
"""
DM Database Specific Converter
Addresses DM database specific requirements and syntax
"""

import re
import sys

def convert_to_dm_specific(input_file, output_file):
    """Convert to DM database with specific DM requirements"""
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
        return False
    
    print("Creating DM-specific database schema...")
    
    # DM-specific header with schema creation
    dm_sql = """-- DM Database Schema Creation Script
-- Converted from MySQL 8.0.42 for DM Database compatibility
-- Database: web_spup_test

-- Create schema if not exists (DM specific)
-- CREATE SCHEMA IF NOT EXISTS web_spup_test;
-- USE web_spup_test;

-- Set DM specific parameters
SET IDENTITY_INSERT OFF;

"""
    
    # Extract table definitions
    table_pattern = r'-- Table structure for table `(\w+)`.*?CREATE TABLE `(\w+)` \((.*?)\) ENGINE=.*?;'
    tables = re.findall(table_pattern, content, re.DOTALL)
    
    print(f"Converting {len(tables)} tables for DM database...")
    
    for i, (table_name, table_name2, table_def) in enumerate(tables, 1):
        print(f"Processing table {i}/{len(tables)}: {table_name}")
        
        # Add table header
        dm_sql += f"--\n-- Table: {table_name}\n--\n"
        dm_sql += f"DROP TABLE IF EXISTS {table_name};\n"
        dm_sql += f"CREATE TABLE {table_name} (\n"
        
        # Process columns
        columns = process_dm_columns(table_def)
        dm_sql += columns
        
        dm_sql += ");\n\n"
    
    # Write output
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(dm_sql)
        print(f"DM-specific conversion completed: {output_file}")
        return True
    except Exception as e:
        print(f"Error writing output: {e}")
        return False

def process_dm_columns(table_def):
    """Process columns with DM-specific requirements"""
    
    lines = table_def.strip().split('\n')
    processed_columns = []
    primary_key = None
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # Handle PRIMARY KEY
        if line.startswith('PRIMARY KEY'):
            primary_key_match = re.search(r'PRIMARY KEY \(`(\w+)`\)', line)
            if primary_key_match:
                primary_key = primary_key_match.group(1)
            continue
            
        # Skip other keys
        if line.startswith('KEY ') or line.startswith('UNIQUE KEY'):
            continue
            
        # Process column definitions
        if line.startswith('`'):
            processed_col = process_dm_column(line)
            if processed_col:
                processed_columns.append(processed_col)
    
    # Build result
    result = ""
    for i, col in enumerate(processed_columns):
        result += f"  {col}"
        if i < len(processed_columns) - 1:
            result += ","
        result += "\n"
    
    # Add primary key
    if primary_key:
        if processed_columns:
            result = result.rstrip('\n') + ",\n"
        result += f"  CONSTRAINT PK_{primary_key} PRIMARY KEY ({primary_key})\n"
    
    return result

def process_dm_column(line):
    """Process individual column for DM compatibility"""
    
    # Remove trailing comma and backticks
    line = line.rstrip(',').strip()
    line = re.sub(r'`([^`]+)`', r'\1', line)
    
    # DM-specific data type conversions
    dm_conversions = {
        # Use DATETIME instead of TIMESTAMP(6) for DM compatibility
        r'\bdatetime\(6\)': 'DATETIME',
        r'\btimestamp\(6\)': 'DATETIME', 
        r'\bdatetime\b': 'DATETIME',
        r'\btinyblob\b': 'BLOB',
        r'\bbigint\b': 'BIGINT',
        r'\bint\b(?!\s*\()': 'INT',
        r'\bsmallint\b': 'SMALLINT', 
        r'\btinyint\b': 'TINYINT',
        r'\bdate\b': 'DATE',
        r'\bvarchar\b': 'VARCHAR',
        r'\bchar\b': 'CHAR',
        r'\btext\b': 'TEXT'
    }
    
    for pattern, replacement in dm_conversions.items():
        line = re.sub(pattern, replacement, line, flags=re.IGNORECASE)
    
    # Handle AUTO_INCREMENT - use simpler IDENTITY syntax for DM
    if 'AUTO_INCREMENT' in line.upper():
        # For DM, use IDENTITY without parameters
        line = re.sub(
            r'(\w+)\s+BIGINT\s+NOT\s+NULL\s+AUTO_INCREMENT',
            r'\1 BIGINT IDENTITY NOT NULL',
            line,
            flags=re.IGNORECASE
        )
        line = re.sub(
            r'(\w+)\s+INT\s+NOT\s+NULL\s+AUTO_INCREMENT', 
            r'\1 INT IDENTITY NOT NULL',
            line,
            flags=re.IGNORECASE
        )
        line = re.sub(r'\s+AUTO_INCREMENT', '', line, flags=re.IGNORECASE)
    
    # Remove MySQL-specific attributes
    line = re.sub(r'\s+COLLATE\s+\w+', '', line, flags=re.IGNORECASE)
    line = re.sub(r'\s+CHARACTER\s+SET\s+\w+', '', line, flags=re.IGNORECASE)
    
    # Convert comments to DM format
    comment_match = re.search(r'\s+COMMENT\s+\'([^\']+)\'', line)
    if comment_match:
        comment_text = comment_match.group(1)
        line = re.sub(r'\s+COMMENT\s+\'[^\']+\'', f' -- {comment_text}', line)
    
    return line

def create_dm_test_script():
    """Create a simple test script to verify DM compatibility"""
    
    test_script = """-- DM Database Test Script
-- Test basic table creation and IDENTITY functionality

-- Test simple table creation
DROP TABLE IF EXISTS dm_test_table;
CREATE TABLE dm_test_table (
  id BIGINT IDENTITY NOT NULL,
  name VARCHAR(100) NOT NULL,
  created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT PK_dm_test_table PRIMARY KEY (id)
);

-- Test insert
INSERT INTO dm_test_table (name) VALUES ('Test Record');

-- Test select
SELECT * FROM dm_test_table;

-- Clean up
DROP TABLE dm_test_table;

-- If this script runs successfully, your DM database is ready for the main schema
"""
    
    with open('dm_test_script.sql', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("Created DM test script: dm_test_script.sql")

if __name__ == "__main__":
    input_file = "dump-web_spup_test-202505222301.sql.bak"
    output_file = "dump-web_spup_test-dm-specific.sql"
    
    print("=== DM Database Specific Converter ===")
    print("Addressing DM database specific requirements...")
    print()
    
    success = convert_to_dm_specific(input_file, output_file)
    
    if success:
        create_dm_test_script()
        print(f"\n✅ DM-specific conversion completed!")
        print(f"📁 Main schema: {output_file}")
        print(f"🧪 Test script: dm_test_script.sql")
        print(f"\n📋 Next steps:")
        print(f"1. Run dm_test_script.sql first to verify DM compatibility")
        print(f"2. If test passes, run {output_file} to create full schema")
    else:
        print("❌ Conversion failed")
        sys.exit(1)
