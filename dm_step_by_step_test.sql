-- 达梦数据库逐步测试脚本
-- 从最基础的语法开始测试

-- 步骤1: 测试最简单的表创建
CREATE TABLE test1 (
  id INT
);

-- 步骤2: 测试插入和查询
INSERT INTO test1 (id) VALUES (1);
SELECT * FROM test1;

-- 步骤3: 测试删除表
DROP TABLE test1;

-- 步骤4: 测试带主键的表
CREATE TABLE test2 (
  id INT PRIMARY KEY,
  name VARCHAR(50)
);

INSERT INTO test2 (id, name) VALUES (1, 'test');
SELECT * FROM test2;
DROP TABLE test2;

-- 步骤5: 测试BIGINT类型
CREATE TABLE test3 (
  id BIGINT PRIMARY KEY,
  name VARCHAR(100)
);

INSERT INTO test3 (id, name) VALUES (1, 'bigint_test');
SELECT * FROM test3;
DROP TABLE test3;

-- 步骤6: 测试DATETIME类型
CREATE TABLE test4 (
  id BIGINT PRIMARY KEY,
  create_time DATETIME
);

INSERT INTO test4 (id, create_time) VALUES (1, SYSDATE);
SELECT * FROM test4;
DROP TABLE test4;

-- 步骤7: 测试保留字section
CREATE TABLE test5 (
  id BIGINT PRIMARY KEY,
  "section" VARCHAR(100)
);

INSERT INTO test5 (id, "section") VALUES (1, 'section_test');
SELECT id, "section" FROM test5;
DROP TABLE test5;

-- 如果所有步骤都成功，显示成功消息
SELECT '所有基础测试通过！' AS result;
