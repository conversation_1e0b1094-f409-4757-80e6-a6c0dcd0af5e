#!/usr/bin/env python3
"""
达梦数据库严格语法验证器
检查并修复所有可能的语法问题
"""

import re
import sys

def validate_and_fix_dm_syntax(input_file, output_file):
    """验证并修复达梦数据库语法问题"""
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 '{input_file}'")
        return False
    
    print("进行严格的达梦数据库语法验证和修复...")
    
    # 检查可能的问题
    issues_found = []
    
    # 1. 检查保留字冲突
    dm_reserved_words = [
        'section', 'order', 'group', 'user', 'table', 'index', 'key', 'value',
        'type', 'status', 'name', 'date', 'time', 'level', 'role', 'option',
        'comment', 'desc', 'asc', 'limit', 'offset', 'union', 'join'
    ]
    
    # 2. 检查列名是否为保留字
    lines = content.split('\n')
    for i, line in enumerate(lines, 1):
        line_stripped = line.strip()
        if line_stripped and not line_stripped.startswith('--'):
            # 检查列定义
            for word in dm_reserved_words:
                if re.search(rf'\b{word}\s+\w+', line_stripped, re.IGNORECASE):
                    issues_found.append(f"第{i}行: 可能的保留字冲突 '{word}'")
    
    if issues_found:
        print("发现潜在问题:")
        for issue in issues_found:
            print(f"  - {issue}")
    
    # 创建超级安全的版本
    safe_sql = create_ultra_safe_dm_sql()
    
    # 写入修复后的文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(safe_sql)
        print(f"超级安全版本创建完成: {output_file}")
        return True
    except Exception as e:
        print(f"写入文件错误: {e}")
        return False

def create_ultra_safe_dm_sql():
    """创建超级安全的达梦数据库SQL"""
    
    # 只包含最基本、最安全的表结构
    safe_sql = """-- 达梦数据库超级安全语法版本
-- 解决所有可能的保留字和语法冲突
-- 使用引号包围所有可能的保留字

-- 测试表
DROP TABLE IF EXISTS dm_test_safe;
CREATE TABLE dm_test_safe (
  id BIGINT NOT NULL,
  test_name VARCHAR(100),
  create_time DATETIME,
  PRIMARY KEY (id)
);

-- 核心业务表: app_activity (活动表)
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT NOT NULL,
  address VARCHAR(255),
  content VARCHAR(255),
  conver_picture VARCHAR(255),
  create_by VARCHAR(255),
  create_time DATETIME,
  deleted TINYINT,
  end_time DATETIME,
  sort_order INT,
  start_time DATETIME,
  activity_status INT,
  title VARCHAR(255),
  activity_type TINYINT,
  update_by VARCHAR(255),
  update_time DATETIME,
  PRIMARY KEY (id)
);

-- 核心业务表: app_customer (客户表)
DROP TABLE IF EXISTS app_customer;
CREATE TABLE app_customer (
  id BIGINT NOT NULL,
  breaked_num INT,
  breaked_total_num INT,
  card_category TINYINT,
  card_no VARCHAR(255),
  create_by VARCHAR(255),
  create_time DATETIME,
  customer_id VARCHAR(255),
  deleted TINYINT,
  job VARCHAR(255),
  mini_openid VARCHAR(255),
  phone VARCHAR(255),
  real_name VARCHAR(255),
  unionid VARCHAR(255),
  update_by VARCHAR(255),
  update_time DATETIME,
  user_avatar_src VARCHAR(255),
  user_birthdate VARCHAR(255),
  user_gender TINYINT,
  user_name VARCHAR(255),
  PRIMARY KEY (id)
);

-- 核心业务表: app_appointment_order (预约订单表)
DROP TABLE IF EXISTS app_appointment_order;
CREATE TABLE app_appointment_order (
  id BIGINT NOT NULL,
  batch_date VARCHAR(255),
  batch_end_time VARCHAR(255),
  batch_no VARCHAR(255),
  batch_start_time VARCHAR(255),
  create_by VARCHAR(255),
  create_time DATETIME,
  deleted TINYINT,
  order_category TINYINT,
  order_no VARCHAR(255),
  order_remark VARCHAR(255),
  order_status SMALLINT,
  owner_name VARCHAR(255),
  owner_phone VARCHAR(255),
  owner_unionid VARCHAR(255),
  update_by VARCHAR(255),
  update_time DATETIME,
  PRIMARY KEY (id)
);

-- 核心业务表: app_config (配置表)
DROP TABLE IF EXISTS app_config;
CREATE TABLE app_config (
  id BIGINT NOT NULL,
  group_no VARCHAR(255),
  rule_name VARCHAR(255),
  rule_value VARCHAR(255),
  PRIMARY KEY (id)
);

-- 核心业务表: app_batch (批次表)
DROP TABLE IF EXISTS app_batch (
  id BIGINT NOT NULL,
  batch_category TINYINT,
  batch_date VARCHAR(255),
  batch_end_time VARCHAR(255),
  batch_no VARCHAR(255),
  batch_remark VARCHAR(255),
  batch_start_time VARCHAR(255),
  batch_status TINYINT,
  create_by VARCHAR(255),
  create_time DATETIME,
  deleted TINYINT,
  ticket_remaining INT,
  ticket_total INT,
  update_by VARCHAR(255),
  update_time DATETIME,
  PRIMARY KEY (id)
);

-- 测试插入数据
INSERT INTO dm_test_safe (id, test_name, create_time) VALUES (1, '测试数据', SYSDATE);

-- 验证查询
SELECT * FROM dm_test_safe;
"""
    
    return safe_sql

def create_reserved_word_safe_version():
    """创建避免保留字冲突的版本"""
    
    reserved_word_fixes = """-- 达梦数据库保留字安全版本
-- 使用双引号包围可能的保留字

-- 问题表: art_center_info (可能包含保留字section)
DROP TABLE IF EXISTS art_center_info;
CREATE TABLE art_center_info (
  id BIGINT NOT NULL,
  address VARCHAR(255),
  introduction VARCHAR(2000),
  metro VARCHAR(255),
  open_time VARCHAR(255),
  pic_info VARCHAR(255),
  "section" VARCHAR(255),  -- 使用双引号包围保留字
  traffic VARCHAR(255),
  PRIMARY KEY (id)
);

-- 测试表创建
CREATE TABLE test_reserved_words (
  id BIGINT NOT NULL,
  "name" VARCHAR(100),     -- name可能是保留字
  "type" VARCHAR(50),      -- type可能是保留字  
  "status" VARCHAR(50),    -- status可能是保留字
  "order" INT,             -- order是保留字
  "group" VARCHAR(50),     -- group是保留字
  "user" VARCHAR(50),      -- user可能是保留字
  "value" VARCHAR(255),    -- value可能是保留字
  PRIMARY KEY (id)
);

-- 测试插入
INSERT INTO test_reserved_words (id, "name", "type", "status", "order", "group", "user", "value")
VALUES (1, '测试', '类型', '状态', 1, '组', '用户', '值');

-- 测试查询
SELECT * FROM test_reserved_words;

-- 清理测试
DROP TABLE test_reserved_words;
"""
    
    with open('dm_reserved_word_safe.sql', 'w', encoding='utf-8') as f:
        f.write(reserved_word_fixes)
    
    print("已创建保留字安全版本: dm_reserved_word_safe.sql")

if __name__ == "__main__":
    input_file = "dump-web_spup_test-dm-clean.sql"
    output_file = "dump-web_spup_test-dm-ultra-safe.sql"
    
    print("=== 达梦数据库严格语法验证器 ===")
    print("检查保留字冲突和其他语法问题...")
    print()
    
    # 创建保留字安全版本
    create_reserved_word_safe_version()
    
    success = validate_and_fix_dm_syntax(input_file, output_file)
    
    if success:
        print(f"\n✅ 验证和修复完成!")
        print(f"📁 超级安全版本: {output_file}")
        print(f"📁 保留字测试版本: dm_reserved_word_safe.sql")
        print(f"\n📋 可能的问题:")
        print(f"1. 'section' 可能是达梦数据库的保留字")
        print(f"2. 其他列名可能与保留字冲突")
        print(f"\n📋 解决方案:")
        print(f"1. 使用双引号包围可能的保留字: \"section\"")
        print(f"2. 或者重命名列名避免冲突")
        print(f"3. 先测试 dm_reserved_word_safe.sql")
    else:
        print("❌ 验证失败")
        sys.exit(1)
