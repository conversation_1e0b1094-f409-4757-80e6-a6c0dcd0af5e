#!/usr/bin/env python3
"""
达梦数据库语法修复转换器
修复注释位置导致的语法错误
"""

import re
import sys

def fix_dm_syntax_issues(input_file, output_file):
    """修复达梦数据库语法问题"""
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 '{input_file}'")
        return False
    
    print("修复达梦数据库语法问题...")
    
    # 修复注释语法问题
    # 将行内注释移到行末，并确保语法正确
    
    # 处理每个CREATE TABLE语句
    def fix_table_syntax(match):
        table_name = match.group(1)
        table_content = match.group(2)
        
        print(f"修复表: {table_name}")
        
        # 分割成行
        lines = table_content.split('\n')
        fixed_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 处理列定义行
            if line.startswith('id ') or line.startswith('address ') or any(line.startswith(col + ' ') for col in [
                'content', 'conver_picture', 'create_by', 'create_time', 'deleted', 
                'end_time', 'sort', 'start_time', 'status', 'title', 'type', 
                'update_by', 'update_time', 'activity_id', 'entry_end_time',
                'entry_limit', 'entry_start_time', 'ext_attr', 'user_gender',
                'user_idcard', 'user_idcard_type', 'user_name', 'user_phone',
                'user_unionid', 'analysis_date', 'item_checkin_am', 'item_checkin_pm',
                'item_checkin_total', 'item_reserve_am', 'item_reserve_pm',
                'item_reserve_refund_active', 'item_reserve_refund_passive',
                'item_reserve_total', 'ticket_checkin_am', 'ticket_checkin_pm',
                'ticket_checkin_total', 'ticket_reserve_am', 'ticket_reserve_pm',
                'ticket_reserve_refund_active', 'ticket_reserve_refund_passive',
                'ticket_reserve_total', 'admission_notice', 'audience_notice',
                'visiting_instructions', 'batch_date', 'batch_end_time',
                'batch_no', 'batch_start_time', 'item', 'order_category',
                'order_no', 'order_status', 'owner_name', 'owner_phone',
                'owner_unionid', 'contacts_idcard_category', 'contacts_idcard_no',
                'contacts_name', 'contacts_phone', 'onwer_unionid', 'seat_no',
                'suborder_no', 'suborder_status'
            ]):
                # 移除行内注释，稍后统一处理
                if ' -- ' in line:
                    parts = line.split(' -- ', 1)
                    line_without_comment = parts[0].strip()
                    comment = parts[1].strip()
                    
                    # 确保行末有逗号（除了最后一行）
                    if not line_without_comment.endswith(',') and not line_without_comment.endswith(')'):
                        line_without_comment += ','
                    
                    fixed_lines.append(f"  {line_without_comment}")
                else:
                    # 没有注释的行，确保语法正确
                    if not line.endswith(',') and not line.endswith(')') and not line.startswith('PRIMARY'):
                        line += ','
                    fixed_lines.append(f"  {line}")
            elif line.startswith('PRIMARY KEY'):
                fixed_lines.append(f"  {line}")
            else:
                fixed_lines.append(f"  {line}")
        
        # 重新组装表定义
        result = f"CREATE TABLE {table_name} (\n"
        result += '\n'.join(fixed_lines)
        result += "\n);"
        
        return result
    
    # 使用正则表达式查找并修复所有CREATE TABLE语句
    table_pattern = r'CREATE TABLE (\w+) \((.*?)\);'
    content = re.sub(table_pattern, fix_table_syntax, content, flags=re.DOTALL)
    
    # 写入修复后的文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"语法修复完成: {output_file}")
        return True
    except Exception as e:
        print(f"写入文件错误: {e}")
        return False

def create_fixed_dm_sql():
    """创建修复后的达梦数据库SQL文件"""
    
    # 手动创建修复后的app_activity表定义
    fixed_sql = """-- 达梦数据库 DM_SQL 兼容脚本 (语法修复版)
-- 基于达梦数据库官方语法规范转换
-- 修复了注释语法问题

-- 表: app_activity
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT NOT NULL,
  address VARCHAR(255) DEFAULT NULL,
  content VARCHAR(255) DEFAULT NULL,
  conver_picture VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  end_time DATETIME DEFAULT NULL,
  sort INT DEFAULT NULL,
  start_time DATETIME DEFAULT NULL,
  status INT DEFAULT NULL,
  title VARCHAR(255) DEFAULT NULL,
  type TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 注释说明:
-- id: 主键，原MySQL的AUTO_INCREMENT，建议创建序列实现自增
-- address: 活动地点
-- content: 活动内容  
-- conver_picture: 活动封面图
-- end_time: 活动结束时间
-- sort: 排序值
-- start_time: 活动开始时间
-- status: 活动状态
-- title: 活动标题
-- type: 活动类型

"""
    
    with open('app_activity_fixed.sql', 'w', encoding='utf-8') as f:
        f.write(fixed_sql)
    
    print("已创建修复版本: app_activity_fixed.sql")

if __name__ == "__main__":
    input_file = "dump-web_spup_test-dm-official.sql"
    output_file = "dump-web_spup_test-dm-fixed.sql"
    
    print("=== 达梦数据库语法修复工具 ===")
    print("修复注释语法导致的错误...")
    print()
    
    # 先创建单个表的修复示例
    create_fixed_dm_sql()
    
    print(f"\n📋 问题分析:")
    print(f"- 达梦数据库不支持在列定义中间使用行注释")
    print(f"- 注释应该放在表定义之外或使用块注释")
    print(f"- 已创建修复版本的示例")
    
    print(f"\n✅ 解决方案:")
    print(f"1. 使用 app_activity_fixed.sql 作为正确的语法示例")
    print(f"2. 将注释移到表定义外部")
    print(f"3. 确保列定义语法简洁明确")
