-- 达梦数据库表名测试脚本
-- 测试不同的表名格式

-- 测试1: 简单表名
CREATE TABLE simple (id INT);
DROP TABLE simple;

-- 测试2: 全大写表名
CREATE TABLE TESTTABLE (id INT);
DROP TABLE TESTTABLE;

-- 测试3: 全小写表名
CREATE TABLE testtable (id INT);
DROP TABLE testtable;

-- 测试4: 下划线表名
CREATE TABLE test_table (id INT);
DROP TABLE test_table;

-- 测试5: 数字结尾表名
CREATE TABLE test1 (id INT);
DROP TABLE test1;

-- 测试6: 带引号的表名
CREATE TABLE "TestTable" (id INT);
DROP TABLE "TestTable";

-- 测试7: 常见业务表名
CREATE TABLE app_test (id INT);
DROP TABLE app_test;

-- 测试8: 中文表名（如果支持）
-- CREATE TABLE 测试表 (id INT);
-- DROP TABLE 测试表;

SELECT '表名测试完成' AS result;
