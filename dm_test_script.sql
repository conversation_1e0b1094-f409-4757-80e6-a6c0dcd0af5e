-- DM Database Test Script
-- Test basic table creation and IDENTITY functionality

-- Test simple table creation
DROP TABLE IF EXISTS dm_test_table;
CREATE TABLE dm_test_table (
  id BIGINT IDENTITY NOT NULL,
  name VARCHAR(100) NOT NULL,
  created_date D<PERSON>ETIME DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT PK_dm_test_table PRIMARY KEY (id)
);

-- Test insert
INSERT INTO dm_test_table (name) VALUES ('Test Record');

-- Test select
SELECT * FROM dm_test_table;

-- Clean up
DROP TABLE dm_test_table;

-- If this script runs successfully, your DM database is ready for the main schema
