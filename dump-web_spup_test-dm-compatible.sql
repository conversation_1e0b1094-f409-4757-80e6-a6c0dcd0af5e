-- DM Database dump converted from MySQL 8.0.42
--
-- Host: localhost    Database: web_spup_test
-- ------------------------------------------------------
-- Converted for DM Database compatibility

-- Set basic session parameters for DM
SET IDENTITY_INSERT OFF;

-- Table structure for table activity_info
--

DROP TABLE IF EXISTS activity_info;

CREATE TABLE activity_info (
  id BIGINT NOT NULL,
  activity_id varchar(255) DEFAULT NULL,
  activity_name varchar(255) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL,
  create_on TIMESTAMP(6) DEFAULT NULL,
  deleted INT NOT NULL,
  end_date_time TIMESTAMP(6) NOT NULL,
  introduction_info varchar(255) NOT NULL,
  others_info varchar(255) DEFAULT NULL,
  pic_url varchar(255) DEFAULT NULL,
  start_date_time TIMESTAMP(6) NOT NULL,
  status varchar(255) DEFAULT NULL,
  type varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_on TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table activity_info
--

--
-- Table structure for table activity_round_info
--

DROP TABLE IF EXISTS activity_round_info;

CREATE TABLE activity_round_info (
  id BIGINT NOT NULL,
  act_round_end_date_time TIMESTAMP(6) NOT NULL,
  act_round_id varchar(255) DEFAULT NULL,
  act_round_info varchar(255) DEFAULT NULL,
  act_round_max_submit_num INT NOT NULL,
  act_round_start_date_time TIMESTAMP(6) NOT NULL,
  act_round_submit_end_date_time TIMESTAMP(6) NOT NULL,
  act_round_submit_number INT DEFAULT NULL,
  act_round_submit_start_date_time TIMESTAMP(6) NOT NULL,
  activity_id varchar(255) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL,
  create_on TIMESTAMP(6) DEFAULT NULL,
  deleted INT NOT NULL,
  other_info varchar(255) DEFAULT NULL,
  status varchar(255) DEFAULT NULL,
  type varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_on TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table activity_round_info
--

--
-- Table structure for table activity_submit_customer
--

DROP TABLE IF EXISTS activity_submit_customer;

CREATE TABLE activity_submit_customer (
  id BIGINT NOT NULL,
  act_round_id varchar(255) DEFAULT NULL,
  age INT NOT NULL,
  check_in_date_time TIMESTAMP(6) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL,
  create_on TIMESTAMP(6) DEFAULT NULL,
  gender INT NOT NULL,
  pass_string varchar(255) DEFAULT NULL,
  pass_type varchar(255) NOT NULL,
  phone_string varchar(255) DEFAULT NULL,
  status varchar(255) DEFAULT NULL,
  submit_id varchar(255) DEFAULT NULL,
  type varchar(255) DEFAULT NULL,
  unionid varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_on TIMESTAMP(6) DEFAULT NULL,
  username varchar(255) DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table activity_submit_customer
--

--
-- Table structure for table app_activity
--

DROP TABLE IF EXISTS app_activity;

CREATE TABLE app_activity (
  id BIGINT NOT NULL AUTO_INCREMENT,
  address varchar(255) DEFAULT NULL -- 活动地点,
  content varchar(255) DEFAULT NULL -- 活动内容,
  conver_picture varchar(255) DEFAULT NULL -- 活动封面图,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  end_time TIMESTAMP(6) DEFAULT NULL -- 活动结束时间,
  sort INT DEFAULT NULL -- 排序值,
  start_time TIMESTAMP(6) DEFAULT NULL -- 活动开始时间,
  status INT DEFAULT NULL -- 活动状态,
  title varchar(255) DEFAULT NULL -- 活动标题,
  type TINYINT DEFAULT NULL -- 活动类型,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_activity
--

--
-- Table structure for table app_activity_entry_rule
--

DROP TABLE IF EXISTS app_activity_entry_rule;

CREATE TABLE app_activity_entry_rule (
  id BIGINT NOT NULL AUTO_INCREMENT,
  activity_id BIGINT DEFAULT NULL -- 活动id,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  entry_end_time TIMESTAMP(6) DEFAULT NULL,
  entry_limit INT DEFAULT NULL -- 报名人数限制,
  entry_start_time TIMESTAMP(6) DEFAULT NULL -- 报名开始时间,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_activity_entry_rule
--

--
-- Table structure for table app_activity_entry_user
--

DROP TABLE IF EXISTS app_activity_entry_user;

CREATE TABLE app_activity_entry_user (
  id BIGINT NOT NULL AUTO_INCREMENT,
  activity_id BIGINT DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL -- 前端表，unionid,
  create_time TIMESTAMP(6) DEFAULT NULL -- 创建时间,
  deleted TINYINT DEFAULT NULL,
  ext_attr varchar(255) DEFAULT NULL -- 扩展字段,
  update_by varchar(255) DEFAULT NULL -- 前端表，unionid,
  update_time TIMESTAMP(6) DEFAULT NULL -- 修改时间,
  user_gender varchar(255) DEFAULT NULL -- 用户性别,
  user_idcard varchar(255) DEFAULT NULL -- 用户证件号,
  user_idcard_type TINYINT DEFAULT NULL -- 用户证件类型,
  user_name varchar(255) DEFAULT NULL -- 用户姓名,
  user_phone varchar(255) DEFAULT NULL -- 用户手机号,
  user_unionid varchar(255) DEFAULT NULL -- 用户unionid,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_activity_entry_user
--

--
-- Table structure for table app_appointment_analysis
--

DROP TABLE IF EXISTS app_appointment_analysis;

CREATE TABLE app_appointment_analysis (
  id BIGINT NOT NULL AUTO_INCREMENT,
  analysis_date varchar(255) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL -- 创建者unionid,
  create_time TIMESTAMP(6) DEFAULT NULL -- 创建时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  item_checkin_am INT DEFAULT NULL -- 飞阅浦东上午核销数,
  item_checkin_pm INT DEFAULT NULL -- 飞阅浦东下午核销数,
  item_checkin_total INT DEFAULT NULL -- 飞阅浦东核销数,
  item_reserve_am INT DEFAULT NULL -- 飞阅浦东上午预约数,
  item_reserve_pm INT DEFAULT NULL -- 飞阅浦东下午预约数,
  item_reserve_refund_active INT DEFAULT NULL -- 飞阅浦东主动退票数,
  item_reserve_refund_passive INT DEFAULT NULL -- 飞阅浦东被动退票数,
  item_reserve_total INT DEFAULT NULL -- 飞阅浦东展现预约总数,
  ticket_checkin_am INT DEFAULT NULL -- 上午核销数,
  ticket_checkin_pm INT DEFAULT NULL -- 下午核销数,
  ticket_checkin_total INT DEFAULT NULL -- 门票核销总数,
  ticket_reserve_am INT DEFAULT NULL -- 上午预约数,
  ticket_reserve_pm INT DEFAULT NULL -- 下午预约数,
  ticket_reserve_refund_active INT DEFAULT NULL -- 主动退票数,
  ticket_reserve_refund_passive INT DEFAULT NULL -- 被动退票数,
  ticket_reserve_total INT DEFAULT NULL -- 门票预约总数,
  update_by varchar(255) DEFAULT NULL -- 更新者unionid,
  update_time TIMESTAMP(6) DEFAULT NULL -- 数据更新时间,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_appointment_analysis
--

--
-- Table structure for table app_appointment_instructions
--

DROP TABLE IF EXISTS app_appointment_instructions;

CREATE TABLE app_appointment_instructions (
  id BIGINT NOT NULL AUTO_INCREMENT,
  admission_notice varchar(255) DEFAULT NULL -- 入馆须知,
  audience_notice varchar(255) DEFAULT NULL -- 观众须知,
  create_by varchar(255) DEFAULT NULL -- 创建者unionid,
  create_time TIMESTAMP(6) DEFAULT NULL -- 生成时间,
  update_by varchar(255) DEFAULT NULL -- 更新者unionid,
  update_time TIMESTAMP(6) DEFAULT NULL -- 数据更新时间,
  visiting_instructions varchar(255) DEFAULT NULL -- 参观须知,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_appointment_instructions
--

--
-- Table structure for table app_appointment_item_order
--

DROP TABLE IF EXISTS app_appointment_item_order;

CREATE TABLE app_appointment_item_order (
  id BIGINT NOT NULL AUTO_INCREMENT,
  batch_date varchar(255) DEFAULT NULL -- 场次日期,
  batch_end_time varchar(255) DEFAULT NULL -- 场次结束时间,
  batch_no varchar(255) DEFAULT NULL -- 场次编号,
  batch_start_time varchar(255) DEFAULT NULL -- 场次开始时间,
  create_by varchar(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  create_time TIMESTAMP(6) DEFAULT NULL -- 订单生成时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  item varchar(255) DEFAULT NULL,
  order_category TINYINT DEFAULT NULL -- 订单分类，可能会有团体订单等其他的,
  order_no varchar(255) DEFAULT NULL -- 订单编号,
  order_status SMALLINT DEFAULT NULL -- 订单状态,
  owner_name varchar(255) DEFAULT NULL -- 订单所有者姓名,
  owner_phone varchar(255) DEFAULT NULL -- 订单所有者手机号,
  owner_unionid varchar(255) DEFAULT NULL -- 用户小程序唯一id,
  update_by varchar(255) DEFAULT NULL -- 更新者unionid,
  update_time TIMESTAMP(6) DEFAULT NULL -- 数据更新时间,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_appointment_item_order
--

--
-- Table structure for table app_appointment_item_suborder
--

DROP TABLE IF EXISTS app_appointment_item_suborder;

CREATE TABLE app_appointment_item_suborder (
  id BIGINT NOT NULL AUTO_INCREMENT -- 子订单自增id,
  batch_date varchar(255) DEFAULT NULL,
  batch_end_time varchar(255) DEFAULT NULL,
  batch_no varchar(255) DEFAULT NULL,
  batch_start_time varchar(255) DEFAULT NULL,
  contacts_idcard_category TINYINT DEFAULT NULL,
  contacts_idcard_no varchar(255) DEFAULT NULL,
  contacts_name varchar(255) DEFAULT NULL,
  contacts_phone varchar(255) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  onwer_unionid varchar(255) DEFAULT NULL -- 用户小程序唯一id,
  order_no varchar(255) DEFAULT NULL -- 订单编号,
  seat_no TINYINT DEFAULT NULL,
  suborder_no varchar(255) DEFAULT NULL -- 子订单编号,
  suborder_status SMALLINT DEFAULT NULL -- 子订单状态-无符号int，状态可以为负数，表明异常状态；1：待支付，2: 待服务，4: 服务中，8: 已完成。 -1：售后中； -2: 已退款； -4: 已取消；,
  update_by varchar(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_appointment_item_suborder
--

--
-- Table structure for table app_appointment_offline
--

DROP TABLE IF EXISTS app_appointment_offline;

CREATE TABLE app_appointment_offline (
  id BIGINT NOT NULL AUTO_INCREMENT,
  appoint_batch_end_time varchar(255) DEFAULT NULL,
  appoint_batch_no varchar(255) DEFAULT NULL,
  appoint_batch_start_time varchar(255) DEFAULT NULL,
  appoint_date varchar(255) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  persons_num INT DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  visit_info varchar(255) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_appointment_offline
--

--
-- Table structure for table app_appointment_order
--

DROP TABLE IF EXISTS app_appointment_order;

CREATE TABLE app_appointment_order (
  id BIGINT NOT NULL AUTO_INCREMENT,
  batch_date varchar(255) DEFAULT NULL -- 场次日期,
  batch_end_time varchar(255) DEFAULT NULL -- 场次结束时间,
  batch_no varchar(255) DEFAULT NULL -- 场次编号,
  batch_start_time varchar(255) DEFAULT NULL -- 场次开始时间,
  create_by varchar(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  create_time TIMESTAMP(6) DEFAULT NULL -- 订单生成时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  order_category TINYINT DEFAULT NULL -- 订单分类，可能会有团体订单等其他的,
  order_no varchar(255) DEFAULT NULL -- 订单编号,
  order_remark varchar(255) DEFAULT NULL,
  order_status SMALLINT DEFAULT NULL -- 订单状态,
  owner_name varchar(255) DEFAULT NULL -- 订单所有者姓名,
  owner_phone varchar(255) DEFAULT NULL -- 订单所有者手机号,
  owner_unionid varchar(255) DEFAULT NULL -- 用户小程序唯一id,
  update_by varchar(255) DEFAULT NULL -- 更新者unionid,
  update_time TIMESTAMP(6) DEFAULT NULL -- 数据更新时间,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_appointment_order
--

--
-- Table structure for table app_appointment_order_temporary_exhibition
--

DROP TABLE IF EXISTS app_appointment_order_temporary_exhibition;

CREATE TABLE app_appointment_order_temporary_exhibition (
  id BIGINT NOT NULL AUTO_INCREMENT,
  batch_date varchar(255) DEFAULT NULL,
  batch_end_time varchar(255) DEFAULT NULL,
  batch_no varchar(255) DEFAULT NULL,
  batch_start_time varchar(255) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  exhibition_no varchar(255) DEFAULT NULL,
  exhibition_title varchar(255) DEFAULT NULL,
  order_category TINYINT DEFAULT NULL,
  order_no varchar(255) DEFAULT NULL,
  order_status SMALLINT DEFAULT NULL,
  owner_name varchar(255) DEFAULT NULL,
  owner_phone varchar(255) DEFAULT NULL,
  owner_unionid varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_appointment_order_temporary_exhibition
--

--
-- Table structure for table app_appointment_personal_offline
--

DROP TABLE IF EXISTS app_appointment_personal_offline;

CREATE TABLE app_appointment_personal_offline (
  id BIGINT NOT NULL AUTO_INCREMENT,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  person_num INT DEFAULT NULL,
  remark varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  visit_date DATE DEFAULT NULL,
  visit_fypd_batch INT DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_appointment_personal_offline
--

--
-- Table structure for table app_appointment_suborder
--

DROP TABLE IF EXISTS app_appointment_suborder;

CREATE TABLE app_appointment_suborder (
  id BIGINT NOT NULL AUTO_INCREMENT -- 子订单自增id,
  batch_date varchar(255) DEFAULT NULL,
  batch_end_time varchar(255) DEFAULT NULL,
  batch_no varchar(255) DEFAULT NULL,
  batch_start_time varchar(255) DEFAULT NULL,
  contacts_idcard_category TINYINT DEFAULT NULL,
  contacts_idcard_no varchar(255) DEFAULT NULL,
  contacts_name varchar(255) DEFAULT NULL,
  contacts_phone varchar(255) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  onwer_unionid varchar(255) DEFAULT NULL -- 用户小程序唯一id,
  order_no varchar(255) DEFAULT NULL -- 订单编号,
  suborder_no varchar(255) DEFAULT NULL -- 子订单编号,
  suborder_status SMALLINT DEFAULT NULL -- 子订单状态-无符号int，状态可以为负数，表明异常状态；1：待支付，2: 待服务，4: 服务中，8: 已完成。 -1：售后中； -2: 已退款； -4: 已取消；,
  update_by varchar(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_appointment_suborder
--

--
-- Table structure for table app_appointment_suborder_temporary_exhibition
--

DROP TABLE IF EXISTS app_appointment_suborder_temporary_exhibition;

CREATE TABLE app_appointment_suborder_temporary_exhibition (
  id BIGINT NOT NULL AUTO_INCREMENT,
  batch_date varchar(255) DEFAULT NULL,
  batch_end_time varchar(255) DEFAULT NULL,
  batch_no varchar(255) DEFAULT NULL,
  batch_start_time varchar(255) DEFAULT NULL,
  contacts_idcard_category TINYINT DEFAULT NULL,
  contacts_idcard_no varchar(255) DEFAULT NULL,
  contacts_name varchar(255) DEFAULT NULL,
  contacts_phone varchar(255) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  onwer_unionid varchar(255) DEFAULT NULL,
  order_no varchar(255) DEFAULT NULL,
  suborder_no varchar(255) DEFAULT NULL,
  suborder_status SMALLINT DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_appointment_suborder_temporary_exhibition
--

--
-- Table structure for table app_appointment_team_offline
--

DROP TABLE IF EXISTS app_appointment_team_offline;

CREATE TABLE app_appointment_team_offline (
  id BIGINT NOT NULL AUTO_INCREMENT,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  offline_info BLOB,
  order_no varchar(255) DEFAULT NULL,
  ower_unit varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  visit_date DATE DEFAULT NULL,
  visitors_num INT DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_appointment_team_offline
--

--
-- Table structure for table app_appointment_team_order
--

DROP TABLE IF EXISTS app_appointment_team_order;

CREATE TABLE app_appointment_team_order (
  id BIGINT NOT NULL AUTO_INCREMENT,
  batch_date varchar(255) DEFAULT NULL -- 场次日期,
  batch_end_time varchar(255) DEFAULT NULL -- 场次结束时间,
  batch_no varchar(255) DEFAULT NULL -- 场次编号,
  batch_start_time varchar(255) DEFAULT NULL -- 场次开始时间,
  create_by varchar(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  create_time TIMESTAMP(6) DEFAULT NULL -- 订单生成时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  exhibition_no varchar(255) DEFAULT NULL,
  method varchar(255) DEFAULT NULL,
  order_category TINYINT DEFAULT NULL -- 订单分类，可能会有团体订单等其他的,
  order_no varchar(255) DEFAULT NULL -- 订单编号,
  order_remark varchar(255) DEFAULT NULL,
  order_status SMALLINT DEFAULT NULL -- 订单状态,
  ower_unit varchar(255) DEFAULT NULL,
  ower_unit_code varchar(255) DEFAULT NULL,
  owner_name varchar(255) DEFAULT NULL -- 订单所有者姓名,
  owner_phone varchar(255) DEFAULT NULL -- 订单所有者手机号,
  owner_unionid varchar(255) DEFAULT NULL -- 用户小程序唯一id,
  supply_info varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL -- 更新者unionid,
  update_time TIMESTAMP(6) DEFAULT NULL -- 数据更新时间,
  visitors_num INT DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_appointment_team_order
--

--
-- Table structure for table app_batch
--

DROP TABLE IF EXISTS app_batch;

CREATE TABLE app_batch (
  id BIGINT NOT NULL AUTO_INCREMENT,
  batch_category TINYINT DEFAULT NULL -- 场次分类,
  batch_date varchar(255) DEFAULT NULL -- 场次日期,
  batch_end_time varchar(255) DEFAULT NULL -- 场次结束时间,
  batch_no varchar(255) DEFAULT NULL -- 场次编号,
  batch_remark varchar(255) DEFAULT NULL -- 场次备注说明，便于后期管理,
  batch_start_time varchar(255) DEFAULT NULL -- 场次开始时间,
  batch_status TINYINT DEFAULT NULL -- 场次状态,
  create_by varchar(255) DEFAULT NULL -- 系统创建-给相关类名,
  create_time TIMESTAMP(6) DEFAULT NULL -- 记录创建时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  ticket_remaining INT DEFAULT NULL -- 剩余票数,
  ticket_total INT DEFAULT NULL -- 总计发放票数,
  update_by varchar(255) DEFAULT NULL -- 创建者unionid-可为用户、管理员、系统等,
  update_time TIMESTAMP(6) DEFAULT NULL -- 记录修改时间,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_batch
--

--
-- Table structure for table app_batch_set
--

DROP TABLE IF EXISTS app_batch_set;

CREATE TABLE app_batch_set (
  id BIGINT NOT NULL AUTO_INCREMENT,
  batch_category TINYINT DEFAULT NULL -- 场次类型,
  batch_effect_end_time TIMESTAMP(6) DEFAULT NULL -- 场次生效结束日期（空表示永久）,
  batch_effect_start_time TIMESTAMP(6) DEFAULT NULL -- 场次生效开始日期,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  status TINYINT DEFAULT NULL -- 规则当前状态,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_batch_set
--

--
-- Table structure for table app_batch_set_detail
--

DROP TABLE IF EXISTS app_batch_set_detail;

CREATE TABLE app_batch_set_detail (
  id BIGINT NOT NULL AUTO_INCREMENT,
  batch_end_time varchar(255) DEFAULT NULL,
  batch_set_id BIGINT DEFAULT NULL,
  batch_start_time varchar(255) DEFAULT NULL,
  batch_ticket_total INT DEFAULT NULL,
  batch_type TINYINT DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  range_weeks varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_batch_set_detail
--

--
-- Table structure for table app_comments
--

DROP TABLE IF EXISTS app_comments;

CREATE TABLE app_comments (
  id BIGINT NOT NULL AUTO_INCREMENT,
  content varchar(255) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  create_time TIMESTAMP(6) DEFAULT NULL -- 订单生成时间,
  customer varchar(255) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  purpose varchar(255) DEFAULT NULL,
  status varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL -- 更新者unionid,
  update_time TIMESTAMP(6) DEFAULT NULL -- 数据更新时间,
  visit_time varchar(255) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_comments
--

--
-- Table structure for table app_config
--

DROP TABLE IF EXISTS app_config;

CREATE TABLE app_config (
  id BIGINT NOT NULL AUTO_INCREMENT,
  group_no varchar(255) DEFAULT NULL,
  rule_name varchar(255) DEFAULT NULL,
  rule_value varchar(255) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_config
--

--
-- Table structure for table app_customer
--

DROP TABLE IF EXISTS app_customer;

CREATE TABLE app_customer (
  id BIGINT NOT NULL AUTO_INCREMENT,
  breaked_num INT DEFAULT NULL,
  breaked_total_num INT DEFAULT NULL,
  card_category TINYINT DEFAULT NULL,
  card_no varchar(255) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL -- 系统创建-给相关类名,
  create_time TIMESTAMP(6) DEFAULT NULL -- 记录创建时间,
  customer_id varchar(255) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  job varchar(255) DEFAULT NULL,
  mini_openid varchar(255) DEFAULT NULL -- 小程序openid,
  phone varchar(255) DEFAULT NULL,
  real_name varchar(255) DEFAULT NULL,
  unionid varchar(255) DEFAULT NULL -- 微信唯一识别码,
  update_by varchar(255) DEFAULT NULL -- 创建者unionid-可为用户、管理员、系统等,
  update_time TIMESTAMP(6) DEFAULT NULL -- 记录修改时间,
  user_avatar_src varchar(255) DEFAULT NULL -- 用户本地头像，可供用户替换头像；2作为备用,
  user_birthdate varchar(255) DEFAULT NULL -- 用户生日,
  user_gender TINYINT DEFAULT NULL -- 用户性别： 1， 男， 2 女， 0 未知,
  user_name varchar(255) DEFAULT NULL -- 用户姓名,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_customer
--

--
-- Table structure for table app_customer_contacts
--

DROP TABLE IF EXISTS app_customer_contacts;

CREATE TABLE app_customer_contacts (
  id BIGINT NOT NULL AUTO_INCREMENT,
  create_by varchar(255) DEFAULT NULL -- 系统创建-给相关类名,
  create_time TIMESTAMP(6) DEFAULT NULL -- 记录创建时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  idcard_category TINYINT DEFAULT NULL,
  idcard_no varchar(255) DEFAULT NULL,
  name varchar(255) DEFAULT NULL,
  ower_unionid varchar(255) DEFAULT NULL,
  phone varchar(255) DEFAULT NULL,
  remark varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL -- 创建者unionid-可为用户、管理员、系统等,
  update_time TIMESTAMP(6) DEFAULT NULL -- 记录修改时间,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_customer_contacts
--

--
-- Table structure for table app_manage_role
--

DROP TABLE IF EXISTS app_manage_role;

CREATE TABLE app_manage_role (
  id BIGINT NOT NULL AUTO_INCREMENT,
  create_by varchar(255) DEFAULT NULL -- 创建者open_id,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  menu_code varchar(255) DEFAULT NULL,
  role_code INT DEFAULT NULL,
  role_name varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL -- 修改人open_id,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_manage_role
--

--
-- Table structure for table app_manage_user
--

DROP TABLE IF EXISTS app_manage_user;

CREATE TABLE app_manage_user (
  id BIGINT NOT NULL AUTO_INCREMENT,
  create_by varchar(255) DEFAULT NULL -- 系统创建-给相关类名,
  create_time TIMESTAMP(6) DEFAULT NULL -- 记录创建时间,
  deleted TINYINT DEFAULT NULL,
  menu_code varchar(255) DEFAULT NULL -- 菜单，以逗号分隔,
  mobile varchar(255) DEFAULT NULL -- 手机号,
  name varchar(255) DEFAULT NULL -- 用户昵称,
  open_id varchar(255) DEFAULT NULL -- 登录用户open_id,
  remark varchar(255) DEFAULT NULL -- 描述,
  role_code BIGINT DEFAULT NULL -- 角色，可配置多个角色，采用位运算,
  status TINYINT DEFAULT NULL -- 状态：1启用；2禁用；4其他,
  unionid varchar(255) DEFAULT NULL -- 登录用户统一识别码,
  update_by varchar(255) DEFAULT NULL -- 创建者unionid-可为用户、管理员、系统等,
  update_time TIMESTAMP(6) DEFAULT NULL -- 记录修改时间,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_manage_user
--

--
-- Table structure for table app_media
--

DROP TABLE IF EXISTS app_media;

CREATE TABLE app_media (
  id BIGINT NOT NULL AUTO_INCREMENT -- 媒体内容自增id,
  create_by varchar(255) DEFAULT NULL -- 创建者,
  create_time TIMESTAMP(6) DEFAULT NULL -- 记录创建时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  md5_sum varchar(255) DEFAULT NULL -- 文件校验，避免重复存储。,
  media_name varchar(255) DEFAULT NULL -- 媒体文件名（不带后缀名）,
  media_showname varchar(255) DEFAULT NULL -- 显示名称，相同文件可显示名称不同,
  media_size BIGINT DEFAULT NULL -- 文件大小,
  media_src varchar(255) DEFAULT NULL -- 文件存储路径,
  media_status TINYINT DEFAULT NULL -- 已删除，正常，已隐藏,
  media_type varchar(255) DEFAULT NULL -- 媒体文件类型,
  update_by varchar(255) DEFAULT NULL -- 修改人,
  update_time TIMESTAMP(6) DEFAULT NULL -- 记录修改时间,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_media
--

--
-- Table structure for table app_operate_log
--

DROP TABLE IF EXISTS app_operate_log;

CREATE TABLE app_operate_log (
  id BIGINT NOT NULL AUTO_INCREMENT,
  operate_params varchar(255) DEFAULT NULL,
  operate_time TIMESTAMP(6) DEFAULT NULL,
  operate_url varchar(255) DEFAULT NULL,
  operator varchar(255) DEFAULT NULL,
  operator_browser varchar(255) DEFAULT NULL,
  operator_ip varchar(255) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_operate_log
--

--
-- Table structure for table app_surrounding_goods
--

DROP TABLE IF EXISTS app_surrounding_goods;

CREATE TABLE app_surrounding_goods (
  id BIGINT NOT NULL AUTO_INCREMENT,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  goods_category varchar(255) DEFAULT NULL -- 商品分类,
  goods_conver_picture varchar(255) DEFAULT NULL,
  goods_introduce varchar(255) DEFAULT NULL -- 商品介绍,
  goods_name varchar(255) DEFAULT NULL -- 商品名称,
  goods_no varchar(255) DEFAULT NULL -- 商品编号,
  goods_price double DEFAULT NULL -- 商品价格,
  goods_status TINYINT DEFAULT NULL -- 商品状态：1上架；2下架；4其他,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_surrounding_goods
--

--
-- Table structure for table app_temporary_exhibition
--

DROP TABLE IF EXISTS app_temporary_exhibition;

CREATE TABLE app_temporary_exhibition (
  id BIGINT NOT NULL AUTO_INCREMENT,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  detail_banner_pic varchar(255) DEFAULT NULL,
  exhibition_address varchar(255) DEFAULT NULL,
  exhibition_content varchar(255) DEFAULT NULL,
  exhibition_end_date DATE DEFAULT NULL,
  exhibition_no varchar(255) DEFAULT NULL,
  exhibition_start_date DATE DEFAULT NULL,
  exhibition_title varchar(255) DEFAULT NULL,
  status TINYINT DEFAULT NULL,
  thumbnail_pic varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_temporary_exhibition
--

--
-- Table structure for table app_visit_guide
--

DROP TABLE IF EXISTS app_visit_guide;

CREATE TABLE app_visit_guide (
  id BIGINT NOT NULL AUTO_INCREMENT,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  page_views INT DEFAULT NULL,
  show_imgs varchar(255) DEFAULT NULL,
  show_voices varchar(255) DEFAULT NULL,
  sort_code INT DEFAULT NULL -- 排序值,
  spot_area varchar(255) DEFAULT NULL -- 所在区域（楼层）,
  spot_content varchar(255) DEFAULT NULL -- 展项介绍,
  spot_name varchar(255) DEFAULT NULL -- 展项名称,
  tips varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_visit_guide
--

--
-- Table structure for table app_workday
--

DROP TABLE IF EXISTS app_workday;

CREATE TABLE app_workday (
  id BIGINT NOT NULL AUTO_INCREMENT,
  config varchar(5000) DEFAULT NULL -- 工作日配置,
  day varchar(255) DEFAULT NULL,
  day_remark varchar(255) DEFAULT NULL,
  is_workday INT DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_workday
--

--
-- Table structure for table app_workday_temporary_exhibition
--

DROP TABLE IF EXISTS app_workday_temporary_exhibition;

CREATE TABLE app_workday_temporary_exhibition (
  id BIGINT NOT NULL AUTO_INCREMENT,
  day varchar(255) DEFAULT NULL,
  day_remark varchar(255) DEFAULT NULL,
  is_workday TINYINT DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table app_workday_temporary_exhibition
--

--
-- Table structure for table art_center_info
--

DROP TABLE IF EXISTS art_center_info;

CREATE TABLE art_center_info (
  id BIGINT NOT NULL AUTO_INCREMENT,
  address varchar(255) DEFAULT NULL,
  introduction varchar(2000) DEFAULT NULL,
  metro varchar(255) DEFAULT NULL,
  open_time varchar(255) DEFAULT NULL,
  pic_info varchar(255) DEFAULT NULL,
  section varchar(255) DEFAULT NULL,
  traffic varchar(255) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table art_center_info
--

--
-- Table structure for table black_list
--

DROP TABLE IF EXISTS black_list;

CREATE TABLE black_list (
  id BIGINT NOT NULL AUTO_INCREMENT,
  category varchar(255) DEFAULT NULL,
  locking_date_time TIMESTAMP(6) DEFAULT NULL,
  name varchar(255) DEFAULT NULL,
  status varchar(255) DEFAULT NULL,
  unionid varchar(255) DEFAULT NULL,
  unlocking_date_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table black_list
--

--
-- Table structure for table comm_questionnaire
--

DROP TABLE IF EXISTS comm_questionnaire;

CREATE TABLE comm_questionnaire (
  id BIGINT NOT NULL AUTO_INCREMENT,
  content varchar(255) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  options varchar(255) DEFAULT NULL,
  title varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  valid_end_date TIMESTAMP(6) DEFAULT NULL,
  valid_start_date TIMESTAMP(6) DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table comm_questionnaire
--

--
-- Table structure for table comm_questionnaire_answer
--

DROP TABLE IF EXISTS comm_questionnaire_answer;

CREATE TABLE comm_questionnaire_answer (
  id BIGINT NOT NULL AUTO_INCREMENT,
  answer varchar(255) DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  questionnaire_id BIGINT DEFAULT NULL,
  unionid varchar(255) DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table comm_questionnaire_answer
--

--
-- Table structure for table exhibition_info
--

DROP TABLE IF EXISTS exhibition_info;

CREATE TABLE exhibition_info (
  id BIGINT NOT NULL AUTO_INCREMENT,
  exhibition_id varchar(255) DEFAULT NULL,
  exhibition_status varchar(255) DEFAULT NULL,
  exhibition_type varchar(255) DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table exhibition_info
--

--
-- Table structure for table hibernate_sequence
--

DROP TABLE IF EXISTS hibernate_sequence;

CREATE TABLE hibernate_sequence (
  next_val BIGINT DEFAULT NULL
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table hibernate_sequence
--

--
-- Table structure for table mp_datacube_everday
--

DROP TABLE IF EXISTS mp_datacube_everday;

CREATE TABLE mp_datacube_everday (
  id BIGINT NOT NULL AUTO_INCREMENT,
  analysis_date DATE DEFAULT NULL,
  article_read_total INT DEFAULT NULL,
  article_summary_json varchar(255) DEFAULT NULL -- 文章阅读总数,
  article_total INT DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL,
  create_on TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_on TIMESTAMP(6) DEFAULT NULL,
  user_cumulate INT DEFAULT NULL,
  user_cumulate_json varchar(255) DEFAULT NULL -- 用户总数,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table mp_datacube_everday
--

--
-- Table structure for table mp_datacube_everday_article_summary
--

DROP TABLE IF EXISTS mp_datacube_everday_article_summary;

CREATE TABLE mp_datacube_everday_article_summary (
  id BIGINT NOT NULL AUTO_INCREMENT,
  add_to_fav_count INT DEFAULT NULL,
  add_to_fav_user INT DEFAULT NULL,
  analysis_date DATE DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL,
  create_on TIMESTAMP(6) DEFAULT NULL,
  msg_id varchar(255) DEFAULT NULL,
  send_date DATE DEFAULT NULL,
  title varchar(255) DEFAULT NULL,
  total_read_count INT DEFAULT NULL,
  total_read_user INT DEFAULT NULL,
  total_share_count INT DEFAULT NULL,
  total_share_user INT DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_on TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table mp_datacube_everday_article_summary
--

--
-- Table structure for table mp_datacube_everday_user_summary
--

DROP TABLE IF EXISTS mp_datacube_everday_user_summary;

CREATE TABLE mp_datacube_everday_user_summary (
  id BIGINT NOT NULL AUTO_INCREMENT,
  analysis_date DATE DEFAULT NULL,
  article_summary_json varchar(255) DEFAULT NULL -- 文章阅读总数,
  cancel_user INT DEFAULT NULL,
  create_by varchar(255) DEFAULT NULL,
  create_on TIMESTAMP(6) DEFAULT NULL,
  new_user INT DEFAULT NULL,
  total_visits BIGINT DEFAULT NULL,
  update_by varchar(255) DEFAULT NULL,
  update_on TIMESTAMP(6) DEFAULT NULL,
  user_cumulate INT DEFAULT NULL,
  visits BIGINT DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table mp_datacube_everday_user_summary
--

--
-- Table structure for table one_time_tasks
--

DROP TABLE IF EXISTS one_time_tasks;

CREATE TABLE one_time_tasks (
  id varchar(255) NOT NULL,
  created_at TIMESTAMP(6) DEFAULT NULL,
  deleted INT DEFAULT NULL,
  execute_time TIMESTAMP(6) DEFAULT NULL,
  status varchar(255) DEFAULT NULL,
  task_class varchar(255) DEFAULT NULL,
  task_data varchar(255) DEFAULT NULL,
  task_type varchar(255) DEFAULT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table one_time_tasks
--

--
-- Table structure for table round_config
--

DROP TABLE IF EXISTS round_config;

CREATE TABLE round_config (
  id BIGINT NOT NULL AUTO_INCREMENT,
  end_time time DEFAULT NULL,
  exhibition_id varchar(255) DEFAULT NULL,
  round_date DATE DEFAULT NULL,
  round_id varchar(255) DEFAULT NULL,
  round_status varchar(255) DEFAULT NULL,
  start_time time DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table round_config
--

--
-- Table structure for table volunteer_info
--

DROP TABLE IF EXISTS volunteer_info;

CREATE TABLE volunteer_info (
  id BIGINT NOT NULL AUTO_INCREMENT,
  age INT DEFAULT NULL,
  area varchar(255) DEFAULT NULL,
  category INT DEFAULT NULL,
  email varchar(255) DEFAULT NULL,
  fixed_tel varchar(255) DEFAULT NULL,
  gender varchar(255) DEFAULT NULL,
  name varchar(255) DEFAULT NULL,
  phone varchar(255) DEFAULT NULL,
  service_duration double DEFAULT NULL,
  skill varchar(255) DEFAULT NULL,
  uid varchar(255) DEFAULT NULL,
  unit varchar(255) DEFAULT NULL,
  volunteer_id varchar(255) DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
) COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table volunteer_info
--

--
-- Dumping routines for database 'web_spup_test'
--

-- Dump completed on 2025-05-22 23:01:43
