-- 达梦数据库最终正确版本
-- 只修复真正的保留字冲突，保持标准SQL语法
-- 基于达梦数据库官方语法规范

-- 表: activity_info
DROP TABLE IF EXISTS activity_info;
CREATE TABLE activity_info (
  id BIGINT NOT NULL,
  activity_id VARCHAR(255) DEFAULT NULL,
  activity_name VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  deleted INT NOT NULL,
  end_date_time DATETIME NOT NULL,
  introduction_info VARCHAR(255) NOT NULL,
  others_info VARCHAR(255) DEFAULT NULL,
  pic_url VARCHAR(255) DEFAULT NULL,
  start_date_time DATETIME NOT NULL,
  status VARCHAR(255) DEFAULT NULL,
  type VARCHAR(255) DEFAULT NULL,
  update_by VA<PERSON>HAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: activity_round_info
DROP TABLE IF EXISTS activity_round_info;
CREATE TABLE activity_round_info (
  id BIGINT NOT NULL,
  act_round_end_date_time DATETIME NOT NULL,
  act_round_id VARCHAR(255) DEFAULT NULL,
  act_round_info VARCHAR(255) DEFAULT NULL,
  act_round_max_submit_num INT NOT NULL,
  act_round_start_date_time DATETIME NOT NULL,
  act_round_submit_end_date_time DATETIME NOT NULL,
  act_round_submit_number INT DEFAULT NULL,
  act_round_submit_start_date_time DATETIME NOT NULL,
  activity_id VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  deleted INT NOT NULL,
  other_info VARCHAR(255) DEFAULT NULL,
  status VARCHAR(255) DEFAULT NULL,
  type VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: activity_submit_customer
DROP TABLE IF EXISTS activity_submit_customer;
CREATE TABLE activity_submit_customer (
  id BIGINT NOT NULL,
  act_round_id VARCHAR(255) DEFAULT NULL,
  age INT NOT NULL,
  check_in_date_time DATETIME DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  gender INT NOT NULL,
  pass_string VARCHAR(255) DEFAULT NULL,
  pass_type VARCHAR(255) NOT NULL,
  phone_string VARCHAR(255) DEFAULT NULL,
  status VARCHAR(255) DEFAULT NULL,
  submit_id VARCHAR(255) DEFAULT NULL,
  type VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  username VARCHAR(255) DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
);

-- 表: app_activity
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT NOT NULL,
  address VARCHAR(255) DEFAULT NULL,
  content VARCHAR(255) DEFAULT NULL,
  conver_picture VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  end_time DATETIME DEFAULT NULL,
  sort INT DEFAULT NULL,
  start_time DATETIME DEFAULT NULL,
  status INT DEFAULT NULL,
  title VARCHAR(255) DEFAULT NULL,
  type TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_activity_entry_rule
DROP TABLE IF EXISTS app_activity_entry_rule;
CREATE TABLE app_activity_entry_rule (
  id BIGINT NOT NULL,
  activity_id BIGINT DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  entry_end_time DATETIME DEFAULT NULL,
  entry_limit INT DEFAULT NULL,
  entry_start_time DATETIME DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_activity_entry_user
DROP TABLE IF EXISTS app_activity_entry_user;
CREATE TABLE app_activity_entry_user (
  id BIGINT NOT NULL,
  activity_id BIGINT DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  ext_attr VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  user_gender VARCHAR(255) DEFAULT NULL,
  user_idcard VARCHAR(255) DEFAULT NULL,
  user_idcard_type TINYINT DEFAULT NULL,
  user_name VARCHAR(255) DEFAULT NULL,
  user_phone VARCHAR(255) DEFAULT NULL,
  user_unionid VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_analysis
DROP TABLE IF EXISTS app_appointment_analysis;
CREATE TABLE app_appointment_analysis (
  id BIGINT NOT NULL,
  analysis_date VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  item_checkin_am INT DEFAULT NULL,
  item_checkin_pm INT DEFAULT NULL,
  item_checkin_total INT DEFAULT NULL,
  item_reserve_am INT DEFAULT NULL,
  item_reserve_pm INT DEFAULT NULL,
  item_reserve_refund_active INT DEFAULT NULL,
  item_reserve_refund_passive INT DEFAULT NULL,
  item_reserve_total INT DEFAULT NULL,
  ticket_checkin_am INT DEFAULT NULL,
  ticket_checkin_pm INT DEFAULT NULL,
  ticket_checkin_total INT DEFAULT NULL,
  ticket_reserve_am INT DEFAULT NULL,
  ticket_reserve_pm INT DEFAULT NULL,
  ticket_reserve_refund_active INT DEFAULT NULL,
  ticket_reserve_refund_passive INT DEFAULT NULL,
  ticket_reserve_total INT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_instructions
DROP TABLE IF EXISTS app_appointment_instructions;
CREATE TABLE app_appointment_instructions (
  id BIGINT NOT NULL,
  admission_notice VARCHAR(255) DEFAULT NULL,
  audience_notice VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  visiting_instructions VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_item_order
DROP TABLE IF EXISTS app_appointment_item_order;
CREATE TABLE app_appointment_item_order (
  id BIGINT NOT NULL,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  item VARCHAR(255) DEFAULT NULL,
  order_category TINYINT DEFAULT NULL,
  order_no VARCHAR(255) DEFAULT NULL,
  order_status SMALLINT DEFAULT NULL,
  owner_name VARCHAR(255) DEFAULT NULL,
  owner_phone VARCHAR(255) DEFAULT NULL,
  owner_unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_item_suborder
DROP TABLE IF EXISTS app_appointment_item_suborder;
CREATE TABLE app_appointment_item_suborder (
  id BIGINT NOT NULL,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  contacts_idcard_category TINYINT DEFAULT NULL,
  contacts_idcard_no VARCHAR(255) DEFAULT NULL,
  contacts_name VARCHAR(255) DEFAULT NULL,
  contacts_phone VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  onwer_unionid VARCHAR(255) DEFAULT NULL,
  order_no VARCHAR(255) DEFAULT NULL,
  seat_no TINYINT DEFAULT NULL,
  suborder_no VARCHAR(255) DEFAULT NULL,
  suborder_status SMALLINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_offline
DROP TABLE IF EXISTS app_appointment_offline;
CREATE TABLE app_appointment_offline (
  id BIGINT NOT NULL,
  appoint_batch_end_time VARCHAR(255) DEFAULT NULL,
  appoint_batch_no VARCHAR(255) DEFAULT NULL,
  appoint_batch_start_time VARCHAR(255) DEFAULT NULL,
  appoint_date VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  persons_num INT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  visit_info VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_order
DROP TABLE IF EXISTS app_appointment_order;
CREATE TABLE app_appointment_order (
  id BIGINT NOT NULL,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  order_category TINYINT DEFAULT NULL,
  order_no VARCHAR(255) DEFAULT NULL,
  order_remark VARCHAR(255) DEFAULT NULL,
  order_status SMALLINT DEFAULT NULL,
  owner_name VARCHAR(255) DEFAULT NULL,
  owner_phone VARCHAR(255) DEFAULT NULL,
  owner_unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_order_temporary_exhibition
DROP TABLE IF EXISTS app_appointment_order_temporary_exhibition;
CREATE TABLE app_appointment_order_temporary_exhibition (
  id BIGINT NOT NULL,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  exhibition_no VARCHAR(255) DEFAULT NULL,
  exhibition_title VARCHAR(255) DEFAULT NULL,
  order_category TINYINT DEFAULT NULL,
  order_no VARCHAR(255) DEFAULT NULL,
  order_status SMALLINT DEFAULT NULL,
  owner_name VARCHAR(255) DEFAULT NULL,
  owner_phone VARCHAR(255) DEFAULT NULL,
  owner_unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_personal_offline
DROP TABLE IF EXISTS app_appointment_personal_offline;
CREATE TABLE app_appointment_personal_offline (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  person_num INT DEFAULT NULL,
  remark VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  visit_date DATE DEFAULT NULL,
  visit_fypd_batch INT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_suborder
DROP TABLE IF EXISTS app_appointment_suborder;
CREATE TABLE app_appointment_suborder (
  id BIGINT NOT NULL,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  contacts_idcard_category TINYINT DEFAULT NULL,
  contacts_idcard_no VARCHAR(255) DEFAULT NULL,
  contacts_name VARCHAR(255) DEFAULT NULL,
  contacts_phone VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  onwer_unionid VARCHAR(255) DEFAULT NULL,
  order_no VARCHAR(255) DEFAULT NULL,
  suborder_no VARCHAR(255) DEFAULT NULL,
  suborder_status SMALLINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_suborder_temporary_exhibition
DROP TABLE IF EXISTS app_appointment_suborder_temporary_exhibition;
CREATE TABLE app_appointment_suborder_temporary_exhibition (
  id BIGINT NOT NULL,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  contacts_idcard_category TINYINT DEFAULT NULL,
  contacts_idcard_no VARCHAR(255) DEFAULT NULL,
  contacts_name VARCHAR(255) DEFAULT NULL,
  contacts_phone VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  onwer_unionid VARCHAR(255) DEFAULT NULL,
  order_no VARCHAR(255) DEFAULT NULL,
  suborder_no VARCHAR(255) DEFAULT NULL,
  suborder_status SMALLINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_team_offline
DROP TABLE IF EXISTS app_appointment_team_offline;
CREATE TABLE app_appointment_team_offline (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  offline_info BLOB,
  order_no VARCHAR(255) DEFAULT NULL,
  ower_unit VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  visit_date DATE DEFAULT NULL,
  visitors_num INT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_team_order
DROP TABLE IF EXISTS app_appointment_team_order;
CREATE TABLE app_appointment_team_order (
  id BIGINT NOT NULL,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  exhibition_no VARCHAR(255) DEFAULT NULL,
  method VARCHAR(255) DEFAULT NULL,
  order_category TINYINT DEFAULT NULL,
  order_no VARCHAR(255) DEFAULT NULL,
  order_remark VARCHAR(255) DEFAULT NULL,
  order_status SMALLINT DEFAULT NULL,
  ower_unit VARCHAR(255) DEFAULT NULL,
  ower_unit_code VARCHAR(255) DEFAULT NULL,
  owner_name VARCHAR(255) DEFAULT NULL,
  owner_phone VARCHAR(255) DEFAULT NULL,
  owner_unionid VARCHAR(255) DEFAULT NULL,
  supply_info VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  visitors_num INT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_batch
DROP TABLE IF EXISTS app_batch;
CREATE TABLE app_batch (
  id BIGINT NOT NULL,
  batch_category TINYINT DEFAULT NULL,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_remark VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  batch_status TINYINT DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  ticket_remaining INT DEFAULT NULL,
  ticket_total INT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_batch_set
DROP TABLE IF EXISTS app_batch_set;
CREATE TABLE app_batch_set (
  id BIGINT NOT NULL,
  batch_category TINYINT DEFAULT NULL,
  batch_effect_end_time DATETIME DEFAULT NULL,
  batch_effect_start_time DATETIME DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  status TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_batch_set_detail
DROP TABLE IF EXISTS app_batch_set_detail;
CREATE TABLE app_batch_set_detail (
  id BIGINT NOT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_set_id BIGINT DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  batch_ticket_total INT DEFAULT NULL,
  batch_type TINYINT DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  range_weeks VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_comments
DROP TABLE IF EXISTS app_comments;
CREATE TABLE app_comments (
  id BIGINT NOT NULL,
  content VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  customer VARCHAR(255) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  purpose VARCHAR(255) DEFAULT NULL,
  status VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  visit_time VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_config
DROP TABLE IF EXISTS app_config;
CREATE TABLE app_config (
  id BIGINT NOT NULL,
  group_no VARCHAR(255) DEFAULT NULL,
  rule_name VARCHAR(255) DEFAULT NULL,
  rule_value VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_customer
DROP TABLE IF EXISTS app_customer;
CREATE TABLE app_customer (
  id BIGINT NOT NULL,
  breaked_num INT DEFAULT NULL,
  breaked_total_num INT DEFAULT NULL,
  card_category TINYINT DEFAULT NULL,
  card_no VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  customer_id VARCHAR(255) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  job VARCHAR(255) DEFAULT NULL,
  mini_openid VARCHAR(255) DEFAULT NULL,
  phone VARCHAR(255) DEFAULT NULL,
  real_name VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  user_avatar_src VARCHAR(255) DEFAULT NULL,
  user_birthdate VARCHAR(255) DEFAULT NULL,
  user_gender TINYINT DEFAULT NULL,
  user_name VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_customer_contacts
DROP TABLE IF EXISTS app_customer_contacts;
CREATE TABLE app_customer_contacts (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  idcard_category TINYINT DEFAULT NULL,
  idcard_no VARCHAR(255) DEFAULT NULL,
  name VARCHAR(255) DEFAULT NULL,
  ower_unionid VARCHAR(255) DEFAULT NULL,
  phone VARCHAR(255) DEFAULT NULL,
  remark VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_manage_role
DROP TABLE IF EXISTS app_manage_role;
CREATE TABLE app_manage_role (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  menu_code VARCHAR(255) DEFAULT NULL,
  role_code INT DEFAULT NULL,
  role_name VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_manage_user
DROP TABLE IF EXISTS app_manage_user;
CREATE TABLE app_manage_user (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  menu_code VARCHAR(255) DEFAULT NULL,
  mobile VARCHAR(255) DEFAULT NULL,
  name VARCHAR(255) DEFAULT NULL,
  open_id VARCHAR(255) DEFAULT NULL,
  remark VARCHAR(255) DEFAULT NULL,
  role_code BIGINT DEFAULT NULL,
  status TINYINT DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_media
DROP TABLE IF EXISTS app_media;
CREATE TABLE app_media (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  md5_sum VARCHAR(255) DEFAULT NULL,
  media_name VARCHAR(255) DEFAULT NULL,
  media_showname VARCHAR(255) DEFAULT NULL,
  media_size BIGINT DEFAULT NULL,
  media_src VARCHAR(255) DEFAULT NULL,
  media_status TINYINT DEFAULT NULL,
  media_type VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_operate_log
DROP TABLE IF EXISTS app_operate_log;
CREATE TABLE app_operate_log (
  id BIGINT NOT NULL,
  operate_params VARCHAR(255) DEFAULT NULL,
  operate_time DATETIME DEFAULT NULL,
  operate_url VARCHAR(255) DEFAULT NULL,
  operator VARCHAR(255) DEFAULT NULL,
  operator_browser VARCHAR(255) DEFAULT NULL,
  operator_ip VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_surrounding_goods
DROP TABLE IF EXISTS app_surrounding_goods;
CREATE TABLE app_surrounding_goods (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  goods_category VARCHAR(255) DEFAULT NULL,
  goods_conver_picture VARCHAR(255) DEFAULT NULL,
  goods_introduce VARCHAR(255) DEFAULT NULL,
  goods_name VARCHAR(255) DEFAULT NULL,
  goods_no VARCHAR(255) DEFAULT NULL,
  goods_price DOUBLE DEFAULT NULL,
  goods_status TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_temporary_exhibition
DROP TABLE IF EXISTS app_temporary_exhibition;
CREATE TABLE app_temporary_exhibition (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  detail_banner_pic VARCHAR(255) DEFAULT NULL,
  exhibition_address VARCHAR(255) DEFAULT NULL,
  exhibition_content VARCHAR(255) DEFAULT NULL,
  exhibition_end_date DATE DEFAULT NULL,
  exhibition_no VARCHAR(255) DEFAULT NULL,
  exhibition_start_date DATE DEFAULT NULL,
  exhibition_title VARCHAR(255) DEFAULT NULL,
  status TINYINT DEFAULT NULL,
  thumbnail_pic VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_visit_guide
DROP TABLE IF EXISTS app_visit_guide;
CREATE TABLE app_visit_guide (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  page_views INT DEFAULT NULL,
  show_imgs VARCHAR(255) DEFAULT NULL,
  show_voices VARCHAR(255) DEFAULT NULL,
  sort_code INT DEFAULT NULL,
  spot_area VARCHAR(255) DEFAULT NULL,
  spot_content VARCHAR(255) DEFAULT NULL,
  spot_name VARCHAR(255) DEFAULT NULL,
  tips VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_workday
DROP TABLE IF EXISTS app_workday;
CREATE TABLE app_workday (
  id BIGINT NOT NULL,
  config VARCHAR(5000) DEFAULT NULL,
  day VARCHAR(255) DEFAULT NULL,
  day_remark VARCHAR(255) DEFAULT NULL,
  is_workday INT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_workday_temporary_exhibition
DROP TABLE IF EXISTS app_workday_temporary_exhibition;
CREATE TABLE app_workday_temporary_exhibition (
  id BIGINT NOT NULL,
  day VARCHAR(255) DEFAULT NULL,
  day_remark VARCHAR(255) DEFAULT NULL,
  is_workday TINYINT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: art_center_info
DROP TABLE IF EXISTS art_center_info;
CREATE TABLE art_center_info (
  id BIGINT NOT NULL,
  address VARCHAR(255) DEFAULT NULL,
  introduction VARCHAR(2000) DEFAULT NULL,
  metro VARCHAR(255) DEFAULT NULL,
  open_time VARCHAR(255) DEFAULT NULL,
  pic_info VARCHAR(255) DEFAULT NULL,
  "section" VARCHAR(255) DEFAULT NULL,
  traffic VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: black_list
DROP TABLE IF EXISTS black_list;
CREATE TABLE black_list (
  id BIGINT NOT NULL,
  category VARCHAR(255) DEFAULT NULL,
  locking_date_time DATETIME DEFAULT NULL,
  name VARCHAR(255) DEFAULT NULL,
  status VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  unlocking_date_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: comm_questionnaire
DROP TABLE IF EXISTS comm_questionnaire;
CREATE TABLE comm_questionnaire (
  id BIGINT NOT NULL,
  content VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  options VARCHAR(255) DEFAULT NULL,
  title VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  valid_end_date DATETIME DEFAULT NULL,
  valid_start_date DATETIME DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
);

-- 表: comm_questionnaire_answer
DROP TABLE IF EXISTS comm_questionnaire_answer;
CREATE TABLE comm_questionnaire_answer (
  id BIGINT NOT NULL,
  answer VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  questionnaire_id BIGINT DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: exhibition_info
DROP TABLE IF EXISTS exhibition_info;
CREATE TABLE exhibition_info (
  id BIGINT NOT NULL,
  exhibition_id VARCHAR(255) DEFAULT NULL,
  exhibition_status VARCHAR(255) DEFAULT NULL,
  exhibition_type VARCHAR(255) DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
);

-- 表: hibernate_sequence
DROP TABLE IF EXISTS hibernate_sequence;
CREATE TABLE hibernate_sequence (
  next_val BIGINT DEFAULT NULL
);

-- 表: mp_datacube_everday
DROP TABLE IF EXISTS mp_datacube_everday;
CREATE TABLE mp_datacube_everday (
  id BIGINT NOT NULL,
  analysis_date DATE DEFAULT NULL,
  article_read_total INT DEFAULT NULL,
  article_summary_json VARCHAR(255) DEFAULT NULL,
  article_total INT DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  user_cumulate INT DEFAULT NULL,
  user_cumulate_json VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: mp_datacube_everday_article_summary
DROP TABLE IF EXISTS mp_datacube_everday_article_summary;
CREATE TABLE mp_datacube_everday_article_summary (
  id BIGINT NOT NULL,
  add_to_fav_count INT DEFAULT NULL,
  add_to_fav_user INT DEFAULT NULL,
  analysis_date DATE DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  msg_id VARCHAR(255) DEFAULT NULL,
  send_date DATE DEFAULT NULL,
  title VARCHAR(255) DEFAULT NULL,
  total_read_count INT DEFAULT NULL,
  total_read_user INT DEFAULT NULL,
  total_share_count INT DEFAULT NULL,
  total_share_user INT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: mp_datacube_everday_user_summary
DROP TABLE IF EXISTS mp_datacube_everday_user_summary;
CREATE TABLE mp_datacube_everday_user_summary (
  id BIGINT NOT NULL,
  analysis_date DATE DEFAULT NULL,
  article_summary_json VARCHAR(255) DEFAULT NULL,
  cancel_user INT DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  new_user INT DEFAULT NULL,
  total_visits BIGINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  user_cumulate INT DEFAULT NULL,
  visits BIGINT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: one_time_tasks
DROP TABLE IF EXISTS one_time_tasks;
CREATE TABLE one_time_tasks (
  id VARCHAR(255) NOT NULL,
  created_at DATETIME DEFAULT NULL,
  deleted INT DEFAULT NULL,
  execute_time DATETIME DEFAULT NULL,
  status VARCHAR(255) DEFAULT NULL,
  task_class VARCHAR(255) DEFAULT NULL,
  task_data VARCHAR(255) DEFAULT NULL,
  task_type VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: round_config
DROP TABLE IF EXISTS round_config;
CREATE TABLE round_config (
  id BIGINT NOT NULL,
  end_time TIME DEFAULT NULL,
  exhibition_id VARCHAR(255) DEFAULT NULL,
  round_date DATE DEFAULT NULL,
  round_id VARCHAR(255) DEFAULT NULL,
  round_status VARCHAR(255) DEFAULT NULL,
  start_time TIME DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
);

-- 表: volunteer_info
DROP TABLE IF EXISTS volunteer_info;
CREATE TABLE volunteer_info (
  id BIGINT NOT NULL,
  age INT DEFAULT NULL,
  area VARCHAR(255) DEFAULT NULL,
  category INT DEFAULT NULL,
  email VARCHAR(255) DEFAULT NULL,
  fixed_tel VARCHAR(255) DEFAULT NULL,
  gender VARCHAR(255) DEFAULT NULL,
  name VARCHAR(255) DEFAULT NULL,
  phone VARCHAR(255) DEFAULT NULL,
  service_duration DOUBLE DEFAULT NULL,
  skill VARCHAR(255) DEFAULT NULL,
  uid VARCHAR(255) DEFAULT NULL,
  unit VARCHAR(255) DEFAULT NULL,
  volunteer_id VARCHAR(255) DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
);

