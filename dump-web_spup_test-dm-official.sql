-- 达梦数据库 DM_SQL 兼容脚本
-- 基于达梦数据库官方语法规范转换
-- 参考文档: https://eco.dameng.com/document/dm/zh-cn/pm/dm_sql-introduction.html

-- 设置达梦数据库参数
-- SET IDENTITY_INSERT OFF;

-- 表: activity_info
DROP TABLE IF EXISTS activity_info;
CREATE TABLE activity_info (
  id BIGINT NOT NULL,
  activity_id VARCHAR(255) DEFAULT NULL,
  activity_name VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  deleted INT NOT NULL,
  end_date_time DATETIME NOT NULL,
  introduction_info VARCHAR(255) NOT NULL,
  others_info VARCHAR(255) DEFAULT NULL,
  pic_url VARCHAR(255) DEFAULT NULL,
  start_date_time DATETIME NOT NULL,
  status VARCHAR(255) DEFAULT NULL,
  type VA<PERSON>HAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: activity_round_info
DROP TABLE IF EXISTS activity_round_info;
CREATE TABLE activity_round_info (
  id BIGINT NOT NULL,
  act_round_end_date_time DATETIME NOT NULL,
  act_round_id VARCHAR(255) DEFAULT NULL,
  act_round_info VARCHAR(255) DEFAULT NULL,
  act_round_max_submit_num INT NOT NULL,
  act_round_start_date_time DATETIME NOT NULL,
  act_round_submit_end_date_time DATETIME NOT NULL,
  act_round_submit_number INT DEFAULT NULL,
  act_round_submit_start_date_time DATETIME NOT NULL,
  activity_id VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  deleted INT NOT NULL,
  other_info VARCHAR(255) DEFAULT NULL,
  status VARCHAR(255) DEFAULT NULL,
  type VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: activity_submit_customer
DROP TABLE IF EXISTS activity_submit_customer;
CREATE TABLE activity_submit_customer (
  id BIGINT NOT NULL,
  act_round_id VARCHAR(255) DEFAULT NULL,
  age INT NOT NULL,
  check_in_date_time DATETIME DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  gender INT NOT NULL,
  pass_string VARCHAR(255) DEFAULT NULL,
  pass_type VARCHAR(255) NOT NULL,
  phone_string VARCHAR(255) DEFAULT NULL,
  status VARCHAR(255) DEFAULT NULL,
  submit_id VARCHAR(255) DEFAULT NULL,
  type VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  username VARCHAR(255) DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
);

-- 表: app_activity
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  address VARCHAR(255) DEFAULT NULL -- 活动地点,
  content VARCHAR(255) DEFAULT NULL -- 活动内容,
  conver_picture VARCHAR(255) DEFAULT NULL -- 活动封面图,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  end_time DATETIME DEFAULT NULL -- 活动结束时间,
  sort INT DEFAULT NULL -- 排序值,
  start_time DATETIME DEFAULT NULL -- 活动开始时间,
  status INT DEFAULT NULL -- 活动状态,
  title VARCHAR(255) DEFAULT NULL -- 活动标题,
  type TINYINT DEFAULT NULL -- 活动类型,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_activity_entry_rule
DROP TABLE IF EXISTS app_activity_entry_rule;
CREATE TABLE app_activity_entry_rule (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  activity_id BIGINT DEFAULT NULL -- 活动id,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  entry_end_time DATETIME DEFAULT NULL,
  entry_limit INT DEFAULT NULL -- 报名人数限制,
  entry_start_time DATETIME DEFAULT NULL -- 报名开始时间,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_activity_entry_user
DROP TABLE IF EXISTS app_activity_entry_user;
CREATE TABLE app_activity_entry_user (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  activity_id BIGINT DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL -- 前端表，unionid,
  create_time DATETIME DEFAULT NULL -- 创建时间,
  deleted TINYINT DEFAULT NULL,
  ext_attr VARCHAR(255) DEFAULT NULL -- 扩展字段,
  update_by VARCHAR(255) DEFAULT NULL -- 前端表，unionid,
  update_time DATETIME DEFAULT NULL -- 修改时间,
  user_gender VARCHAR(255) DEFAULT NULL -- 用户性别,
  user_idcard VARCHAR(255) DEFAULT NULL -- 用户证件号,
  user_idcard_type TINYINT DEFAULT NULL -- 用户证件类型,
  user_name VARCHAR(255) DEFAULT NULL -- 用户姓名,
  user_phone VARCHAR(255) DEFAULT NULL -- 用户手机号,
  user_unionid VARCHAR(255) DEFAULT NULL -- 用户unionid,
  PRIMARY KEY (id)
);

-- 表: app_appointment_analysis
DROP TABLE IF EXISTS app_appointment_analysis;
CREATE TABLE app_appointment_analysis (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  analysis_date VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL -- 创建者unionid,
  create_time DATETIME DEFAULT NULL -- 创建时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  item_checkin_am INT DEFAULT NULL -- 飞阅浦东上午核销数,
  item_checkin_pm INT DEFAULT NULL -- 飞阅浦东下午核销数,
  item_checkin_total INT DEFAULT NULL -- 飞阅浦东核销数,
  item_reserve_am INT DEFAULT NULL -- 飞阅浦东上午预约数,
  item_reserve_pm INT DEFAULT NULL -- 飞阅浦东下午预约数,
  item_reserve_refund_active INT DEFAULT NULL -- 飞阅浦东主动退票数,
  item_reserve_refund_passive INT DEFAULT NULL -- 飞阅浦东被动退票数,
  item_reserve_total INT DEFAULT NULL -- 飞阅浦东展现预约总数,
  ticket_checkin_am INT DEFAULT NULL -- 上午核销数,
  ticket_checkin_pm INT DEFAULT NULL -- 下午核销数,
  ticket_checkin_total INT DEFAULT NULL -- 门票核销总数,
  ticket_reserve_am INT DEFAULT NULL -- 上午预约数,
  ticket_reserve_pm INT DEFAULT NULL -- 下午预约数,
  ticket_reserve_refund_active INT DEFAULT NULL -- 主动退票数,
  ticket_reserve_refund_passive INT DEFAULT NULL -- 被动退票数,
  ticket_reserve_total INT DEFAULT NULL -- 门票预约总数,
  update_by VARCHAR(255) DEFAULT NULL -- 更新者unionid,
  update_time DATETIME DEFAULT NULL -- 数据更新时间,
  PRIMARY KEY (id)
);

-- 表: app_appointment_instructions
DROP TABLE IF EXISTS app_appointment_instructions;
CREATE TABLE app_appointment_instructions (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  admission_notice VARCHAR(255) DEFAULT NULL -- 入馆须知,
  audience_notice VARCHAR(255) DEFAULT NULL -- 观众须知,
  create_by VARCHAR(255) DEFAULT NULL -- 创建者unionid,
  create_time DATETIME DEFAULT NULL -- 生成时间,
  update_by VARCHAR(255) DEFAULT NULL -- 更新者unionid,
  update_time DATETIME DEFAULT NULL -- 数据更新时间,
  visiting_instructions VARCHAR(255) DEFAULT NULL -- 参观须知,
  PRIMARY KEY (id)
);

-- 表: app_appointment_item_order
DROP TABLE IF EXISTS app_appointment_item_order;
CREATE TABLE app_appointment_item_order (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  batch_date VARCHAR(255) DEFAULT NULL -- 场次日期,
  batch_end_time VARCHAR(255) DEFAULT NULL -- 场次结束时间,
  batch_no VARCHAR(255) DEFAULT NULL -- 场次编号,
  batch_start_time VARCHAR(255) DEFAULT NULL -- 场次开始时间,
  create_by VARCHAR(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  create_time DATETIME DEFAULT NULL -- 订单生成时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  item VARCHAR(255) DEFAULT NULL,
  order_category TINYINT DEFAULT NULL -- 订单分类，可能会有团体订单等其他的,
  order_no VARCHAR(255) DEFAULT NULL -- 订单编号,
  order_status SMALLINT DEFAULT NULL -- 订单状态,
  owner_name VARCHAR(255) DEFAULT NULL -- 订单所有者姓名,
  owner_phone VARCHAR(255) DEFAULT NULL -- 订单所有者手机号,
  owner_unionid VARCHAR(255) DEFAULT NULL -- 用户小程序唯一id,
  update_by VARCHAR(255) DEFAULT NULL -- 更新者unionid,
  update_time DATETIME DEFAULT NULL -- 数据更新时间,
  PRIMARY KEY (id)
);

-- 表: app_appointment_item_suborder
DROP TABLE IF EXISTS app_appointment_item_suborder;
CREATE TABLE app_appointment_item_suborder (
  id BIGINT NOT NULL -- 子订单自增id -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  contacts_idcard_category TINYINT DEFAULT NULL,
  contacts_idcard_no VARCHAR(255) DEFAULT NULL,
  contacts_name VARCHAR(255) DEFAULT NULL,
  contacts_phone VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  onwer_unionid VARCHAR(255) DEFAULT NULL -- 用户小程序唯一id,
  order_no VARCHAR(255) DEFAULT NULL -- 订单编号,
  seat_no TINYINT DEFAULT NULL,
  suborder_no VARCHAR(255) DEFAULT NULL -- 子订单编号,
  suborder_status SMALLINT DEFAULT NULL -- 子订单状态-无符号int，状态可以为负数，表明异常状态；1：待支付，2: 待服务，4: 服务中，8: 已完成。 -1：售后中； -2: 已退款； -4: 已取消；,
  update_by VARCHAR(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_offline
DROP TABLE IF EXISTS app_appointment_offline;
CREATE TABLE app_appointment_offline (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  appoint_batch_end_time VARCHAR(255) DEFAULT NULL,
  appoint_batch_no VARCHAR(255) DEFAULT NULL,
  appoint_batch_start_time VARCHAR(255) DEFAULT NULL,
  appoint_date VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  persons_num INT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  visit_info VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_order
DROP TABLE IF EXISTS app_appointment_order;
CREATE TABLE app_appointment_order (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  batch_date VARCHAR(255) DEFAULT NULL -- 场次日期,
  batch_end_time VARCHAR(255) DEFAULT NULL -- 场次结束时间,
  batch_no VARCHAR(255) DEFAULT NULL -- 场次编号,
  batch_start_time VARCHAR(255) DEFAULT NULL -- 场次开始时间,
  create_by VARCHAR(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  create_time DATETIME DEFAULT NULL -- 订单生成时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  order_category TINYINT DEFAULT NULL -- 订单分类，可能会有团体订单等其他的,
  order_no VARCHAR(255) DEFAULT NULL -- 订单编号,
  order_remark VARCHAR(255) DEFAULT NULL,
  order_status SMALLINT DEFAULT NULL -- 订单状态,
  owner_name VARCHAR(255) DEFAULT NULL -- 订单所有者姓名,
  owner_phone VARCHAR(255) DEFAULT NULL -- 订单所有者手机号,
  owner_unionid VARCHAR(255) DEFAULT NULL -- 用户小程序唯一id,
  update_by VARCHAR(255) DEFAULT NULL -- 更新者unionid,
  update_time DATETIME DEFAULT NULL -- 数据更新时间,
  PRIMARY KEY (id)
);

-- 表: app_appointment_order_temporary_exhibition
DROP TABLE IF EXISTS app_appointment_order_temporary_exhibition;
CREATE TABLE app_appointment_order_temporary_exhibition (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  exhibition_no VARCHAR(255) DEFAULT NULL,
  exhibition_title VARCHAR(255) DEFAULT NULL,
  order_category TINYINT DEFAULT NULL,
  order_no VARCHAR(255) DEFAULT NULL,
  order_status SMALLINT DEFAULT NULL,
  owner_name VARCHAR(255) DEFAULT NULL,
  owner_phone VARCHAR(255) DEFAULT NULL,
  owner_unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_personal_offline
DROP TABLE IF EXISTS app_appointment_personal_offline;
CREATE TABLE app_appointment_personal_offline (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  person_num INT DEFAULT NULL,
  remark VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  visit_date DATE DEFAULT NULL,
  visit_fypd_batch INT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_suborder
DROP TABLE IF EXISTS app_appointment_suborder;
CREATE TABLE app_appointment_suborder (
  id BIGINT NOT NULL -- 子订单自增id -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  contacts_idcard_category TINYINT DEFAULT NULL,
  contacts_idcard_no VARCHAR(255) DEFAULT NULL,
  contacts_name VARCHAR(255) DEFAULT NULL,
  contacts_phone VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  onwer_unionid VARCHAR(255) DEFAULT NULL -- 用户小程序唯一id,
  order_no VARCHAR(255) DEFAULT NULL -- 订单编号,
  suborder_no VARCHAR(255) DEFAULT NULL -- 子订单编号,
  suborder_status SMALLINT DEFAULT NULL -- 子订单状态-无符号int，状态可以为负数，表明异常状态；1：待支付，2: 待服务，4: 服务中，8: 已完成。 -1：售后中； -2: 已退款； -4: 已取消；,
  update_by VARCHAR(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_suborder_temporary_exhibition
DROP TABLE IF EXISTS app_appointment_suborder_temporary_exhibition;
CREATE TABLE app_appointment_suborder_temporary_exhibition (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  contacts_idcard_category TINYINT DEFAULT NULL,
  contacts_idcard_no VARCHAR(255) DEFAULT NULL,
  contacts_name VARCHAR(255) DEFAULT NULL,
  contacts_phone VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  onwer_unionid VARCHAR(255) DEFAULT NULL,
  order_no VARCHAR(255) DEFAULT NULL,
  suborder_no VARCHAR(255) DEFAULT NULL,
  suborder_status SMALLINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_team_offline
DROP TABLE IF EXISTS app_appointment_team_offline;
CREATE TABLE app_appointment_team_offline (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  offline_info BLOB,
  order_no VARCHAR(255) DEFAULT NULL,
  ower_unit VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  visit_date DATE DEFAULT NULL,
  visitors_num INT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_appointment_team_order
DROP TABLE IF EXISTS app_appointment_team_order;
CREATE TABLE app_appointment_team_order (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  batch_date VARCHAR(255) DEFAULT NULL -- 场次日期,
  batch_end_time VARCHAR(255) DEFAULT NULL -- 场次结束时间,
  batch_no VARCHAR(255) DEFAULT NULL -- 场次编号,
  batch_start_time VARCHAR(255) DEFAULT NULL -- 场次开始时间,
  create_by VARCHAR(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  create_time DATETIME DEFAULT NULL -- 订单生成时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  exhibition_no VARCHAR(255) DEFAULT NULL,
  method VARCHAR(255) DEFAULT NULL,
  order_category TINYINT DEFAULT NULL -- 订单分类，可能会有团体订单等其他的,
  order_no VARCHAR(255) DEFAULT NULL -- 订单编号,
  order_remark VARCHAR(255) DEFAULT NULL,
  order_status SMALLINT DEFAULT NULL -- 订单状态,
  ower_unit VARCHAR(255) DEFAULT NULL,
  ower_unit_code VARCHAR(255) DEFAULT NULL,
  owner_name VARCHAR(255) DEFAULT NULL -- 订单所有者姓名,
  owner_phone VARCHAR(255) DEFAULT NULL -- 订单所有者手机号,
  owner_unionid VARCHAR(255) DEFAULT NULL -- 用户小程序唯一id,
  supply_info VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL -- 更新者unionid,
  update_time DATETIME DEFAULT NULL -- 数据更新时间,
  visitors_num INT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_batch
DROP TABLE IF EXISTS app_batch;
CREATE TABLE app_batch (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  batch_category TINYINT DEFAULT NULL -- 场次分类,
  batch_date VARCHAR(255) DEFAULT NULL -- 场次日期,
  batch_end_time VARCHAR(255) DEFAULT NULL -- 场次结束时间,
  batch_no VARCHAR(255) DEFAULT NULL -- 场次编号,
  batch_remark VARCHAR(255) DEFAULT NULL -- 场次备注说明，便于后期管理,
  batch_start_time VARCHAR(255) DEFAULT NULL -- 场次开始时间,
  batch_status TINYINT DEFAULT NULL -- 场次状态,
  create_by VARCHAR(255) DEFAULT NULL -- 系统创建-给相关类名,
  create_time DATETIME DEFAULT NULL -- 记录创建时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  ticket_remaining INT DEFAULT NULL -- 剩余票数,
  ticket_total INT DEFAULT NULL -- 总计发放票数,
  update_by VARCHAR(255) DEFAULT NULL -- 创建者unionid-可为用户、管理员、系统等,
  update_time DATETIME DEFAULT NULL -- 记录修改时间,
  PRIMARY KEY (id)
);

-- 表: app_batch_set
DROP TABLE IF EXISTS app_batch_set;
CREATE TABLE app_batch_set (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  batch_category TINYINT DEFAULT NULL -- 场次类型,
  batch_effect_end_time DATETIME DEFAULT NULL -- 场次生效结束日期（空表示永久）,
  batch_effect_start_time DATETIME DEFAULT NULL -- 场次生效开始日期,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  status TINYINT DEFAULT NULL -- 规则当前状态,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_batch_set_detail
DROP TABLE IF EXISTS app_batch_set_detail;
CREATE TABLE app_batch_set_detail (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_set_id BIGINT DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  batch_ticket_total INT DEFAULT NULL,
  batch_type TINYINT DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  range_weeks VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_comments
DROP TABLE IF EXISTS app_comments;
CREATE TABLE app_comments (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  content VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL -- 创建者unionid-可为用户、店员、店长、系统等,
  create_time DATETIME DEFAULT NULL -- 订单生成时间,
  customer VARCHAR(255) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  purpose VARCHAR(255) DEFAULT NULL,
  status VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL -- 更新者unionid,
  update_time DATETIME DEFAULT NULL -- 数据更新时间,
  visit_time VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_config
DROP TABLE IF EXISTS app_config;
CREATE TABLE app_config (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  group_no VARCHAR(255) DEFAULT NULL,
  rule_name VARCHAR(255) DEFAULT NULL,
  rule_value VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_customer
DROP TABLE IF EXISTS app_customer;
CREATE TABLE app_customer (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  breaked_num INT DEFAULT NULL,
  breaked_total_num INT DEFAULT NULL,
  card_category TINYINT DEFAULT NULL,
  card_no VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL -- 系统创建-给相关类名,
  create_time DATETIME DEFAULT NULL -- 记录创建时间,
  customer_id VARCHAR(255) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  job VARCHAR(255) DEFAULT NULL,
  mini_openid VARCHAR(255) DEFAULT NULL -- 小程序openid,
  phone VARCHAR(255) DEFAULT NULL,
  real_name VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL -- 微信唯一识别码,
  update_by VARCHAR(255) DEFAULT NULL -- 创建者unionid-可为用户、管理员、系统等,
  update_time DATETIME DEFAULT NULL -- 记录修改时间,
  user_avatar_src VARCHAR(255) DEFAULT NULL -- 用户本地头像，可供用户替换头像；2作为备用,
  user_birthdate VARCHAR(255) DEFAULT NULL -- 用户生日,
  user_gender TINYINT DEFAULT NULL -- 用户性别： 1， 男， 2 女， 0 未知,
  user_name VARCHAR(255) DEFAULT NULL -- 用户姓名,
  PRIMARY KEY (id)
);

-- 表: app_customer_contacts
DROP TABLE IF EXISTS app_customer_contacts;
CREATE TABLE app_customer_contacts (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  create_by VARCHAR(255) DEFAULT NULL -- 系统创建-给相关类名,
  create_time DATETIME DEFAULT NULL -- 记录创建时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  idcard_category TINYINT DEFAULT NULL,
  idcard_no VARCHAR(255) DEFAULT NULL,
  name VARCHAR(255) DEFAULT NULL,
  ower_unionid VARCHAR(255) DEFAULT NULL,
  phone VARCHAR(255) DEFAULT NULL,
  remark VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL -- 创建者unionid-可为用户、管理员、系统等,
  update_time DATETIME DEFAULT NULL -- 记录修改时间,
  PRIMARY KEY (id)
);

-- 表: app_manage_role
DROP TABLE IF EXISTS app_manage_role;
CREATE TABLE app_manage_role (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  create_by VARCHAR(255) DEFAULT NULL -- 创建者open_id,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  menu_code VARCHAR(255) DEFAULT NULL,
  role_code INT DEFAULT NULL,
  role_name VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL -- 修改人open_id,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_manage_user
DROP TABLE IF EXISTS app_manage_user;
CREATE TABLE app_manage_user (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  create_by VARCHAR(255) DEFAULT NULL -- 系统创建-给相关类名,
  create_time DATETIME DEFAULT NULL -- 记录创建时间,
  deleted TINYINT DEFAULT NULL,
  menu_code VARCHAR(255) DEFAULT NULL -- 菜单，以逗号分隔,
  mobile VARCHAR(255) DEFAULT NULL -- 手机号,
  name VARCHAR(255) DEFAULT NULL -- 用户昵称,
  open_id VARCHAR(255) DEFAULT NULL -- 登录用户open_id,
  remark VARCHAR(255) DEFAULT NULL -- 描述,
  role_code BIGINT DEFAULT NULL -- 角色，可配置多个角色，采用位运算,
  status TINYINT DEFAULT NULL -- 状态：1启用；2禁用；4其他,
  unionid VARCHAR(255) DEFAULT NULL -- 登录用户统一识别码,
  update_by VARCHAR(255) DEFAULT NULL -- 创建者unionid-可为用户、管理员、系统等,
  update_time DATETIME DEFAULT NULL -- 记录修改时间,
  PRIMARY KEY (id)
);

-- 表: app_media
DROP TABLE IF EXISTS app_media;
CREATE TABLE app_media (
  id BIGINT NOT NULL -- 媒体内容自增id -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  create_by VARCHAR(255) DEFAULT NULL -- 创建者,
  create_time DATETIME DEFAULT NULL -- 记录创建时间,
  deleted TINYINT DEFAULT NULL -- 逻辑删除，默认为0未删除，1已删除,
  md5_sum VARCHAR(255) DEFAULT NULL -- 文件校验，避免重复存储。,
  media_name VARCHAR(255) DEFAULT NULL -- 媒体文件名（不带后缀名）,
  media_showname VARCHAR(255) DEFAULT NULL -- 显示名称，相同文件可显示名称不同,
  media_size BIGINT DEFAULT NULL -- 文件大小,
  media_src VARCHAR(255) DEFAULT NULL -- 文件存储路径,
  media_status TINYINT DEFAULT NULL -- 已删除，正常，已隐藏,
  media_type VARCHAR(255) DEFAULT NULL -- 媒体文件类型,
  update_by VARCHAR(255) DEFAULT NULL -- 修改人,
  update_time DATETIME DEFAULT NULL -- 记录修改时间,
  PRIMARY KEY (id)
);

-- 表: app_operate_log
DROP TABLE IF EXISTS app_operate_log;
CREATE TABLE app_operate_log (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  operate_params VARCHAR(255) DEFAULT NULL,
  operate_time DATETIME DEFAULT NULL,
  operate_url VARCHAR(255) DEFAULT NULL,
  operator VARCHAR(255) DEFAULT NULL,
  operator_browser VARCHAR(255) DEFAULT NULL,
  operator_ip VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_surrounding_goods
DROP TABLE IF EXISTS app_surrounding_goods;
CREATE TABLE app_surrounding_goods (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  goods_category VARCHAR(255) DEFAULT NULL -- 商品分类,
  goods_conver_picture VARCHAR(255) DEFAULT NULL,
  goods_introduce VARCHAR(255) DEFAULT NULL -- 商品介绍,
  goods_name VARCHAR(255) DEFAULT NULL -- 商品名称,
  goods_no VARCHAR(255) DEFAULT NULL -- 商品编号,
  goods_price DOUBLE DEFAULT NULL -- 商品价格,
  goods_status TINYINT DEFAULT NULL -- 商品状态：1上架；2下架；4其他,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_temporary_exhibition
DROP TABLE IF EXISTS app_temporary_exhibition;
CREATE TABLE app_temporary_exhibition (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  detail_banner_pic VARCHAR(255) DEFAULT NULL,
  exhibition_address VARCHAR(255) DEFAULT NULL,
  exhibition_content VARCHAR(255) DEFAULT NULL,
  exhibition_end_date DATE DEFAULT NULL,
  exhibition_no VARCHAR(255) DEFAULT NULL,
  exhibition_start_date DATE DEFAULT NULL,
  exhibition_title VARCHAR(255) DEFAULT NULL,
  status TINYINT DEFAULT NULL,
  thumbnail_pic VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_visit_guide
DROP TABLE IF EXISTS app_visit_guide;
CREATE TABLE app_visit_guide (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  page_views INT DEFAULT NULL,
  show_imgs VARCHAR(255) DEFAULT NULL,
  show_voices VARCHAR(255) DEFAULT NULL,
  sort_code INT DEFAULT NULL -- 排序值,
  spot_area VARCHAR(255) DEFAULT NULL -- 所在区域（楼层）,
  spot_content VARCHAR(255) DEFAULT NULL -- 展项介绍,
  spot_name VARCHAR(255) DEFAULT NULL -- 展项名称,
  tips VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_workday
DROP TABLE IF EXISTS app_workday;
CREATE TABLE app_workday (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  config VARCHAR(5000) DEFAULT NULL -- 工作日配置,
  day VARCHAR(255) DEFAULT NULL,
  day_remark VARCHAR(255) DEFAULT NULL,
  is_workday INT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: app_workday_temporary_exhibition
DROP TABLE IF EXISTS app_workday_temporary_exhibition;
CREATE TABLE app_workday_temporary_exhibition (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  day VARCHAR(255) DEFAULT NULL,
  day_remark VARCHAR(255) DEFAULT NULL,
  is_workday TINYINT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: art_center_info
DROP TABLE IF EXISTS art_center_info;
CREATE TABLE art_center_info (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  address VARCHAR(255) DEFAULT NULL,
  introduction VARCHAR(2000) DEFAULT NULL,
  metro VARCHAR(255) DEFAULT NULL,
  open_time VARCHAR(255) DEFAULT NULL,
  pic_info VARCHAR(255) DEFAULT NULL,
  section VARCHAR(255) DEFAULT NULL,
  traffic VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: black_list
DROP TABLE IF EXISTS black_list;
CREATE TABLE black_list (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  category VARCHAR(255) DEFAULT NULL,
  locking_date_time DATETIME DEFAULT NULL,
  name VARCHAR(255) DEFAULT NULL,
  status VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  unlocking_date_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: comm_questionnaire
DROP TABLE IF EXISTS comm_questionnaire;
CREATE TABLE comm_questionnaire (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  content VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  options VARCHAR(255) DEFAULT NULL,
  title VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  valid_end_date DATETIME DEFAULT NULL,
  valid_start_date DATETIME DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
);

-- 表: comm_questionnaire_answer
DROP TABLE IF EXISTS comm_questionnaire_answer;
CREATE TABLE comm_questionnaire_answer (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  answer VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  questionnaire_id BIGINT DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: exhibition_info
DROP TABLE IF EXISTS exhibition_info;
CREATE TABLE exhibition_info (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  exhibition_id VARCHAR(255) DEFAULT NULL,
  exhibition_status VARCHAR(255) DEFAULT NULL,
  exhibition_type VARCHAR(255) DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
);

-- 表: hibernate_sequence
DROP TABLE IF EXISTS hibernate_sequence;
CREATE TABLE hibernate_sequence (
  next_val BIGINT DEFAULT NULL
);

-- 表: mp_datacube_everday
DROP TABLE IF EXISTS mp_datacube_everday;
CREATE TABLE mp_datacube_everday (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  analysis_date DATE DEFAULT NULL,
  article_read_total INT DEFAULT NULL,
  article_summary_json VARCHAR(255) DEFAULT NULL -- 文章阅读总数,
  article_total INT DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  user_cumulate INT DEFAULT NULL,
  user_cumulate_json VARCHAR(255) DEFAULT NULL -- 用户总数,
  PRIMARY KEY (id)
);

-- 表: mp_datacube_everday_article_summary
DROP TABLE IF EXISTS mp_datacube_everday_article_summary;
CREATE TABLE mp_datacube_everday_article_summary (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  add_to_fav_count INT DEFAULT NULL,
  add_to_fav_user INT DEFAULT NULL,
  analysis_date DATE DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  msg_id VARCHAR(255) DEFAULT NULL,
  send_date DATE DEFAULT NULL,
  title VARCHAR(255) DEFAULT NULL,
  total_read_count INT DEFAULT NULL,
  total_read_user INT DEFAULT NULL,
  total_share_count INT DEFAULT NULL,
  total_share_user INT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: mp_datacube_everday_user_summary
DROP TABLE IF EXISTS mp_datacube_everday_user_summary;
CREATE TABLE mp_datacube_everday_user_summary (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  analysis_date DATE DEFAULT NULL,
  article_summary_json VARCHAR(255) DEFAULT NULL -- 文章阅读总数,
  cancel_user INT DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  new_user INT DEFAULT NULL,
  total_visits BIGINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  user_cumulate INT DEFAULT NULL,
  visits BIGINT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: one_time_tasks
DROP TABLE IF EXISTS one_time_tasks;
CREATE TABLE one_time_tasks (
  id VARCHAR(255) NOT NULL,
  created_at DATETIME DEFAULT NULL,
  deleted INT DEFAULT NULL,
  execute_time DATETIME DEFAULT NULL,
  status VARCHAR(255) DEFAULT NULL,
  task_class VARCHAR(255) DEFAULT NULL,
  task_data VARCHAR(255) DEFAULT NULL,
  task_type VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表: round_config
DROP TABLE IF EXISTS round_config;
CREATE TABLE round_config (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  end_time TIME DEFAULT NULL,
  exhibition_id VARCHAR(255) DEFAULT NULL,
  round_date DATE DEFAULT NULL,
  round_id VARCHAR(255) DEFAULT NULL,
  round_status VARCHAR(255) DEFAULT NULL,
  start_time TIME DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
);

-- 表: volunteer_info
DROP TABLE IF EXISTS volunteer_info;
CREATE TABLE volunteer_info (
  id BIGINT NOT NULL -- 注意: 原MySQL的AUTO_INCREMENT，建议创建序列实现自增,
  age INT DEFAULT NULL,
  area VARCHAR(255) DEFAULT NULL,
  category INT DEFAULT NULL,
  email VARCHAR(255) DEFAULT NULL,
  fixed_tel VARCHAR(255) DEFAULT NULL,
  gender VARCHAR(255) DEFAULT NULL,
  name VARCHAR(255) DEFAULT NULL,
  phone VARCHAR(255) DEFAULT NULL,
  service_duration DOUBLE DEFAULT NULL,
  skill VARCHAR(255) DEFAULT NULL,
  uid VARCHAR(255) DEFAULT NULL,
  unit VARCHAR(255) DEFAULT NULL,
  volunteer_id VARCHAR(255) DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
);

