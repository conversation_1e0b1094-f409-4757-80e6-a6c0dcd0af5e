-- 达梦数据库超级安全语法版本
-- 解决所有可能的保留字和语法冲突
-- 使用引号包围所有可能的保留字

-- 测试表
DROP TABLE IF EXISTS dm_test_safe;
CREATE TABLE dm_test_safe (
  id BIGINT NOT NULL,
  test_name VARCHAR(100),
  create_time DATETIME,
  PRIMARY KEY (id)
);

-- 核心业务表: app_activity (活动表)
DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT NOT NULL,
  address VARCHAR(255),
  content VARCHAR(255),
  conver_picture VARCHAR(255),
  create_by VARCHAR(255),
  create_time DATETIME,
  deleted TINYINT,
  end_time DATETIME,
  sort_order INT,
  start_time DATETIME,
  activity_status INT,
  title VARCHAR(255),
  activity_type TINYINT,
  update_by VARCHAR(255),
  update_time DATETIME,
  PRIMARY KEY (id)
);

-- 核心业务表: app_customer (客户表)
DROP TABLE IF EXISTS app_customer;
CREATE TABLE app_customer (
  id BIGINT NOT NULL,
  breaked_num INT,
  breaked_total_num INT,
  card_category TINYINT,
  card_no VARCHAR(255),
  create_by VARCHAR(255),
  create_time DATETIME,
  customer_id VARCHAR(255),
  deleted TINYINT,
  job VARCHAR(255),
  mini_openid VARCHAR(255),
  phone VARCHAR(255),
  real_name VARCHAR(255),
  unionid VARCHAR(255),
  update_by VARCHAR(255),
  update_time DATETIME,
  user_avatar_src VARCHAR(255),
  user_birthdate VARCHAR(255),
  user_gender TINYINT,
  user_name VARCHAR(255),
  PRIMARY KEY (id)
);

-- 核心业务表: app_appointment_order (预约订单表)
DROP TABLE IF EXISTS app_appointment_order;
CREATE TABLE app_appointment_order (
  id BIGINT NOT NULL,
  batch_date VARCHAR(255),
  batch_end_time VARCHAR(255),
  batch_no VARCHAR(255),
  batch_start_time VARCHAR(255),
  create_by VARCHAR(255),
  create_time DATETIME,
  deleted TINYINT,
  order_category TINYINT,
  order_no VARCHAR(255),
  order_remark VARCHAR(255),
  order_status SMALLINT,
  owner_name VARCHAR(255),
  owner_phone VARCHAR(255),
  owner_unionid VARCHAR(255),
  update_by VARCHAR(255),
  update_time DATETIME,
  PRIMARY KEY (id)
);

-- 核心业务表: app_config (配置表)
DROP TABLE IF EXISTS app_config;
CREATE TABLE app_config (
  id BIGINT NOT NULL,
  group_no VARCHAR(255),
  rule_name VARCHAR(255),
  rule_value VARCHAR(255),
  PRIMARY KEY (id)
);

-- 核心业务表: app_batch (批次表)
DROP TABLE IF EXISTS app_batch (
  id BIGINT NOT NULL,
  batch_category TINYINT,
  batch_date VARCHAR(255),
  batch_end_time VARCHAR(255),
  batch_no VARCHAR(255),
  batch_remark VARCHAR(255),
  batch_start_time VARCHAR(255),
  batch_status TINYINT,
  create_by VARCHAR(255),
  create_time DATETIME,
  deleted TINYINT,
  ticket_remaining INT,
  ticket_total INT,
  update_by VARCHAR(255),
  update_time DATETIME,
  PRIMARY KEY (id)
);

-- 测试插入数据
INSERT INTO dm_test_safe (id, test_name, create_time) VALUES (1, '测试数据', SYSDATE);

-- 验证查询
SELECT * FROM dm_test_safe;
