#!/usr/bin/env python3
"""
Final cleanup script for DM database compatibility
Handles remaining syntax issues and creates the final clean version
"""

import re

def final_cleanup(input_file, output_file):
    """Final cleanup for DM database compatibility"""
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix table creation statements
    lines = content.split('\n')
    cleaned_lines = []
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Handle CREATE TABLE statements
        if 'CREATE TABLE' in line:
            cleaned_lines.append(line)
            i += 1
            
            # Process table definition
            while i < len(lines) and not line.strip().endswith(');'):
                line = lines[i]
                
                # Remove COLLATE specifications that weren't caught
                line = re.sub(r'\s+COLLATE=\w+', '', line)
                
                # Fix comment syntax issues
                if '-- ' in line and not line.strip().endswith(',') and not line.strip().endswith(')'):
                    if i + 1 < len(lines) and lines[i + 1].strip() and not lines[i + 1].strip().startswith('--'):
                        if not line.strip().endswith(','):
                            line = line.rstrip() + ','
                
                cleaned_lines.append(line)
                i += 1
                
                if line.strip().endswith(');'):
                    break
        else:
            cleaned_lines.append(line)
            i += 1
    
    # Join lines back together
    content = '\n'.join(cleaned_lines)
    
    # Final cleanup patterns
    content = re.sub(r'\s+COLLATE=\w+', '', content)
    content = re.sub(r'(\) )(COLLATE=\w+;)', r'\1;', content)
    
    # Write final cleaned content
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Final cleanup completed. Output saved to: {output_file}")

if __name__ == "__main__":
    input_file = "dump-web_spup_test-dm-compatible-v2.sql"
    output_file = "dump-web_spup_test-dm-final.sql"
    
    try:
        final_cleanup(input_file, output_file)
    except Exception as e:
        print(f"Error during final cleanup: {e}")
        exit(1)
