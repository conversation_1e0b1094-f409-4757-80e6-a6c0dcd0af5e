#!/bin/bash

# 输入输出文件
INPUT_FILE="dump-web_spup_test-202505222301.sql"
OUTPUT_FILE="web_spup_test_dm_compatible.sql"

# 备份原始文件
cp "$INPUT_FILE" "$INPUT_FILE.bak"
echo "已备份原始文件为: $INPUT_FILE.bak"

# 处理步骤
echo "开始处理 MySQL dump 文件..."

# 1. 删除 MySQL 条件注释
sed -i 's/\/\*![0-9]* //g' "$INPUT_FILE"
sed -i 's/ \*\/;//g' "$INPUT_FILE"

# 2. 替换数据类型
sed -i 's/INT UNSIGNED/BIGINT/g' "$INPUT_FILE"
sed -i 's/TINYINT(1)/BOOLEAN/g' "$INPUT_FILE"
sed -i 's/ENGINE=InnoDB//g' "$INPUT_FILE"
sed -i 's/DEFAULT CURRENT_TIMESTAMP/DEFAULT SYSDATE/g' "$INPUT_FILE"

# 3. 替换函数
sed -i 's/NOW()/SYSDATE/g' "$INPUT_FILE"
sed -i 's/LIMIT/FETCH FIRST/g' "$INPUT_FILE"

# 4. 处理外键约束控制语句
sed -i 's/SET FOREIGN_KEY_CHECKS=0/-- 达梦: 使用 ALTER TABLE DISABLE CONSTRAINT/g' "$INPUT_FILE"
sed -i 's/SET FOREIGN_KEY_CHECKS=1/-- 达梦: 使用 ALTER TABLE ENABLE CONSTRAINT/g' "$INPUT_FILE"

# 5. 重命名输出文件
mv "$INPUT_FILE" "$OUTPUT_FILE"
echo "处理完成! 兼容达梦的 SQL 文件: $OUTPUT_FILE"