#!/usr/bin/env python3
"""
MySQL 8 to DM Database SQL Converter - Version 2
Improved converter with better handling of DM-specific syntax
"""

import re
import sys

def convert_mysql_to_dm(input_file, output_file):
    """Convert MySQL SQL dump to DM database compatible SQL"""
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Step 1: Remove MySQL-specific comments and settings
    content = re.sub(r'/\*!\d+.*?\*/;?', '', content, flags=re.DOTALL)
    content = re.sub(r'LOCK TABLES.*?UNLOCK TABLES;', '', content, flags=re.DOTALL)
    
    # Step 2: Convert AUTO_INCREMENT to IDENTITY for DM
    # Handle AUTO_INCREMENT with proper IDENTITY syntax
    content = re.sub(r'(\w+)\s+bigint\s+NOT\s+NULL\s+AUTO_INCREMENT', r'\1 BIGINT IDENTITY(1,1) NOT NULL', content, flags=re.IGNORECASE)
    
    # Remove remaining AUTO_INCREMENT keywords
    content = re.sub(r'\s+AUTO_INCREMENT', '', content, flags=re.IGNORECASE)
    
    # Step 3: Convert data types
    # Convert datetime(6) to TIMESTAMP(6)
    content = re.sub(r'datetime\(6\)', 'TIMESTAMP(6)', content, flags=re.IGNORECASE)
    
    # Convert tinyint to TINYINT
    content = re.sub(r'\btinyint\b', 'TINYINT', content, flags=re.IGNORECASE)
    
    # Convert smallint to SMALLINT
    content = re.sub(r'\bsmallint\b', 'SMALLINT', content, flags=re.IGNORECASE)
    
    # Convert int to INT (but not in function calls)
    content = re.sub(r'\bint\b(?!\s*\()', 'INT', content, flags=re.IGNORECASE)
    
    # Convert bigint to BIGINT
    content = re.sub(r'\bbigint\b', 'BIGINT', content, flags=re.IGNORECASE)
    
    # Convert date to DATE
    content = re.sub(r'\bdate\b', 'DATE', content, flags=re.IGNORECASE)
    
    # Convert tinyblob to BLOB
    content = re.sub(r'\btinyblob\b', 'BLOB', content, flags=re.IGNORECASE)
    
    # Step 4: Remove collation, charset, and engine specifications
    content = re.sub(r'\s+COLLATE\s+\w+', '', content, flags=re.IGNORECASE)
    content = re.sub(r'\s+DEFAULT\s+CHARSET=\w+', '', content, flags=re.IGNORECASE)
    content = re.sub(r'\s+ENGINE=\w+', '', content, flags=re.IGNORECASE)
    content = re.sub(r'\s+AUTO_INCREMENT=\d+', '', content, flags=re.IGNORECASE)
    
    # Step 5: Remove backticks
    content = re.sub(r'`([^`]+)`', r'\1', content)
    
    # Step 6: Convert COMMENT syntax to DM format (use -- comments)
    def convert_comment(match):
        comment_text = match.group(1)
        return f' -- {comment_text}'
    
    content = re.sub(r'\s+COMMENT\s+\'([^\']+)\'', convert_comment, content)
    
    # Step 7: Fix syntax issues specific to the conversion
    # Fix missing commas after comments
    content = re.sub(r'(-- [^,\n]+)(\n\s+\w+)', r'\1,\2', content)
    
    # Fix table creation syntax issues
    content = re.sub(r'(\) )(COLLATE=\w+;)', r'\1;', content)
    
    # Step 8: Clean up extra whitespace and empty lines
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    content = re.sub(r'[ \t]+\n', '\n', content)
    
    # Step 9: Add DM-specific header
    dm_header = """-- DM Database dump converted from MySQL 8.0.42
--
-- Host: localhost    Database: web_spup_test
-- ------------------------------------------------------
-- Converted for DM Database compatibility

-- Set basic session parameters for DM
SET IDENTITY_INSERT OFF;

"""
    
    # Find the start of table definitions (skip MySQL header)
    table_start = content.find('-- Table structure for table')
    if table_start != -1:
        content = dm_header + content[table_start:]
    else:
        content = dm_header + content
    
    # Step 10: Write converted content to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Conversion completed. Output saved to: {output_file}")

if __name__ == "__main__":
    input_file = "dump-web_spup_test-202505222301.sql.bak"
    output_file = "dump-web_spup_test-dm-compatible-v2.sql"
    
    try:
        convert_mysql_to_dm(input_file, output_file)
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
        sys.exit(1)
    except Exception as e:
        print(f"Error during conversion: {e}")
        sys.exit(1)
