upstream relle8880 {
    server 127.0.0.1:8880 weight=5;
    keepalive 256;
}

server {
    listen       443 ssl http2;
	listen       [::]:443 ssl http2;
	server_name  relle.douwifi.cn;

	ssl_certificate "/etc/nginx/ssl/relle.douwifi.cn_ssl/relle.douwifi.cn_bundle.crt";
	ssl_certificate_key "/etc/nginx/ssl/relle.douwifi.cn_ssl/relle.douwifi.cn.key";

	ssl_session_cache    shared:SSL:1m;
	ssl_session_timeout  5m;

	ssl_ciphers  HIGH:!aNULL:!MD5;
	ssl_prefer_server_ciphers  on;


    client_max_body_size 200M;
    charset utf-8 ;
    access_log  /var/log/nginx/host.access.log  main;
    root   /usr/share/nginx/html;
    index index.php  index.html index.htm;
    #proxy_intercept_errors on;
	
	
    error_page 404 500 502 503 504 /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
    location / {
        set $fixed_destination $http_destination;
        if ( $http_destination ~* ^https(.*)$ )
        {
        	set $fixed_destination http$1;
        }
        proxy_set_header        Host $host;
        proxy_set_header        X-Real-IP $remote_addr;
        proxy_set_header        Destination $fixed_destination;	

        index index.jsp;
        proxy_pass http://relle8880;
    }

	#RELL前端页面
	location /relle/ {
		alias /home/<USER>/html/relle/;
	}	
	#RELL静态资源
	location /relle-media/ {
		alias /home/<USER>/html/relle-media/;
	}
	#RELLE-店员
	location /relle-mall/ {
		alias /home/<USER>/html/relle-mall/;
	}
	#RELLE-店长
	location /relle-pm/ {
		alias /home/<USER>/html/relle-pm/;
	}
	#RELLE管理后台页面
	location /relle-admin {
		alias /home/<USER>/html/relle-admin/;
	}	
	#RELLE皮肤检测图片
	location /skin-pic {
	    alias /home/<USER>/;
	}

	#小程序放置在服务端的配置
	location /relleMiniprogram {
		if ($request_method = 'OPTIONS') {
				add_header Access-Control-Allow-Origin $http_origin always;
				add_header Access-Control-Allow-Credentials true always;
				add_header Access-Control-Allow-Methods 'GET,POST,PUT,DELETE,OPTIONS' always;
				add_header Access-Control-Allow-Headers 'Authorization,X-Requested-With,Content-Type,Origin,Accept' always;
				add_header Access-Control-Max-Age 3600;
				add_header Content-Length 0;
				return 204;
		}
		add_header Access-Control-Allow-Headers 'accept, content-type, origin, custom-header';
		alias /home/<USER>/html/relleMiniprogram/;
	}


}

