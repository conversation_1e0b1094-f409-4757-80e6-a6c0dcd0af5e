upstream tomcat8080 {
    #在Nginx的 upstream 模块中， weight 参数用于指定后端服务器的权重。
    #它决定了在负载均衡时，请求被分配到各个服务器的比例。
    #权重越高的服务器，接收到的请求就越多。
    server 127.0.0.1:8080 weight=5;
    keepalive 256;
}
upstream tomcat9080 {
    server 127.0.0.1:9080 weight=5;
    keepalive 256;
}

# 定义上游服务器 python6001，用于处理Python应用，以备python简单测试
upstream python6001 {
    server 127.0.0.1:6001 weight=4;
}

# 定义服务器块
server {
    listen       443 ssl http2;
    # 监听IPv6的443端口，启用SSL和HTTP/2
	listen       [::]:443 ssl http2;
    # 服务器名称
	server_name  relletest.douwifi.cn;

    # SSL证书文件路径
	ssl_certificate "/etc/nginx/ssl/relletest.douwifi.cn_ssl/relletest.douwifi.cn_bundle.crt";
    # SSL证书密钥文件路径
	ssl_certificate_key "/etc/nginx/ssl/relletest.douwifi.cn_ssl/relletest.douwifi.cn.key";

    # SSL会话缓存，共享内存大小1MB
	ssl_session_cache    shared:SSL:10m;
    # SSL会话超时时间5分钟
	ssl_session_timeout  5m;

    # SSL加密算法套件
	ssl_ciphers  HIGH:!aNULL:!MD5;
    # 优先使用服务器端加密算法
	ssl_prefer_server_ciphers  on;

    # 客户端请求体最大大小
    client_max_body_size 200M;
    # 字符集
    charset utf-8 ;
    # 访问日志路径和格式
    access_log  /var/log/nginx/host-tomcat.access.log  main;
    # 网站根目录
    # root   /usr/share/nginx/html;
    # 默认索引文件
    index index.php  index.html index.htm;
    #proxy_intercept_errors on; # 是否拦截后端服务器的错误页面，此处已注释
	
    # 定义错误页面
    error_page 404 500 502 503 504 /50x.html;
    # 错误页面 /50x.html 的位置块
    location = /50x.html {
        # 错误页面的根目录
        root   /etc/nginx/static_html;
    }
    # 根路径 / 的位置块
    location / {
        # 设置一个变量 $fixed_destination，值为HTTP请求头中的 Destination
        set $fixed_destination $http_destination;
        # 如果 Destination 请求头以 https 开头
        if ( $http_destination ~* ^https(.*)$ )
        {
            # 将 $fixed_destination 的值修改为 http 开头
        	set $fixed_destination http$1;
        }
        # 设置代理请求头 Host
        proxy_set_header        Host $host;
        # 设置代理请求头 X-Real-IP，值为客户端真实IP
        proxy_set_header        X-Real-IP $remote_addr;
        # 设置代理请求头 Destination
        proxy_set_header        Destination $fixed_destination;	

        # 默认索引文件
        index index.jsp;
        # 将请求代理到 jsp8080 上游服务器
        proxy_pass http://tomcat8080;
    }

	#浦东馆新前端页面
	location /spup-mp/ {
		alias /home/<USER>/spup-mp/;
	}
	#浦东馆新前台——资源
	location /spup-media/ {
		alias /home/<USER>/spup-media/;
	}
	#浦东规划馆管理后台页面
	location /spupadmin/ {
		alias /home/<USER>/spup_admin/;
	}
	
    # # 匹配特定路径的位置块 (d2d|d2p|xhwanyi|jmap|jmap_admin|supec_appointment)
	#  location ~* /(d2d|d2p|xhwanyi|jmap|jmap_admin|supec_appointment)/ {
    #     # 设置一个变量 $fixed_destination，值为HTTP请求头中的 Destination
    #     set $fixed_destination $http_destination;
    #     # 如果 Destination 请求头以 https 开头
    #     if ( $http_destination ~* ^https(.*)$ )
    #     {
    #         # 将 $fixed_destination 的值修改为 http 开头
    #     	set $fixed_destination http$1;
    #     }
    #     # 设置代理请求头 Host
    #     proxy_set_header        Host $host;
    #     # 设置代理请求头 X-Real-IP，值为客户端真实IP
    #     proxy_set_header        X-Real-IP $remote_addr;
    #     # 设置代理请求头 Destination
    #     proxy_set_header        Destination $fixed_destination;	

    #     # 默认索引文件
    #     index index.jsp;
    #     # 将请求代理到 jsp9080 上游服务器
    #     proxy_pass http://tomcat9080;
    # }
   
	# #规划馆官网
	# location /supec-org {
	#     alias /home/<USER>/webapps/supec/supweb/;
	# }

    # #吉林人大新页面
    # location /jlrd-html/ {
    #     alias /home/<USER>/jlrd_new_html/;
    #     # 尝试按顺序查找文件，如果找不到则返回 /jlrd-html/index.html
    #     try_files $uri $uri/ /jlrd-html/index.html;
    # }

    #吉林人大前台-资源
    # location /jlrd-media/ {
    #     alias /home/<USER>/jlrd-media/;
    # }

    # video 视频相关路径
    # location /video {
    #     # 将请求代理到 127.0.0.1:6001
    #     proxy_pass http://127.0.0.1:6001;
    # }
        
    # socket.io 相关路径，用于WebSocket连接
    # location /socket.io/ {
    #     # 设置代理请求头 Upgrade
    #     proxy_set_header Upgrade $http_upgrade;
    #     # 设置代理请求头 Connection 为 upgrade
    #     proxy_set_header Connection "upgrade";
    #     # 将请求代理到 127.0.0.1:6001/socket.io/
    #     proxy_pass   http://127.0.0.1:6001/socket.io/;
    # }	
	
	# #上海城市规划展示馆新前台
	# location /supecnewhtml/ {
	# 	alias /home/<USER>/supec_new_html/;
	# }
	# #上海城市规划展示馆新前台——资源
	# location /supecnewmedia/ {
	# 	alias /home/<USER>/supec_new_media/;
	# }
    # #上海城市规划展示馆-官网新管理后台
    # location /supec_admin_v2/ {
    #     alias /home/<USER>/supec_admin_v2/;
    # }
    # #上海城市规划展示馆-公众号新管理后台
    # location /supec_mp_admin_v2/ {
    #     alias /home/<USER>/supec_mp_admin_v2/;
    # }
    # #上海城市规划展示馆-新官网-预览
    # location /supec_org_v2_preview/ {
    #     alias /home/<USER>/supec_org_v2_preview/;
    # }
    # #上海城市规划展示馆-新官网
    # location /supec_org_v2/ {
    #     alias /home/<USER>/supec_org_v2/;
    # }
	
	
	# #RELL前端页面
	location /relle/ {
		alias /home/<USER>/relle/;
	}	
	#RELL静态资源
	location /relle-media/ {
		alias /home/<USER>/relle-media/;
	}
	#RELLE-店员
	location /relle-mall/ {
		alias /home/<USER>/relle-mall/;
	}
	#RELLE-店长
	location /relle-pm/ {
		alias /home/<USER>/relle-pm/;
	}
	#RELLE管理后台页面
	location /relle-admin {
		alias /home/<USER>/relle-admin/;
	}	
    #RELLE皮肤检测图片
    location /skin-pic {
            alias /home/<USER>/;
    }
	
	
	# #api接口
    # location /api/ {
    #     alias /home/<USER>/api/;
    # }

    # #车辆测试-管理后台
    # location /car_test/ {
    #     alias /home/<USER>/car_test/;
    # }
    # #车辆测试-移动端页面
    # location /car_test_m/ {
    #     alias /home/<USER>/car_test_m/;
    # }
}

