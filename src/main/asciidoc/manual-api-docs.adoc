# SPUP API Documentation

## Common Response Format

All API responses follow a common format:

```json
{
  "status": boolean,       // true for success, false for failure
  "code": number,          // HTTP status code
  "message": string,       // Human-readable message
  "data": object|null      // Response data (null for error responses)
}
```

## Common Error Responses

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - The request was invalid or cannot be served |
| 401 | Unauthorized - Authentication is required or has failed |
| 403 | Forbidden - The server understood the request but refuses to authorize it |
| 404 | Not Found - The requested resource could not be found |
| 409 | Conflict - The request could not be completed due to a conflict with the current state of the resource |
| 500 | Internal Server Error - An unexpected condition was encountered |

## Data Validation Rules

### Phone Number Format
- Must be a Chinese mobile phone number
- Must start with 1, followed by a digit between 3-9, and then 9 more digits
- Regex pattern: `^1[3-9]\\d{9}$`
- Example: `13800138000`

### ID Card Format
- Must be a valid Chinese ID card number
- 18 digits in the format: RRRRRRYYYYMMDDSSSC
  - RRRRRR: 6-digit region code
  - YYYYMMDD: 8-digit birth date
  - SSS: 3-digit sequence code
  - C: 1-digit check code (can be 0-9 or X)
- Regex pattern: `^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$`
- Example: `110101199001011234`

### Appointment Status Values
- Valid values: `CONFIRMED`, `CANCELLED`, `COMPLETED`, `NO_SHOW`

### Batch Status Values
- Valid values: `OPEN`, `FULL`, `CLOSED`, `CANCELLED`

### Email Format
- Must be a valid email address
- Regex pattern: `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$`
- Example: `<EMAIL>`

### Date Format
- Must be in ISO date format: YYYY-MM-DD
- Example: `2023-10-15`

### Time Format
- Must be in 24-hour format: HH:MM
- Example: `14:30`

## App API Endpoints

### Get Appointment Details

Retrieves the details of a specific appointment.

**Request:**
```
GET /api/appointments/{id}
Accept: application/json
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the appointment |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "id": 123,
    "orderNo": "ORDER123456789012",
    "batchDate": "2023-10-15",
    "batchStartTime": "10:00",
    "batchEndTime": "11:00",
    "status": "CONFIRMED"
  }
}
```

**Error Responses:**

*Appointment Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Appointment not found"
}
```

*Unauthorized Access (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Unauthorized access"
}
```

### Book Appointment

Books a new appointment for a specific batch.

**Request:**
```
POST /api/appointments/book
Content-Type: application/json
Accept: application/json

{
  "batchId": 789,
  "contactName": "John Doe",
  "contactPhone": "13800138000",
  "idCardType": 1,
  "idCardNo": "110101199001011234"
}
```

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| batchId | Integer | Yes | The ID of the batch to book | Must be a valid batch ID |
| contactName | String | Yes | The name of the contact person | |
| contactPhone | String | Yes | The phone number of the contact person | Must match the phone number format |
| idCardType | Integer | Yes | The type of ID card (1 for Chinese ID) | |
| idCardNo | String | Yes | The ID card number | Must match the ID card format |

**Success Response (201 Created):**
```json
{
  "status": true,
  "code": 200,
  "message": "Appointment booked successfully",
  "data": {
    "orderNo": "ORDER123456789012",
    "batchDate": "2023-10-20",
    "batchStartTime": "14:00",
    "batchEndTime": "15:00",
    "status": "CONFIRMED"
  }
}
```

**Error Responses:**

*Missing Required Fields (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Missing required fields"
}
```

*Invalid Phone Number Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid phone number format"
}
```

*Invalid ID Card Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid ID card format"
}
```

*Batch Not Available (409 Conflict):*
```json
{
  "status": false,
  "code": 409,
  "message": "Batch is not available"
}
```

### List Batches

Retrieves a paginated list of batches with optional filtering by date.

**Request:**
```
GET /api/batches?date=2023-10-15&page=0&size=10
Accept: application/json
```

**Query Parameters:**
| Parameter | Type | Required | Default | Description | Validation |
|-----------|------|----------|---------|-------------|------------|
| date | String | No | | Filter by batch date (YYYY-MM-DD) | |
| page | Integer | No | 0 | The page number (zero-based) | Must be >= 0 |
| size | Integer | No | 10 | The page size | Must be <= 100 |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [
      {
        "id": 101,
        "date": "2023-10-15",
        "startTime": "09:00",
        "endTime": "10:00",
        "capacity": 50,
        "remaining": 20,
        "status": "OPEN"
      },
      {
        "id": 102,
        "date": "2023-10-15",
        "startTime": "10:30",
        "endTime": "11:30",
        "capacity": 50,
        "remaining": 0,
        "status": "FULL"
      }
    ],
    "totalElements": 2,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

**Success Response - Empty Result (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [],
    "totalElements": 0,
    "totalPages": 0,
    "size": 10,
    "number": 0
  }
}
```

**Error Responses:**

*Unauthorized Access (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Unauthorized access"
}
```

*Invalid Page Size (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Page size must not be greater than 100"
}
```

### Get Batch Details

Retrieves the details of a specific batch.

**Request:**
```
GET /api/batches/{id}
Accept: application/json
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the batch |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "id": 101,
    "date": "2023-10-15",
    "startTime": "09:00",
    "endTime": "10:00",
    "capacity": 50,
    "remaining": 20,
    "status": "OPEN",
    "description": "Morning batch",
    "notes": "Please arrive 15 minutes early"
  }
}
```

**Error Responses:**

*Batch Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Batch not found"
}
```

### Get Available Dates

Retrieves a list of dates that have available batches.

**Request:**
```
GET /api/batches/available-dates?month=2023-10
Accept: application/json
```

**Query Parameters:**
| Parameter | Type | Required | Default | Description | Validation |
|-----------|------|----------|---------|-------------|------------|
| month | String | No | Current month | Filter by month (YYYY-MM) | |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "month": "2023-10",
    "dates": [
      "2023-10-01",
      "2023-10-05",
      "2023-10-10",
      "2023-10-15",
      "2023-10-20",
      "2023-10-25"
    ]
  }
}
```

### List Goods

Retrieves a paginated list of goods with optional filtering by category.

**Request:**
```
GET /api/goods?category=books&page=0&size=10
Accept: application/json
```

**Query Parameters:**
| Parameter | Type | Required | Default | Description | Validation |
|-----------|------|----------|---------|-------------|------------|
| category | String | No | | Filter by goods category | |
| page | Integer | No | 0 | The page number (zero-based) | Must be >= 0 |
| size | Integer | No | 10 | The page size | Must be <= 100 |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [
      {
        "id": 201,
        "name": "Museum Guide Book",
        "description": "Comprehensive guide to the museum",
        "price": 29.99,
        "category": "books",
        "imageUrl": "https://example.com/images/guide-book.jpg",
        "inStock": true
      },
      {
        "id": 202,
        "name": "Museum Souvenir Mug",
        "description": "Ceramic mug with museum logo",
        "price": 19.99,
        "category": "souvenirs",
        "imageUrl": "https://example.com/images/mug.jpg",
        "inStock": true
      }
    ],
    "totalElements": 2,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

**Success Response - Empty Result (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [],
    "totalElements": 0,
    "totalPages": 0,
    "size": 10,
    "number": 0
  }
}
```

**Error Responses:**

*Unauthorized Access (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Unauthorized access"
}
```

*Invalid Page Size (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Page size must not be greater than 100"
}
```

### Get Goods Details

Retrieves the details of a specific goods item.

**Request:**
```
GET /api/goods/{id}
Accept: application/json
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the goods item |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "id": 201,
    "name": "Museum Guide Book",
    "description": "Comprehensive guide to the museum with detailed information about all exhibits",
    "price": 29.99,
    "category": "books",
    "imageUrl": "https://example.com/images/guide-book.jpg",
    "inStock": true,
    "stockQuantity": 150,
    "createdAt": "2023-01-15T10:00:00Z",
    "updatedAt": "2023-05-20T14:30:00Z",
    "relatedItems": [
      {
        "id": 202,
        "name": "Museum Souvenir Mug",
        "price": 19.99
      },
      {
        "id": 203,
        "name": "Museum Map",
        "price": 9.99
      }
    ]
  }
}
```

**Error Responses:**

*Goods Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Goods not found"
}
```

### Get Goods Categories

Retrieves a list of available goods categories.

**Request:**
```
GET /api/goods/categories
Accept: application/json
```

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "books",
        "displayName": "Books"
      },
      {
        "id": 2,
        "name": "souvenirs",
        "displayName": "Souvenirs"
      },
      {
        "id": 3,
        "name": "clothing",
        "displayName": "Clothing"
      }
    ]
  }
}
```

### Search Goods

Searches for goods items based on keyword and optional category filter.

**Request:**
```
POST /api/goods/search
Content-Type: application/json
Accept: application/json

{
  "keyword": "museum",
  "category": "books"
}
```

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| keyword | String | Yes | The search keyword | |
| category | String | No | Filter by category | |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [
      {
        "id": 201,
        "name": "Museum Guide Book",
        "description": "Comprehensive guide to the museum",
        "price": 29.99,
        "category": "books",
        "imageUrl": "https://example.com/images/guide-book.jpg"
      }
    ],
    "totalElements": 1,
    "keyword": "museum",
    "category": "books"
  }
}
```

**Success Response - No Results (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "No results found",
  "data": {
    "content": [],
    "totalElements": 0,
    "keyword": "nonexistent"
  }
}
```

**Error Responses:**

*Missing Keyword (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Keyword is required"
}
```

### User Login

Authenticates a user and returns a token.

**Request:**
```
POST /api/users/login
Content-Type: application/json
Accept: application/json

{
  "username": "johndoe",
  "password": "password123"
}
```

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| username | String | Yes | The username of the user | |
| password | String | Yes | The password of the user | |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Login successful",
  "data": {
    "userId": 1001,
    "username": "johndoe",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600
  }
}
```

**Error Responses:**

*Missing Required Fields (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Username and password are required"
}
```

*Invalid Credentials (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Invalid username or password"
}
```

### User Registration

Registers a new user.

**Request:**
```
POST /api/users/register
Content-Type: application/json
Accept: application/json

{
  "username": "newuser",
  "password": "password123",
  "phone": "13800138000",
  "email": "<EMAIL>"
}
```

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| username | String | Yes | The username for the new user | |
| password | String | Yes | The password for the new user | |
| phone | String | Yes | The phone number of the user | Must match the phone number format |
| email | String | Yes | The email address of the user | Must match the email format |

**Success Response (201 Created):**
```json
{
  "status": true,
  "code": 201,
  "message": "Registration successful",
  "data": {
    "userId": 1001,
    "username": "newuser",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "createdAt": "2023-10-15T10:00:00Z"
  }
}
```

**Error Responses:**

*Missing Required Fields (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Missing required fields"
}
```

*Invalid Phone Number Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid phone number format"
}
```

*Invalid Email Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid email format"
}
```

*Username Already Exists (409 Conflict):*
```json
{
  "status": false,
  "code": 409,
  "message": "Username already exists"
}
```

### Get User Profile

Retrieves the profile of the authenticated user.

**Request:**
```
GET /api/users/profile
Authorization: Bearer {token}
Accept: application/json
```

**Headers:**
| Header | Required | Description |
|--------|----------|-------------|
| Authorization | Yes | Bearer token for authentication |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "userId": 1001,
    "username": "johndoe",
    "fullName": "John Doe",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatars/johndoe.jpg",
    "createdAt": "2023-01-15T10:00:00Z",
    "lastLogin": "2023-10-15T08:30:00Z"
  }
}
```

**Error Responses:**

*Unauthorized Access (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Unauthorized access"
}
```

*Invalid Token (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Invalid token"
}
```

### Update User Profile

Updates the profile of the authenticated user.

**Request:**
```
PUT /api/users/profile
Authorization: Bearer {token}
Content-Type: application/json
Accept: application/json

{
  "fullName": "John Smith",
  "phone": "13900139000",
  "email": "<EMAIL>"
}
```

**Headers:**
| Header | Required | Description |
|--------|----------|-------------|
| Authorization | Yes | Bearer token for authentication |

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| fullName | String | No | The full name of the user | |
| phone | String | No | The phone number of the user | Must match the phone number format if provided |
| email | String | No | The email address of the user | Must match the email format if provided |
| avatar | String | No | URL to the user's avatar image | |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Profile updated successfully",
  "data": {
    "userId": 1001,
    "username": "johndoe",
    "fullName": "John Smith",
    "phone": "13900139000",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatars/johndoe.jpg",
    "updatedAt": "2023-10-15T10:30:00Z"
  }
}
```

**Error Responses:**

*Unauthorized Access (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Unauthorized access"
}
```

*Invalid Phone Number Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid phone number format"
}
```

*Invalid Email Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid email format"
}
```

### Change Password

Changes the password of the authenticated user.

**Request:**
```
POST /api/users/change-password
Authorization: Bearer {token}
Content-Type: application/json
Accept: application/json

{
  "currentPassword": "password123",
  "newPassword": "newpassword123"
}
```

**Headers:**
| Header | Required | Description |
|--------|----------|-------------|
| Authorization | Yes | Bearer token for authentication |

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| currentPassword | String | Yes | The current password of the user | |
| newPassword | String | Yes | The new password for the user | |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Password changed successfully"
}
```

**Error Responses:**

*Unauthorized Access (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Unauthorized access"
}
```

*Missing Required Fields (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Current password and new password are required"
}
```

*Incorrect Current Password (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Current password is incorrect"
}
```

### Get User Orders

Retrieves a paginated list of orders for the authenticated user.

**Request:**
```
GET /api/users/orders?page=0&size=10
Authorization: Bearer {token}
Accept: application/json
```

**Headers:**
| Header | Required | Description |
|--------|----------|-------------|
| Authorization | Yes | Bearer token for authentication |

**Query Parameters:**
| Parameter | Type | Required | Default | Description | Validation |
|-----------|------|----------|---------|-------------|------------|
| page | Integer | No | 0 | The page number (zero-based) | Must be >= 0 |
| size | Integer | No | 10 | The page size | Must be <= 100 |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [
      {
        "id": 5001,
        "orderNo": "ORDER123456789012",
        "type": "APPOINTMENT",
        "status": "CONFIRMED",
        "amount": 0.00,
        "createdAt": "2023-10-15T10:00:00Z"
      },
      {
        "id": 5002,
        "orderNo": "ORDER987654321098",
        "type": "GOODS",
        "status": "PAID",
        "amount": 49.98,
        "createdAt": "2023-10-10T14:30:00Z"
      }
    ],
    "totalElements": 2,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

**Success Response - Empty Result (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [],
    "totalElements": 0,
    "totalPages": 0,
    "size": 10,
    "number": 0
  }
}
```

**Error Responses:**

*Unauthorized Access (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Unauthorized access"
}
```

*Invalid Page Size (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Page size must not be greater than 100"
}
```

## Admin API Endpoints

### List Appointments

Retrieves a paginated list of appointments with optional filtering.

**Request:**
```
GET /manage/appointments?page=0&size=10&status=CONFIRMED&date=2023-10-15
Accept: application/json
```

**Query Parameters:**
| Parameter | Type | Required | Default | Description | Validation |
|-----------|------|----------|---------|-------------|------------|
| page | Integer | No | 0 | The page number (zero-based) | Must be >= 0 |
| size | Integer | No | 10 | The page size | Must be <= 100 |
| status | String | No | | Filter by appointment status | Must be a valid status value |
| date | String | No | | Filter by appointment date (YYYY-MM-DD) | |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [
      {
        "id": 123,
        "orderNo": "ORDER123456789012",
        "batchDate": "2023-10-15",
        "batchStartTime": "10:00",
        "batchEndTime": "11:00",
        "status": "CONFIRMED",
        "contactName": "John Doe",
        "contactPhone": "13800138000"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

**Success Response - Empty Result (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [],
    "totalElements": 0,
    "totalPages": 0,
    "size": 10,
    "number": 0
  }
}
```

**Error Responses:**

*Unauthorized Access (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Unauthorized access"
}
```

*Invalid Page Size (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Page size must not be greater than 100"
}
```

### Update Appointment Status

Updates the status of an existing appointment.

**Request:**
```
PUT /manage/appointments/{id}/status
Content-Type: application/json
Accept: application/json

{
  "status": "CANCELLED",
  "reason": "Customer request"
}
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the appointment |

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| status | String | Yes | The new status of the appointment | Must be a valid status value |
| reason | String | No | The reason for the status change | |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Status updated successfully",
  "data": {
    "id": 123,
    "status": "CANCELLED"
  }
}
```

**Error Responses:**

*Appointment Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Appointment not found"
}
```

*Missing Status (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Status is required"
}
```

*Invalid Status Value (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid status value. Must be one of: [CONFIRMED, CANCELLED, COMPLETED, NO_SHOW]"
}
```

*Invalid Status Transition (409 Conflict):*
```json
{
  "status": false,
  "code": 409,
  "message": "Cannot transition from CANCELLED to COMPLETED"
}
```

### List Batches (Admin)

Retrieves a paginated list of batches with optional filtering.

**Request:**
```
GET /manage/batches?date=2023-10-15&status=OPEN&page=0&size=10
Accept: application/json
```

**Query Parameters:**
| Parameter | Type | Required | Default | Description | Validation |
|-----------|------|----------|---------|-------------|------------|
| date | String | No | | Filter by batch date (YYYY-MM-DD) | |
| status | String | No | | Filter by batch status | Must be a valid status value |
| page | Integer | No | 0 | The page number (zero-based) | Must be >= 0 |
| size | Integer | No | 10 | The page size | Must be <= 100 |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [
      {
        "id": 101,
        "date": "2023-10-15",
        "startTime": "09:00",
        "endTime": "10:00",
        "capacity": 50,
        "remaining": 20,
        "status": "OPEN",
        "createdBy": "admin",
        "createdAt": "2023-10-01T10:00:00Z"
      },
      {
        "id": 102,
        "date": "2023-10-15",
        "startTime": "10:30",
        "endTime": "11:30",
        "capacity": 50,
        "remaining": 0,
        "status": "FULL",
        "createdBy": "admin",
        "createdAt": "2023-10-01T10:15:00Z"
      }
    ],
    "totalElements": 2,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

**Success Response - Empty Result (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [],
    "totalElements": 0,
    "totalPages": 0,
    "size": 10,
    "number": 0
  }
}
```

**Error Responses:**

*Unauthorized Access (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Unauthorized access"
}
```

*Invalid Page Size (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Page size must not be greater than 100"
}
```

### Get Batch Details (Admin)

Retrieves the details of a specific batch, including appointments.

**Request:**
```
GET /manage/batches/{id}
Accept: application/json
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the batch |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "id": 101,
    "date": "2023-10-15",
    "startTime": "09:00",
    "endTime": "10:00",
    "capacity": 50,
    "remaining": 20,
    "status": "OPEN",
    "description": "Morning batch",
    "notes": "Please arrive 15 minutes early",
    "createdBy": "admin",
    "createdAt": "2023-10-01T10:00:00Z",
    "updatedBy": "admin",
    "updatedAt": "2023-10-05T14:30:00Z",
    "appointments": [
      {
        "id": 201,
        "orderNo": "ORDER123456789012",
        "contactName": "John Doe",
        "contactPhone": "13800138000",
        "status": "CONFIRMED",
        "createdAt": "2023-10-10T09:15:00Z"
      },
      {
        "id": 202,
        "orderNo": "ORDER987654321098",
        "contactName": "Jane Smith",
        "contactPhone": "13900139000",
        "status": "CONFIRMED",
        "createdAt": "2023-10-10T10:30:00Z"
      }
    ]
  }
}
```

**Error Responses:**

*Batch Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Batch not found"
}
```

### Create Batch

Creates a new batch.

**Request:**
```
POST /manage/batches
Content-Type: application/json
Accept: application/json

{
  "date": "2023-11-01",
  "startTime": "14:00",
  "endTime": "15:00",
  "capacity": 50,
  "description": "Afternoon batch",
  "notes": "Please arrive 15 minutes early"
}
```

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| date | String | Yes | The date of the batch | Must be in YYYY-MM-DD format |
| startTime | String | Yes | The start time of the batch | Must be in HH:MM format (24-hour) |
| endTime | String | Yes | The end time of the batch | Must be in HH:MM format (24-hour) |
| capacity | Integer | Yes | The maximum capacity of the batch | |
| description | String | No | A description of the batch | |
| notes | String | No | Additional notes for the batch | |

**Success Response (201 Created):**
```json
{
  "status": true,
  "code": 201,
  "message": "Batch created successfully",
  "data": {
    "id": 103,
    "date": "2023-11-01",
    "startTime": "14:00",
    "endTime": "15:00",
    "capacity": 50,
    "remaining": 50,
    "status": "OPEN",
    "description": "Afternoon batch",
    "notes": "Please arrive 15 minutes early",
    "createdBy": "admin",
    "createdAt": "2023-10-15T10:00:00Z"
  }
}
```

**Error Responses:**

*Missing Required Fields (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Missing required fields"
}
```

*Invalid Date Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid date format. Use YYYY-MM-DD"
}
```

*Invalid Time Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid time format. Use HH:MM (24-hour format)"
}
```

*Batch Already Exists (409 Conflict):*
```json
{
  "status": false,
  "code": 409,
  "message": "Batch already exists for this date and time"
}
```

### Update Batch

Updates an existing batch.

**Request:**
```
PUT /manage/batches/{id}
Content-Type: application/json
Accept: application/json

{
  "startTime": "09:30",
  "endTime": "10:30",
  "capacity": 60,
  "description": "Updated morning batch",
  "notes": "Updated notes"
}
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the batch |

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| startTime | String | No | The start time of the batch | Must be in HH:MM format (24-hour) if provided |
| endTime | String | No | The end time of the batch | Must be in HH:MM format (24-hour) if provided |
| capacity | Integer | No | The maximum capacity of the batch | |
| status | String | No | The status of the batch | Must be a valid status value if provided |
| description | String | No | A description of the batch | |
| notes | String | No | Additional notes for the batch | |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Batch updated successfully",
  "data": {
    "id": 101,
    "date": "2023-10-15",
    "startTime": "09:30",
    "endTime": "10:30",
    "capacity": 60,
    "remaining": 20,
    "status": "OPEN",
    "description": "Updated morning batch",
    "notes": "Updated notes",
    "updatedBy": "admin",
    "updatedAt": "2023-10-15T14:30:00Z"
  }
}
```

**Error Responses:**

*Batch Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Batch not found"
}
```

*Invalid Time Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid start time format. Use HH:MM (24-hour format)"
}
```

### Update Batch Status

Updates the status of an existing batch.

**Request:**
```
PUT /manage/batches/{id}/status
Content-Type: application/json
Accept: application/json

{
  "status": "CLOSED"
}
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the batch |

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| status | String | Yes | The new status of the batch | Must be a valid status value |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Batch status updated successfully",
  "data": {
    "id": 101,
    "status": "CLOSED",
    "updatedBy": "admin",
    "updatedAt": "2023-10-15T14:30:00Z"
  }
}
```

**Error Responses:**

*Batch Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Batch not found"
}
```

*Missing Status (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Status is required"
}
```

*Invalid Status Value (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid status value. Must be one of: [OPEN, FULL, CLOSED, CANCELLED]"
}
```

*Invalid Status Transition (409 Conflict):*
```json
{
  "status": false,
  "code": 409,
  "message": "Cannot cancel a batch with confirmed appointments"
}
```

### Delete Batch

Deletes an existing batch.

**Request:**
```
DELETE /manage/batches/{id}
Accept: application/json
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the batch |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Batch deleted successfully"
}
```

**Error Responses:**

*Batch Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Batch not found"
}
```

*Cannot Delete Batch (409 Conflict):*
```json
{
  "status": false,
  "code": 409,
  "message": "Cannot delete a batch with appointments"
}
```
      {
        "id": 102,
        "date": "2023-10-15",
        "startTime": "10:30",
        "endTime": "11:30",
        "capacity": 50,
        "remaining": 0,
        "status": "FULL",
        "createdBy": "admin",
        "createdAt": "2023-10-01T10:15:00Z"
      }
    ],
    "totalElements": 2,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

**Success Response - Empty Result (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "content": [],
    "totalElements": 0,
    "totalPages": 0,
    "size": 10,
    "number": 0
  }
}
```

**Error Responses:**

*Unauthorized Access (401 Unauthorized):*
```json
{
  "status": false,
  "code": 401,
  "message": "Unauthorized access"
}
```

*Invalid Page Size (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Page size must not be greater than 100"
}
```

### Get Batch Details (Admin)

Retrieves the details of a specific batch, including appointments.

**Request:**
```
GET /manage/batches/{id}
Accept: application/json
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the batch |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Success",
  "data": {
    "id": 101,
    "date": "2023-10-15",
    "startTime": "09:00",
    "endTime": "10:00",
    "capacity": 50,
    "remaining": 20,
    "status": "OPEN",
    "description": "Morning batch",
    "notes": "Please arrive 15 minutes early",
    "createdBy": "admin",
    "createdAt": "2023-10-01T10:00:00Z",
    "updatedBy": "admin",
    "updatedAt": "2023-10-05T14:30:00Z",
    "appointments": [
      {
        "id": 201,
        "orderNo": "ORDER123456789012",
        "contactName": "John Doe",
        "contactPhone": "13800138000",
        "status": "CONFIRMED",
        "createdAt": "2023-10-10T09:15:00Z"
      },
      {
        "id": 202,
        "orderNo": "ORDER987654321098",
        "contactName": "Jane Smith",
        "contactPhone": "13900139000",
        "status": "CONFIRMED",
        "createdAt": "2023-10-10T10:30:00Z"
      }
    ]
  }
}
```

**Error Responses:**

*Batch Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Batch not found"
}
```

### Create Batch

Creates a new batch.

**Request:**
```
POST /manage/batches
Content-Type: application/json
Accept: application/json

{
  "date": "2023-11-01",
  "startTime": "14:00",
  "endTime": "15:00",
  "capacity": 50,
  "description": "Afternoon batch",
  "notes": "Please arrive 15 minutes early"
}
```

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| date | String | Yes | The date of the batch | Must be in YYYY-MM-DD format |
| startTime | String | Yes | The start time of the batch | Must be in HH:MM format (24-hour) |
| endTime | String | Yes | The end time of the batch | Must be in HH:MM format (24-hour) |
| capacity | Integer | Yes | The maximum capacity of the batch | |
| description | String | No | A description of the batch | |
| notes | String | No | Additional notes for the batch | |

**Success Response (201 Created):**
```json
{
  "status": true,
  "code": 201,
  "message": "Batch created successfully",
  "data": {
    "id": 103,
    "date": "2023-11-01",
    "startTime": "14:00",
    "endTime": "15:00",
    "capacity": 50,
    "remaining": 50,
    "status": "OPEN",
    "description": "Afternoon batch",
    "notes": "Please arrive 15 minutes early",
    "createdBy": "admin",
    "createdAt": "2023-10-15T10:00:00Z"
  }
}
```

**Error Responses:**

*Missing Required Fields (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Missing required fields"
}
```

*Invalid Date Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid date format. Use YYYY-MM-DD"
}
```

*Invalid Time Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid time format. Use HH:MM (24-hour format)"
}
```

*Batch Already Exists (409 Conflict):*
```json
{
  "status": false,
  "code": 409,
  "message": "Batch already exists for this date and time"
}
```

### Update Batch

Updates an existing batch.

**Request:**
```
PUT /manage/batches/{id}
Content-Type: application/json
Accept: application/json

{
  "startTime": "09:30",
  "endTime": "10:30",
  "capacity": 60,
  "description": "Updated morning batch",
  "notes": "Updated notes"
}
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the batch |

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| startTime | String | No | The start time of the batch | Must be in HH:MM format (24-hour) if provided |
| endTime | String | No | The end time of the batch | Must be in HH:MM format (24-hour) if provided |
| capacity | Integer | No | The maximum capacity of the batch | |
| status | String | No | The status of the batch | Must be a valid status value if provided |
| description | String | No | A description of the batch | |
| notes | String | No | Additional notes for the batch | |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Batch updated successfully",
  "data": {
    "id": 101,
    "date": "2023-10-15",
    "startTime": "09:30",
    "endTime": "10:30",
    "capacity": 60,
    "remaining": 20,
    "status": "OPEN",
    "description": "Updated morning batch",
    "notes": "Updated notes",
    "updatedBy": "admin",
    "updatedAt": "2023-10-15T14:30:00Z"
  }
}
```

**Error Responses:**

*Batch Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Batch not found"
}
```

*Invalid Time Format (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid start time format. Use HH:MM (24-hour format)"
}
```

### Update Batch Status

Updates the status of an existing batch.

**Request:**
```
PUT /manage/batches/{id}/status
Content-Type: application/json
Accept: application/json

{
  "status": "CLOSED"
}
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the batch |

**Request Body Parameters:**
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| status | String | Yes | The new status of the batch | Must be a valid status value |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Batch status updated successfully",
  "data": {
    "id": 101,
    "status": "CLOSED",
    "updatedBy": "admin",
    "updatedAt": "2023-10-15T14:30:00Z"
  }
}
```

**Error Responses:**

*Batch Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Batch not found"
}
```

*Missing Status (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Status is required"
}
```

*Invalid Status Value (400 Bad Request):*
```json
{
  "status": false,
  "code": 400,
  "message": "Invalid status value. Must be one of: [OPEN, FULL, CLOSED, CANCELLED]"
}
```

*Invalid Status Transition (409 Conflict):*
```json
{
  "status": false,
  "code": 409,
  "message": "Cannot cancel a batch with confirmed appointments"
}
```

### Delete Batch

Deletes an existing batch.

**Request:**
```
DELETE /manage/batches/{id}
Accept: application/json
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | Long | Yes | The unique identifier of the batch |

**Success Response (200 OK):**
```json
{
  "status": true,
  "code": 200,
  "message": "Batch deleted successfully"
}
```

**Error Responses:**

*Batch Not Found (404 Not Found):*
```json
{
  "status": false,
  "code": 404,
  "message": "Batch not found"
}
```

*Cannot Delete Batch (409 Conflict):*
```json
{
  "status": false,
  "code": 409,
  "message": "Cannot delete a batch with appointments"
}
```
