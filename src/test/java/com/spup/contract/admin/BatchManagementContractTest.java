package com.spup.contract.admin;

import io.restassured.module.mockmvc.RestAssuredMockMvc;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * Contract tests for the batch management API
 */
public class BatchManagementContractTest {

    private MockMvc mockMvc;
    private BatchManagementController batchManagementController;

    @BeforeEach
    public void setup() {
        batchManagementController = new BatchManagementController();
        mockMvc = MockMvcBuilders.standaloneSetup(batchManagementController).build();
        RestAssuredMockMvc.mockMvc(mockMvc);
    }

    // Success scenarios

    @Test
    public void shouldListBatches() throws Exception {
        mockMvc.perform(get("/manage/batches")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].id").value(101))
                .andExpect(jsonPath("$.data.content[0].date").value("2023-10-15"))
                .andExpect(jsonPath("$.data.content[0].startTime").value("09:00"))
                .andExpect(jsonPath("$.data.content[0].endTime").value("10:00"))
                .andExpect(jsonPath("$.data.content[0].capacity").value(50))
                .andExpect(jsonPath("$.data.content[0].remaining").value(20))
                .andExpect(jsonPath("$.data.content[0].status").value("OPEN"))
                .andExpect(jsonPath("$.data.content[0].createdBy").value("admin"))
                .andExpect(jsonPath("$.data.content[1].id").value(102))
                .andExpect(jsonPath("$.data.content[1].status").value("FULL"))
                .andExpect(jsonPath("$.data.totalElements").value(2))
                .andExpect(jsonPath("$.data.totalPages").value(1))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));
    }

    @Test
    public void shouldListBatchesWithFilters() throws Exception {
        mockMvc.perform(get("/manage/batches")
                .param("date", "2023-11-01")
                .param("status", "CLOSED")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].date").value("2023-11-01"))
                .andExpect(jsonPath("$.data.content[0].status").value("CLOSED"))
                .andExpect(jsonPath("$.data.totalElements").value(2));
    }

    @Test
    public void shouldGetBatchDetails() throws Exception {
        mockMvc.perform(get("/manage/batches/101")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.id").value(101))
                .andExpect(jsonPath("$.data.date").value("2023-10-15"))
                .andExpect(jsonPath("$.data.startTime").value("09:00"))
                .andExpect(jsonPath("$.data.endTime").value("10:00"))
                .andExpect(jsonPath("$.data.capacity").value(50))
                .andExpect(jsonPath("$.data.remaining").value(20))
                .andExpect(jsonPath("$.data.status").value("OPEN"))
                .andExpect(jsonPath("$.data.description").value("Morning batch"))
                .andExpect(jsonPath("$.data.notes").value("Please arrive 15 minutes early"))
                .andExpect(jsonPath("$.data.createdBy").value("admin"))
                .andExpect(jsonPath("$.data.createdAt").isString())
                .andExpect(jsonPath("$.data.updatedBy").value("admin"))
                .andExpect(jsonPath("$.data.updatedAt").isString())
                .andExpect(jsonPath("$.data.appointments").isArray())
                .andExpect(jsonPath("$.data.appointments.length()").value(2))
                .andExpect(jsonPath("$.data.appointments[0].id").value(201))
                .andExpect(jsonPath("$.data.appointments[0].orderNo").value("ORDER123456789012"))
                .andExpect(jsonPath("$.data.appointments[0].contactName").value("John Doe"))
                .andExpect(jsonPath("$.data.appointments[0].contactPhone").value("13800138000"))
                .andExpect(jsonPath("$.data.appointments[0].status").value("CONFIRMED"));
    }

    @Test
    public void shouldCreateBatch() throws Exception {
        String requestBody = "{\n" +
                "  \"date\": \"2023-11-01\",\n" +
                "  \"startTime\": \"14:00\",\n" +
                "  \"endTime\": \"15:00\",\n" +
                "  \"capacity\": 50,\n" +
                "  \"description\": \"Afternoon batch\",\n" +
                "  \"notes\": \"Please arrive 15 minutes early\"\n" +
                "}";

        mockMvc.perform(post("/manage/batches")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(201))
                .andExpect(jsonPath("$.message").value("Batch created successfully"))
                .andExpect(jsonPath("$.data.id").value(103))
                .andExpect(jsonPath("$.data.date").value("2023-11-01"))
                .andExpect(jsonPath("$.data.startTime").value("14:00"))
                .andExpect(jsonPath("$.data.endTime").value("15:00"))
                .andExpect(jsonPath("$.data.capacity").value(50))
                .andExpect(jsonPath("$.data.remaining").value(50))
                .andExpect(jsonPath("$.data.status").value("OPEN"))
                .andExpect(jsonPath("$.data.description").value("Afternoon batch"))
                .andExpect(jsonPath("$.data.notes").value("Please arrive 15 minutes early"))
                .andExpect(jsonPath("$.data.createdBy").value("admin"))
                .andExpect(jsonPath("$.data.createdAt").isString());
    }

    @Test
    public void shouldUpdateBatch() throws Exception {
        String requestBody = "{\n" +
                "  \"startTime\": \"09:30\",\n" +
                "  \"endTime\": \"10:30\",\n" +
                "  \"capacity\": 60,\n" +
                "  \"description\": \"Updated morning batch\",\n" +
                "  \"notes\": \"Updated notes\"\n" +
                "}";

        mockMvc.perform(put("/manage/batches/101")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Batch updated successfully"))
                .andExpect(jsonPath("$.data.id").value(101))
                .andExpect(jsonPath("$.data.startTime").value("09:30"))
                .andExpect(jsonPath("$.data.endTime").value("10:30"))
                .andExpect(jsonPath("$.data.capacity").value(60))
                .andExpect(jsonPath("$.data.description").value("Updated morning batch"))
                .andExpect(jsonPath("$.data.notes").value("Updated notes"))
                .andExpect(jsonPath("$.data.updatedBy").value("admin"))
                .andExpect(jsonPath("$.data.updatedAt").isString());
    }

    @Test
    public void shouldUpdateBatchStatus() throws Exception {
        String requestBody = "{\n" +
                "  \"status\": \"CLOSED\"\n" +
                "}";

        mockMvc.perform(put("/manage/batches/101/status")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Batch status updated successfully"))
                .andExpect(jsonPath("$.data.id").value(101))
                .andExpect(jsonPath("$.data.status").value("CLOSED"))
                .andExpect(jsonPath("$.data.updatedBy").value("admin"))
                .andExpect(jsonPath("$.data.updatedAt").isString());
    }

    @Test
    public void shouldDeleteBatch() throws Exception {
        mockMvc.perform(delete("/manage/batches/101")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Batch deleted successfully"));
    }

    // Edge cases

    @Test
    public void shouldReturnEmptyListWhenNoAvailableBatchesForDate() throws Exception {
        mockMvc.perform(get("/manage/batches")
                .param("date", "2023-12-25")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content").isEmpty())
                .andExpect(jsonPath("$.data.totalElements").value(0))
                .andExpect(jsonPath("$.data.totalPages").value(0));
    }

    // Error scenarios

    @Test
    public void shouldReturnNotFoundWhenBatchDoesNotExist() throws Exception {
        mockMvc.perform(get("/manage/batches/999")
                .accept(APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("Batch not found"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenAccessingWithInvalidCredentials() throws Exception {
        mockMvc.perform(get("/manage/batches")
                .param("page", "-1")  // Trigger unauthorized scenario
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnBadRequestWhenPageSizeExceedsLimit() throws Exception {
        mockMvc.perform(get("/manage/batches")
                .param("page", "0")
                .param("size", "101")  // Exceeds limit of 100
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Page size must not be greater than 100"));
    }

    @Test
    public void shouldReturnBadRequestWhenCreateBatchMissingRequiredFields() throws Exception {
        String requestBody = "{\n" +
                "  \"date\": \"2023-11-01\",\n" +
                "  \"startTime\": \"14:00\"\n" +
                "}";

        mockMvc.perform(post("/manage/batches")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Missing required fields"));
    }

    @Test
    public void shouldReturnBadRequestWhenCreateBatchInvalidDateFormat() throws Exception {
        String requestBody = "{\n" +
                "  \"date\": \"2023/11/01\",\n" +
                "  \"startTime\": \"14:00\",\n" +
                "  \"endTime\": \"15:00\",\n" +
                "  \"capacity\": 50\n" +
                "}";

        mockMvc.perform(post("/manage/batches")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid date format. Use YYYY-MM-DD"));
    }

    @Test
    public void shouldReturnBadRequestWhenCreateBatchInvalidTimeFormat() throws Exception {
        String requestBody = "{\n" +
                "  \"date\": \"2023-11-01\",\n" +
                "  \"startTime\": \"14:00\",\n" +
                "  \"endTime\": \"3:00 PM\",\n" +
                "  \"capacity\": 50\n" +
                "}";

        mockMvc.perform(post("/manage/batches")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid time format. Use HH:MM (24-hour format)"));
    }

    @Test
    public void shouldReturnConflictWhenCreateBatchAlreadyExists() throws Exception {
        String requestBody = "{\n" +
                "  \"date\": \"2023-10-15\",\n" +
                "  \"startTime\": \"09:00\",\n" +
                "  \"endTime\": \"10:00\",\n" +
                "  \"capacity\": 50\n" +
                "}";

        mockMvc.perform(post("/manage/batches")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(409))
                .andExpect(jsonPath("$.message").value("Batch already exists for this date and time"));
    }

    @Test
    public void shouldReturnBadRequestWhenUpdateBatchInvalidTimeFormat() throws Exception {
        String requestBody = "{\n" +
                "  \"startTime\": \"9 AM\"\n" +
                "}";

        mockMvc.perform(put("/manage/batches/101")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid start time format. Use HH:MM (24-hour format)"));
    }

    @Test
    public void shouldReturnBadRequestWhenUpdateBatchStatusMissingStatus() throws Exception {
        String requestBody = "{}";

        mockMvc.perform(put("/manage/batches/101/status")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Status is required"));
    }

    @Test
    public void shouldReturnBadRequestWhenUpdateBatchStatusInvalidValue() throws Exception {
        String requestBody = "{\n" +
                "  \"status\": \"INVALID_STATUS\"\n" +
                "}";

        mockMvc.perform(put("/manage/batches/101/status")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid status value. Must be one of: [OPEN, FULL, CLOSED, CANCELLED]"));
    }

    @Test
    public void shouldReturnConflictWhenUpdateBatchStatusInvalidTransition() throws Exception {
        String requestBody = "{\n" +
                "  \"status\": \"CANCELLED\"\n" +
                "}";

        mockMvc.perform(put("/manage/batches/102/status")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(409))
                .andExpect(jsonPath("$.message").value("Cannot cancel a batch with confirmed appointments"));
    }

    @Test
    public void shouldReturnConflictWhenDeleteBatchWithAppointments() throws Exception {
        mockMvc.perform(delete("/manage/batches/102")
                .accept(APPLICATION_JSON))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(409))
                .andExpect(jsonPath("$.message").value("Cannot delete a batch with appointments"));
    }
}
