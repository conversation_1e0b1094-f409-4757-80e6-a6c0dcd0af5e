package com.spup.contract.app;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test controller for batch contracts
 */
@RestController
@RequestMapping("/api/batches")
public class BatchController {

    @GetMapping
    public ResponseEntity<Map<String, Object>> listBatches(
            @RequestParam(required = false) String date,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        // Simulate unauthorized access
        if (page < 0) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate invalid parameters
        if (size > 100) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Page size must not be greater than 100");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate empty result for a specific date
        if ("2023-12-25".equals(date)) {
            Map<String, Object> data = new HashMap<>();
            data.put("content", new ArrayList<>());
            data.put("totalElements", 0);
            data.put("totalPages", 0);
            data.put("size", size);
            data.put("number", page);

            Map<String, Object> response = new HashMap<>();
            response.put("status", true);
            response.put("code", 200);
            response.put("message", "Success");
            response.put("data", data);

            return ResponseEntity.ok(response);
        }

        // Normal response with batches
        List<Map<String, Object>> content = new ArrayList<>();
        
        Map<String, Object> batch1 = new HashMap<>();
        batch1.put("id", 101);
        batch1.put("date", date != null ? date : "2023-10-15");
        batch1.put("startTime", "09:00");
        batch1.put("endTime", "10:00");
        batch1.put("capacity", 50);
        batch1.put("remaining", 20);
        batch1.put("status", "OPEN");
        content.add(batch1);
        
        Map<String, Object> batch2 = new HashMap<>();
        batch2.put("id", 102);
        batch2.put("date", date != null ? date : "2023-10-15");
        batch2.put("startTime", "10:30");
        batch2.put("endTime", "11:30");
        batch2.put("capacity", 50);
        batch2.put("remaining", 0);
        batch2.put("status", "FULL");
        content.add(batch2);

        Map<String, Object> data = new HashMap<>();
        data.put("content", content);
        data.put("totalElements", 2);
        data.put("totalPages", 1);
        data.put("size", size);
        data.put("number", page);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getBatchDetails(@PathVariable Long id) {
        // Simulate not found scenario
        if (id == 999) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Batch not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        Map<String, Object> response = new HashMap<>();

        Map<String, Object> data = new HashMap<>();
        data.put("id", id);
        data.put("date", "2023-10-15");
        data.put("startTime", "09:00");
        data.put("endTime", "10:00");
        data.put("capacity", 50);
        data.put("remaining", 20);
        data.put("status", "OPEN");
        data.put("description", "Morning batch");
        data.put("notes", "Please arrive 15 minutes early");

        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/available-dates")
    public ResponseEntity<Map<String, Object>> getAvailableDates(
            @RequestParam(required = false) String month) {
        
        Map<String, Object> response = new HashMap<>();
        List<String> dates = new ArrayList<>();
        
        // If month is not provided, return dates for the current month
        LocalDate now = LocalDate.now();
        String currentMonth = month != null ? month : now.getYear() + "-" + String.format("%02d", now.getMonthValue());
        
        // Generate some sample dates
        dates.add(currentMonth + "-01");
        dates.add(currentMonth + "-05");
        dates.add(currentMonth + "-10");
        dates.add(currentMonth + "-15");
        dates.add(currentMonth + "-20");
        dates.add(currentMonth + "-25");

        Map<String, Object> data = new HashMap<>();
        data.put("dates", dates);
        data.put("month", currentMonth);

        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }
}
