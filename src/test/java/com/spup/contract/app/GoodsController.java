package com.spup.contract.app;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test controller for goods contracts
 */
@RestController
@RequestMapping("/api/goods")
public class GoodsController {

    @GetMapping
    public ResponseEntity<Map<String, Object>> listGoods(
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        // Simulate unauthorized access
        if (page < 0) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 401);
            response.put("message", "Unauthorized access");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        // Simulate invalid parameters
        if (size > 100) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Page size must not be greater than 100");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        // Simulate empty result for a specific category
        if ("non-existent".equals(category)) {
            Map<String, Object> data = new HashMap<>();
            data.put("content", new ArrayList<>());
            data.put("totalElements", 0);
            data.put("totalPages", 0);
            data.put("size", size);
            data.put("number", page);

            Map<String, Object> response = new HashMap<>();
            response.put("status", true);
            response.put("code", 200);
            response.put("message", "Success");
            response.put("data", data);

            return ResponseEntity.ok(response);
        }

        // Normal response with goods
        List<Map<String, Object>> content = new ArrayList<>();
        
        Map<String, Object> goods1 = new HashMap<>();
        goods1.put("id", 201);
        goods1.put("name", "Museum Guide Book");
        goods1.put("description", "Comprehensive guide to the museum");
        goods1.put("price", 29.99);
        goods1.put("category", category != null ? category : "books");
        goods1.put("imageUrl", "https://example.com/images/guide-book.jpg");
        goods1.put("inStock", true);
        content.add(goods1);
        
        Map<String, Object> goods2 = new HashMap<>();
        goods2.put("id", 202);
        goods2.put("name", "Museum Souvenir Mug");
        goods2.put("description", "Ceramic mug with museum logo");
        goods2.put("price", 19.99);
        goods2.put("category", category != null ? category : "souvenirs");
        goods2.put("imageUrl", "https://example.com/images/mug.jpg");
        goods2.put("inStock", true);
        content.add(goods2);

        Map<String, Object> data = new HashMap<>();
        data.put("content", content);
        data.put("totalElements", 2);
        data.put("totalPages", 1);
        data.put("size", size);
        data.put("number", page);

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getGoodsDetails(@PathVariable Long id) {
        // Simulate not found scenario
        if (id == 999) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 404);
            response.put("message", "Goods not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        Map<String, Object> response = new HashMap<>();

        Map<String, Object> data = new HashMap<>();
        data.put("id", id);
        data.put("name", "Museum Guide Book");
        data.put("description", "Comprehensive guide to the museum with detailed information about all exhibits");
        data.put("price", 29.99);
        data.put("category", "books");
        data.put("imageUrl", "https://example.com/images/guide-book.jpg");
        data.put("inStock", true);
        data.put("stockQuantity", 150);
        data.put("createdAt", "2023-01-15T10:00:00Z");
        data.put("updatedAt", "2023-05-20T14:30:00Z");

        // Add related items
        List<Map<String, Object>> relatedItems = new ArrayList<>();
        Map<String, Object> relatedItem1 = new HashMap<>();
        relatedItem1.put("id", 202);
        relatedItem1.put("name", "Museum Souvenir Mug");
        relatedItem1.put("price", 19.99);
        relatedItems.add(relatedItem1);
        
        Map<String, Object> relatedItem2 = new HashMap<>();
        relatedItem2.put("id", 203);
        relatedItem2.put("name", "Museum Map");
        relatedItem2.put("price", 9.99);
        relatedItems.add(relatedItem2);
        
        data.put("relatedItems", relatedItems);

        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/categories")
    public ResponseEntity<Map<String, Object>> getCategories() {
        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> categories = new ArrayList<>();
        
        Map<String, Object> category1 = new HashMap<>();
        category1.put("id", 1);
        category1.put("name", "books");
        category1.put("displayName", "Books");
        categories.add(category1);
        
        Map<String, Object> category2 = new HashMap<>();
        category2.put("id", 2);
        category2.put("name", "souvenirs");
        category2.put("displayName", "Souvenirs");
        categories.add(category2);
        
        Map<String, Object> category3 = new HashMap<>();
        category3.put("id", 3);
        category3.put("name", "clothing");
        category3.put("displayName", "Clothing");
        categories.add(category3);

        Map<String, Object> data = new HashMap<>();
        data.put("categories", categories);

        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @PostMapping("/search")
    public ResponseEntity<Map<String, Object>> searchGoods(@RequestBody Map<String, Object> request) {
        // Validate required fields
        if (!request.containsKey("keyword")) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", false);
            response.put("code", 400);
            response.put("message", "Keyword is required");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        String keyword = (String) request.get("keyword");
        String category = request.containsKey("category") ? (String) request.get("category") : null;
        
        // Simulate no results found
        if ("nonexistent".equalsIgnoreCase(keyword)) {
            Map<String, Object> data = new HashMap<>();
            data.put("content", new ArrayList<>());
            data.put("totalElements", 0);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", true);
            response.put("code", 200);
            response.put("message", "No results found");
            response.put("data", data);
            
            return ResponseEntity.ok(response);
        }

        // Normal response with search results
        List<Map<String, Object>> content = new ArrayList<>();
        
        Map<String, Object> goods1 = new HashMap<>();
        goods1.put("id", 201);
        goods1.put("name", "Museum Guide Book");
        goods1.put("description", "Comprehensive guide to the museum");
        goods1.put("price", 29.99);
        goods1.put("category", "books");
        goods1.put("imageUrl", "https://example.com/images/guide-book.jpg");
        content.add(goods1);
        
        // Only add second item if category filter matches or is not specified
        if (category == null || "souvenirs".equals(category)) {
            Map<String, Object> goods2 = new HashMap<>();
            goods2.put("id", 202);
            goods2.put("name", "Museum Souvenir Mug");
            goods2.put("description", "Ceramic mug with museum logo");
            goods2.put("price", 19.99);
            goods2.put("category", "souvenirs");
            goods2.put("imageUrl", "https://example.com/images/mug.jpg");
            content.add(goods2);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("content", content);
        data.put("totalElements", content.size());
        data.put("keyword", keyword);
        if (category != null) {
            data.put("category", category);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("status", true);
        response.put("code", 200);
        response.put("message", "Success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }
}
