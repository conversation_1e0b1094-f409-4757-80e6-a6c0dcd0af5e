package com.spup.contract.app;

import io.restassured.module.mockmvc.RestAssuredMockMvc;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * Contract tests for the user API
 */
public class UserContractTest {

    private MockMvc mockMvc;
    private UserController userController;

    @BeforeEach
    public void setup() {
        userController = new UserController();
        mockMvc = MockMvcBuilders.standaloneSetup(userController).build();
        RestAssuredMockMvc.mockMvc(mockMvc);
    }

    // Success scenarios

    @Test
    public void shouldLoginSuccessfully() throws Exception {
        String requestBody = "{\n" +
                "  \"username\": \"johndoe\",\n" +
                "  \"password\": \"password123\"\n" +
                "}";

        mockMvc.perform(post("/api/users/login")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Login successful"))
                .andExpect(jsonPath("$.data.userId").value(1001))
                .andExpect(jsonPath("$.data.username").value("johndoe"))
                .andExpect(jsonPath("$.data.token").isString())
                .andExpect(jsonPath("$.data.expiresIn").value(3600));
    }

    @Test
    public void shouldRegisterSuccessfully() throws Exception {
        String requestBody = "{\n" +
                "  \"username\": \"newuser\",\n" +
                "  \"password\": \"password123\",\n" +
                "  \"phone\": \"13800138000\",\n" +
                "  \"email\": \"<EMAIL>\"\n" +
                "}";

        mockMvc.perform(post("/api/users/register")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(201))
                .andExpect(jsonPath("$.message").value("Registration successful"))
                .andExpect(jsonPath("$.data.userId").value(1001))
                .andExpect(jsonPath("$.data.username").value("newuser"))
                .andExpect(jsonPath("$.data.phone").value("13800138000"))
                .andExpect(jsonPath("$.data.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.data.createdAt").isString());
    }

    @Test
    public void shouldGetUserProfile() throws Exception {
        mockMvc.perform(get("/api/users/profile")
                .header("Authorization", "Bearer valid-token")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.userId").value(1001))
                .andExpect(jsonPath("$.data.username").value("johndoe"))
                .andExpect(jsonPath("$.data.fullName").value("John Doe"))
                .andExpect(jsonPath("$.data.phone").value("13800138000"))
                .andExpect(jsonPath("$.data.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.data.avatar").isString())
                .andExpect(jsonPath("$.data.createdAt").isString())
                .andExpect(jsonPath("$.data.lastLogin").isString());
    }

    @Test
    public void shouldUpdateUserProfile() throws Exception {
        String requestBody = "{\n" +
                "  \"fullName\": \"John Smith\",\n" +
                "  \"phone\": \"13900139000\",\n" +
                "  \"email\": \"<EMAIL>\"\n" +
                "}";

        mockMvc.perform(put("/api/users/profile")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Profile updated successfully"))
                .andExpect(jsonPath("$.data.userId").value(1001))
                .andExpect(jsonPath("$.data.username").value("johndoe"))
                .andExpect(jsonPath("$.data.fullName").value("John Smith"))
                .andExpect(jsonPath("$.data.phone").value("13900139000"))
                .andExpect(jsonPath("$.data.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.data.updatedAt").isString());
    }

    @Test
    public void shouldChangePasswordSuccessfully() throws Exception {
        String requestBody = "{\n" +
                "  \"currentPassword\": \"password123\",\n" +
                "  \"newPassword\": \"newpassword123\"\n" +
                "}";

        mockMvc.perform(post("/api/users/change-password")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Password changed successfully"));
    }

    @Test
    public void shouldGetUserOrders() throws Exception {
        mockMvc.perform(get("/api/users/orders")
                .header("Authorization", "Bearer valid-token")
                .param("page", "0")
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content[0].id").value(5001))
                .andExpect(jsonPath("$.data.content[0].orderNo").value("ORDER123456789012"))
                .andExpect(jsonPath("$.data.content[0].type").value("APPOINTMENT"))
                .andExpect(jsonPath("$.data.content[0].status").value("CONFIRMED"))
                .andExpect(jsonPath("$.data.content[1].id").value(5002))
                .andExpect(jsonPath("$.data.content[1].type").value("GOODS"))
                .andExpect(jsonPath("$.data.content[1].status").value("PAID"))
                .andExpect(jsonPath("$.data.content[1].amount").value(49.98))
                .andExpect(jsonPath("$.data.totalElements").value(2))
                .andExpect(jsonPath("$.data.totalPages").value(1))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));
    }

    // Edge cases

    @Test
    public void shouldReturnEmptyOrdersListWhenNoOrders() throws Exception {
        mockMvc.perform(get("/api/users/orders")
                .header("Authorization", "Bearer valid-token")
                .param("page", "11")  // Page with no results
                .param("size", "10")
                .accept(APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("Success"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content").isEmpty())
                .andExpect(jsonPath("$.data.totalElements").value(0))
                .andExpect(jsonPath("$.data.totalPages").value(0));
    }

    // Error scenarios

    @Test
    public void shouldReturnUnauthorizedWhenMissingToken() throws Exception {
        mockMvc.perform(get("/api/users/profile")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Unauthorized access"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenInvalidToken() throws Exception {
        mockMvc.perform(get("/api/users/profile")
                .header("Authorization", "Bearer invalid")
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Invalid token"));
    }

    @Test
    public void shouldReturnBadRequestWhenLoginMissingCredentials() throws Exception {
        String requestBody = "{\n" +
                "  \"username\": \"johndoe\"\n" +
                "}";

        mockMvc.perform(post("/api/users/login")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Username and password are required"));
    }

    @Test
    public void shouldReturnUnauthorizedWhenLoginInvalidCredentials() throws Exception {
        String requestBody = "{\n" +
                "  \"username\": \"invalid\",\n" +
                "  \"password\": \"password123\"\n" +
                "}";

        mockMvc.perform(post("/api/users/login")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("Invalid username or password"));
    }

    @Test
    public void shouldReturnBadRequestWhenRegisterMissingFields() throws Exception {
        String requestBody = "{\n" +
                "  \"username\": \"newuser\",\n" +
                "  \"password\": \"password123\"\n" +
                "}";

        mockMvc.perform(post("/api/users/register")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Missing required fields"));
    }

    @Test
    public void shouldReturnBadRequestWhenRegisterInvalidPhoneFormat() throws Exception {
        String requestBody = "{\n" +
                "  \"username\": \"newuser\",\n" +
                "  \"password\": \"password123\",\n" +
                "  \"phone\": \"12345\",\n" +
                "  \"email\": \"<EMAIL>\"\n" +
                "}";

        mockMvc.perform(post("/api/users/register")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid phone number format"));
    }

    @Test
    public void shouldReturnBadRequestWhenRegisterInvalidEmailFormat() throws Exception {
        String requestBody = "{\n" +
                "  \"username\": \"newuser\",\n" +
                "  \"password\": \"password123\",\n" +
                "  \"phone\": \"13800138000\",\n" +
                "  \"email\": \"invalid-email\"\n" +
                "}";

        mockMvc.perform(post("/api/users/register")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid email format"));
    }

    @Test
    public void shouldReturnConflictWhenRegisterUsernameExists() throws Exception {
        String requestBody = "{\n" +
                "  \"username\": \"existing\",\n" +
                "  \"password\": \"password123\",\n" +
                "  \"phone\": \"13800138000\",\n" +
                "  \"email\": \"<EMAIL>\"\n" +
                "}";

        mockMvc.perform(post("/api/users/register")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(409))
                .andExpect(jsonPath("$.message").value("Username already exists"));
    }

    @Test
    public void shouldReturnBadRequestWhenUpdateProfileInvalidPhoneFormat() throws Exception {
        String requestBody = "{\n" +
                "  \"phone\": \"12345\"\n" +
                "}";

        mockMvc.perform(put("/api/users/profile")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Invalid phone number format"));
    }

    @Test
    public void shouldReturnBadRequestWhenChangePasswordMissingFields() throws Exception {
        String requestBody = "{\n" +
                "  \"currentPassword\": \"password123\"\n" +
                "}";

        mockMvc.perform(post("/api/users/change-password")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Current password and new password are required"));
    }

    @Test
    public void shouldReturnBadRequestWhenChangePasswordIncorrectCurrentPassword() throws Exception {
        String requestBody = "{\n" +
                "  \"currentPassword\": \"incorrect\",\n" +
                "  \"newPassword\": \"newpassword123\"\n" +
                "}";

        mockMvc.perform(post("/api/users/change-password")
                .header("Authorization", "Bearer valid-token")
                .contentType(APPLICATION_JSON)
                .content(requestBody)
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Current password is incorrect"));
    }

    @Test
    public void shouldReturnBadRequestWhenOrdersPageSizeExceedsLimit() throws Exception {
        mockMvc.perform(get("/api/users/orders")
                .header("Authorization", "Bearer valid-token")
                .param("page", "0")
                .param("size", "101")  // Exceeds limit of 100
                .accept(APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Page size must not be greater than 100"));
    }
}
