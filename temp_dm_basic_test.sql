-- 达梦数据库最基础测试

-- 测试1: 检查数据库基本信息
SELECT VERSION() AS database_version;
SELECT USER AS current_user;
SELECT SYSDATE AS current_time;

-- 测试2: 创建最简单的表
CREATE TABLE simple_test (
    id INT PRIMARY KEY,
    name VARCHAR(50)
);

-- 测试3: 插入数据
INSERT INTO simple_test VALUES (1, 'test');

-- 测试4: 查询数据
SELECT * FROM simple_test;

-- 测试5: 删除表
DROP TABLE simple_test;

-- 测试6: 测试section保留字
CREATE TABLE section_test (
    id INT PRIMARY KEY,
    "section" VARCHAR(100)
);

INSERT INTO section_test VALUES (1, 'section_value');
SELECT id, "section" FROM section_test;
DROP TABLE section_test;

SELECT '基础测试全部通过' AS result;
