-- 达梦数据库清理脚本
-- 如果表已存在，请先执行此脚本清理

-- 手动删除可能存在的表
-- 如果表不存在，会报错，但可以忽略

DROP TABLE art_center_info;
DROP TABLE app_activity;
DROP TABLE app_customer;
DROP TABLE app_config;
DROP TABLE app_appointment_order;
DROP TABLE app_appointment_suborder;
DROP TABLE app_appointment_instructions;
DROP TABLE app_appointment_analysis;
DROP TABLE app_batch;
DROP TABLE app_batch_set;
DROP TABLE app_batch_set_detail;
DROP TABLE activity_info;
DROP TABLE activity_round_info;
DROP TABLE app_manage_user;
DROP TABLE app_manage_role;
DROP TABLE app_operate_log;
DROP TABLE black_list;
DROP TABLE one_time_tasks;
DROP TABLE hibernate_sequence;
DROP TABLE app_comments;
DROP TABLE app_customer_contacts;
DROP TABLE app_media;
DROP TABLE app_surrounding_goods;
DROP TABLE app_workday;
DROP TABLE exhibition_info;

SELECT '清理完成，可以忽略上面的错误消息' AS result;
