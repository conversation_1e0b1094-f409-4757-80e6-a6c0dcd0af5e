-- 达梦数据库转换 - 最终验证脚本

-- 验证所有表是否创建成功
SELECT '=== 最终验证 - 所有表统计 ===' AS verification;

-- 统计所有创建的表
SELECT 
    '总表数:' AS info,
    COUNT(*) AS count
FROM USER_TABLES 
WHERE TABLE_NAME IN (
    'ART_CENTER_INFO', 'APP_ACTIVITY', 'APP_CUSTOMER', 'APP_CONFIG',
    'APP_APPOINTMENT_ORDER', 'APP_APPOINTMENT_SUBORDER', 'APP_APPOINTMENT_INSTRUCTIONS', 'APP_APPOINTMENT_ANALYSIS',
    'APP_BATCH', 'APP_BATCH_SET', 'APP_BATCH_SET_DETAIL', 'ACTIVITY_INFO', 'ACTIVITY_ROUND_INFO',
    'APP_MANAGE_USER', 'APP_MANAGE_ROLE', 'APP_OPERATE_LOG', 'BLACK_LIST', 'ONE_TIME_TASKS', 'HIBERNATE_SEQUENCE',
    'APP_COMMENTS', 'APP_CUSTOMER_CONTACTS', 'APP_MEDIA', 'APP_SURROUNDING_GOODS', 'APP_WORKDAY', 'EXHIBITION_INFO'
);

-- 详细列出所有表
SELECT 
    TABLE_NAME,
    'EXISTS' AS status
FROM USER_TABLES 
WHERE TABLE_NAME IN (
    'ART_CENTER_INFO', 'APP_ACTIVITY', 'APP_CUSTOMER', 'APP_CONFIG',
    'APP_APPOINTMENT_ORDER', 'APP_APPOINTMENT_SUBORDER', 'APP_APPOINTMENT_INSTRUCTIONS', 'APP_APPOINTMENT_ANALYSIS',
    'APP_BATCH', 'APP_BATCH_SET', 'APP_BATCH_SET_DETAIL', 'ACTIVITY_INFO', 'ACTIVITY_ROUND_INFO',
    'APP_MANAGE_USER', 'APP_MANAGE_ROLE', 'APP_OPERATE_LOG', 'BLACK_LIST', 'ONE_TIME_TASKS', 'HIBERNATE_SEQUENCE',
    'APP_COMMENTS', 'APP_CUSTOMER_CONTACTS', 'APP_MEDIA', 'APP_SURROUNDING_GOODS', 'APP_WORKDAY', 'EXHIBITION_INFO'
)
ORDER BY TABLE_NAME;

-- 验证section保留字处理
SELECT '=== section保留字验证 ===' AS verification;

-- 测试art_center_info表的section列
SELECT 
    id, 
    address, 
    "section" AS section_column
FROM art_center_info 
WHERE id = 1;

-- 测试插入和查询section列
INSERT INTO art_center_info (id, address, "section") VALUES (999, '验证地址', '验证区域');
SELECT COUNT(*) AS section_test_count FROM art_center_info WHERE "section" = '验证区域';

-- 清理验证数据
DELETE FROM art_center_info WHERE id = 999;

-- 数据完整性验证
SELECT '=== 数据完整性验证 ===' AS verification;

SELECT 
    'art_center_info' AS table_name,
    COUNT(*) AS record_count
FROM art_center_info
UNION ALL
SELECT 'app_activity', COUNT(*) FROM app_activity
UNION ALL
SELECT 'app_customer', COUNT(*) FROM app_customer
UNION ALL
SELECT 'app_config', COUNT(*) FROM app_config
UNION ALL
SELECT 'app_appointment_order', COUNT(*) FROM app_appointment_order;

-- 最终成功消息
SELECT 
    '达梦数据库转换完成！' AS result,
    SYSDATE AS completion_time,
    '所有表创建成功，section保留字已正确处理' AS note;
