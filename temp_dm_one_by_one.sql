-- 达梦数据库逐句测试
-- 请逐句复制执行，不要一次性执行整个文件

-- 第1句: 测试基本查询
SELECT 1;

-- 第2句: 测试时间函数
SELECT SYSDATE;

-- 第3句: 创建简单表
CREATE TABLE t1 (id INT);

-- 第4句: 插入数据
INSERT INTO t1 VALUES (1);

-- 第5句: 查询数据
SELECT * FROM t1;

-- 第6句: 删除表
DROP TABLE t1;

-- 第7句: 创建带主键的表
CREATE TABLE t2 (id INT PRIMARY KEY);

-- 第8句: 插入数据
INSERT INTO t2 VALUES (1);

-- 第9句: 查询数据
SELECT * FROM t2;

-- 第10句: 删除表
DROP TABLE t2;

-- 第11句: 测试section保留字
CREATE TABLE t3 (id INT, "section" VARCHAR(100));

-- 第12句: 插入数据
INSERT INTO t3 VALUES (1, 'test_section');

-- 第13句: 查询section列
SELECT id, "section" FROM t3;

-- 第14句: 删除表
DROP TABLE t3;

-- 第15句: 创建art_center_info表
CREATE TABLE art_center_info (
  id BIGINT PRIMARY KEY,
  address VARCHAR(255),
  "section" VARCHAR(255)
);

-- 第16句: 插入数据
INSERT INTO art_center_info VALUES (1, '测试地址', '测试区域');

-- 第17句: 查询数据
SELECT * FROM art_center_info;

-- 第18句: 测试section列查询
SELECT id, address, "section" FROM art_center_info;
