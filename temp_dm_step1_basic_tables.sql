-- 达梦数据库转换 - 步骤1: 基础核心表
-- 使用双引号方案处理section保留字

-- 清理可能存在的表（达梦数据库兼容方式）
BEGIN
  EXECUTE IMMEDIATE 'DROP TABLE art_center_info';
EXCEPTION
  WHEN OTHERS THEN NULL;
END;
/

BEGIN
  EXECUTE IMMEDIATE 'DROP TABLE app_activity';
EXCEPTION
  WHEN OTHERS THEN NULL;
END;
/

BEGIN
  EXECUTE IMMEDIATE 'DROP TABLE app_customer';
EXCEPTION
  WHEN OTHERS THEN NULL;
END;
/

BEGIN
  EXECUTE IMMEDIATE 'DROP TABLE app_config';
EXCEPTION
  WHEN OTHERS THEN NULL;
END;
/

-- 表1: art_center_info (包含section保留字)
CREATE TABLE art_center_info (
  id BIGINT NOT NULL,
  address VARCHAR(255) DEFAULT NULL,
  introduction VARCHAR(2000) DEFAULT NULL,
  metro VARCHAR(255) DEFAULT NULL,
  open_time VARCHAR(255) DEFAULT NULL,
  pic_info VARCHAR(255) DEFAULT NULL,
  "section" VARCHAR(255) DEFAULT NULL,
  traffic VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表2: app_activity
CREATE TABLE app_activity (
  id BIGINT NOT NULL,
  address VARCHAR(255) DEFAULT NULL,
  content VARCHAR(255) DEFAULT NULL,
  conver_picture VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  end_time DATETIME DEFAULT NULL,
  sort_order INT DEFAULT NULL,
  start_time DATETIME DEFAULT NULL,
  activity_status INT DEFAULT NULL,
  title VARCHAR(255) DEFAULT NULL,
  activity_type TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表3: app_customer
CREATE TABLE app_customer (
  id BIGINT NOT NULL,
  breaked_num INT DEFAULT NULL,
  breaked_total_num INT DEFAULT NULL,
  card_category TINYINT DEFAULT NULL,
  card_no VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  customer_id VARCHAR(255) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  job VARCHAR(255) DEFAULT NULL,
  mini_openid VARCHAR(255) DEFAULT NULL,
  phone VARCHAR(255) DEFAULT NULL,
  real_name VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  user_avatar_src VARCHAR(255) DEFAULT NULL,
  user_birthdate VARCHAR(255) DEFAULT NULL,
  user_gender TINYINT DEFAULT NULL,
  user_name VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表4: app_config
CREATE TABLE app_config (
  id BIGINT NOT NULL,
  group_no VARCHAR(255) DEFAULT NULL,
  rule_name VARCHAR(255) DEFAULT NULL,
  rule_value VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 测试插入数据
INSERT INTO art_center_info (id, address, "section") VALUES (1, '测试地址', '测试区域');
INSERT INTO app_activity (id, title, activity_status, activity_type) VALUES (1, '测试活动', 1, 1);
INSERT INTO app_customer (id, real_name, phone) VALUES (1, '测试用户', '13800138000');
INSERT INTO app_config (id, group_no, rule_name, rule_value) VALUES (1, 'test', 'test_rule', 'test_value');

-- 验证数据
SELECT '步骤1验证:' AS step, 'art_center_info' AS table_name, COUNT(*) AS count FROM art_center_info
UNION ALL
SELECT '步骤1验证:', 'app_activity', COUNT(*) FROM app_activity
UNION ALL
SELECT '步骤1验证:', 'app_customer', COUNT(*) FROM app_customer
UNION ALL
SELECT '步骤1验证:', 'app_config', COUNT(*) FROM app_config;

SELECT '步骤1完成 - 基础核心表创建成功' AS result;
