-- 达梦数据库转换 - 步骤1: 基础核心表（简化版）
-- 使用双引号方案处理section保留字

-- 表1: art_center_info (包含section保留字)
CREATE TABLE art_center_info (
  id BIGINT NOT NULL,
  address VARCHAR(255),
  introduction VARCHAR(2000),
  metro VARCHAR(255),
  open_time VARCHAR(255),
  pic_info VARCHAR(255),
  "section" VARCHAR(255),
  traffic VARCHAR(255),
  PRIMARY KEY (id)
);

-- 表2: app_activity
CREATE TABLE app_activity (
  id BIGINT NOT NULL,
  address VARCHAR(255),
  content VARCHAR(255),
  conver_picture VARCHAR(255),
  create_by VARCHAR(255),
  create_time DATETIME,
  deleted TINYINT,
  end_time DATETIME,
  sort_order INT,
  start_time DATETIME,
  activity_status INT,
  title VARCHAR(255),
  activity_type TINYINT,
  update_by VARCHAR(255),
  update_time DATETIME,
  PRIMARY KEY (id)
);

-- 表3: app_customer
CREATE TABLE app_customer (
  id BIGINT NOT NULL,
  breaked_num INT,
  breaked_total_num INT,
  card_category TINYINT,
  card_no VARCHAR(255),
  create_by VARCHAR(255),
  create_time DATETIME,
  customer_id VARCHAR(255),
  deleted TINYINT,
  job VARCHAR(255),
  mini_openid VARCHAR(255),
  phone VARCHAR(255),
  real_name VARCHAR(255),
  unionid VARCHAR(255),
  update_by VARCHAR(255),
  update_time DATETIME,
  user_avatar_src VARCHAR(255),
  user_birthdate VARCHAR(255),
  user_gender TINYINT,
  user_name VARCHAR(255),
  PRIMARY KEY (id)
);

-- 表4: app_config
CREATE TABLE app_config (
  id BIGINT NOT NULL,
  group_no VARCHAR(255),
  rule_name VARCHAR(255),
  rule_value VARCHAR(255),
  PRIMARY KEY (id)
);

-- 测试插入数据
INSERT INTO art_center_info (id, address, "section") VALUES (1, '测试地址', '测试区域');
INSERT INTO app_activity (id, title, activity_status, activity_type) VALUES (1, '测试活动', 1, 1);
INSERT INTO app_customer (id, real_name, phone) VALUES (1, '测试用户', '13800138000');
INSERT INTO app_config (id, group_no, rule_name, rule_value) VALUES (1, 'test', 'test_rule', 'test_value');

-- 验证数据
SELECT 'art_center_info' AS table_name, COUNT(*) AS count FROM art_center_info;
SELECT 'app_activity' AS table_name, COUNT(*) AS count FROM app_activity;
SELECT 'app_customer' AS table_name, COUNT(*) AS count FROM app_customer;
SELECT 'app_config' AS table_name, COUNT(*) AS count FROM app_config;

-- 验证section列
SELECT id, address, "section" FROM art_center_info WHERE id = 1;

SELECT '步骤1完成 - 基础核心表创建成功' AS result;
