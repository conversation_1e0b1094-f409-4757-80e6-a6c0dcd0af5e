-- 达梦数据库转换 - 步骤2: 预约相关表

-- 表5: app_appointment_order
BEGIN
  EXECUTE IMMEDIATE 'DROP TABLE app_appointment_order';
EXCEPTION
  WHEN OTHERS THEN NULL;
END;
/
CREATE TABLE app_appointment_order (
  id BIGINT NOT NULL,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  order_category TINYINT DEFAULT NULL,
  order_no VARCHAR(255) DEFAULT NULL,
  order_remark VARCHAR(255) DEFAULT NULL,
  order_status SMALLINT DEFAULT NULL,
  owner_name VA<PERSON>HA<PERSON>(255) DEFAULT NULL,
  owner_phone VARCHAR(255) DEFAULT NULL,
  owner_unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表6: app_appointment_suborder
BEGIN
  EXECUTE IMMEDIATE 'DROP TABLE app_appointment_suborder';
EXCEPTION
  WHEN OTHERS THEN NULL;
END;
/
CREATE TABLE app_appointment_suborder (
  id BIGINT NOT NULL,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  contacts_idcard_category TINYINT DEFAULT NULL,
  contacts_idcard_no VARCHAR(255) DEFAULT NULL,
  contacts_name VARCHAR(255) DEFAULT NULL,
  contacts_phone VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  onwer_unionid VARCHAR(255) DEFAULT NULL,
  order_no VARCHAR(255) DEFAULT NULL,
  suborder_no VARCHAR(255) DEFAULT NULL,
  suborder_status SMALLINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表7: app_appointment_instructions
BEGIN
  EXECUTE IMMEDIATE 'DROP TABLE app_appointment_instructions';
EXCEPTION
  WHEN OTHERS THEN NULL;
END;
/
CREATE TABLE app_appointment_instructions (
  id BIGINT NOT NULL,
  admission_notice VARCHAR(255) DEFAULT NULL,
  audience_notice VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  visiting_instructions VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表8: app_appointment_analysis
DROP TABLE IF EXISTS app_appointment_analysis;
CREATE TABLE app_appointment_analysis (
  id BIGINT NOT NULL,
  analysis_date VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  item_checkin_am INT DEFAULT NULL,
  item_checkin_pm INT DEFAULT NULL,
  item_checkin_total INT DEFAULT NULL,
  item_reserve_am INT DEFAULT NULL,
  item_reserve_pm INT DEFAULT NULL,
  item_reserve_refund_active INT DEFAULT NULL,
  item_reserve_refund_passive INT DEFAULT NULL,
  item_reserve_total INT DEFAULT NULL,
  ticket_checkin_am INT DEFAULT NULL,
  ticket_checkin_pm INT DEFAULT NULL,
  ticket_checkin_total INT DEFAULT NULL,
  ticket_reserve_am INT DEFAULT NULL,
  ticket_reserve_pm INT DEFAULT NULL,
  ticket_reserve_refund_active INT DEFAULT NULL,
  ticket_reserve_refund_passive INT DEFAULT NULL,
  ticket_reserve_total INT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 测试插入数据
INSERT INTO app_appointment_order (id, order_no, owner_name) VALUES (1, 'ORDER001', '测试订单');
INSERT INTO app_appointment_suborder (id, suborder_no, contacts_name) VALUES (1, 'SUB001', '测试子订单');
INSERT INTO app_appointment_instructions (id, admission_notice) VALUES (1, '入场须知');
INSERT INTO app_appointment_analysis (id, analysis_date) VALUES (1, '2024-01-01');

-- 验证数据
SELECT '步骤2验证:' AS step, 'app_appointment_order' AS table_name, COUNT(*) AS count FROM app_appointment_order
UNION ALL
SELECT '步骤2验证:', 'app_appointment_suborder', COUNT(*) FROM app_appointment_suborder
UNION ALL
SELECT '步骤2验证:', 'app_appointment_instructions', COUNT(*) FROM app_appointment_instructions
UNION ALL
SELECT '步骤2验证:', 'app_appointment_analysis', COUNT(*) FROM app_appointment_analysis;

SELECT '步骤2完成 - 预约相关表创建成功' AS result;
