-- 达梦数据库转换 - 步骤3: 批次和活动相关表

-- 表9: app_batch
DROP TABLE IF EXISTS app_batch;
CREATE TABLE app_batch (
  id BIGINT NOT NULL,
  batch_category TINYINT DEFAULT NULL,
  batch_date VARCHAR(255) DEFAULT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_no VARCHAR(255) DEFAULT NULL,
  batch_remark VARCHAR(255) DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  batch_status TINYINT DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  ticket_remaining INT DEFAULT NULL,
  ticket_total INT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表10: app_batch_set
DROP TABLE IF EXISTS app_batch_set;
CREATE TABLE app_batch_set (
  id BIGINT NOT NULL,
  batch_category TINYINT DEFAULT NULL,
  batch_effect_end_time DATETIME DEFAULT NULL,
  batch_effect_start_time DATETIME DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  batch_status TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表11: app_batch_set_detail
DROP TABLE IF EXISTS app_batch_set_detail;
CREATE TABLE app_batch_set_detail (
  id BIGINT NOT NULL,
  batch_end_time VARCHAR(255) DEFAULT NULL,
  batch_set_id BIGINT DEFAULT NULL,
  batch_start_time VARCHAR(255) DEFAULT NULL,
  batch_ticket_total INT DEFAULT NULL,
  batch_type TINYINT DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  range_weeks VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表12: activity_info
DROP TABLE IF EXISTS activity_info;
CREATE TABLE activity_info (
  id BIGINT NOT NULL,
  activity_id VARCHAR(255) DEFAULT NULL,
  activity_name VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  deleted INT NOT NULL,
  end_date_time DATETIME NOT NULL,
  introduction_info VARCHAR(255) NOT NULL,
  others_info VARCHAR(255) DEFAULT NULL,
  pic_url VARCHAR(255) DEFAULT NULL,
  start_date_time DATETIME NOT NULL,
  activity_status VARCHAR(255) DEFAULT NULL,
  activity_type VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表13: activity_round_info
DROP TABLE IF EXISTS activity_round_info;
CREATE TABLE activity_round_info (
  id BIGINT NOT NULL,
  act_round_end_date_time DATETIME NOT NULL,
  act_round_id VARCHAR(255) DEFAULT NULL,
  act_round_info VARCHAR(255) DEFAULT NULL,
  act_round_max_submit_num INT NOT NULL,
  act_round_start_date_time DATETIME NOT NULL,
  act_round_submit_end_date_time DATETIME NOT NULL,
  act_round_submit_number INT DEFAULT NULL,
  act_round_submit_start_date_time DATETIME NOT NULL,
  activity_id VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on DATETIME DEFAULT NULL,
  deleted INT NOT NULL,
  other_info VARCHAR(255) DEFAULT NULL,
  round_status VARCHAR(255) DEFAULT NULL,
  round_type VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 测试插入数据
INSERT INTO app_batch (id, batch_no, batch_status) VALUES (1, 'BATCH001', 1);
INSERT INTO app_batch_set (id, batch_category) VALUES (1, 1);
INSERT INTO app_batch_set_detail (id, batch_set_id, batch_type) VALUES (1, 1, 1);
INSERT INTO activity_info (id, activity_name, deleted, end_date_time, introduction_info, start_date_time) 
VALUES (1, '测试活动', 0, SYSDATE, '活动介绍', SYSDATE);
INSERT INTO activity_round_info (id, act_round_end_date_time, act_round_max_submit_num, act_round_start_date_time, act_round_submit_end_date_time, act_round_submit_start_date_time, deleted) 
VALUES (1, SYSDATE, 100, SYSDATE, SYSDATE, SYSDATE, 0);

-- 验证数据
SELECT '步骤3验证:' AS step, 'app_batch' AS table_name, COUNT(*) AS count FROM app_batch
UNION ALL
SELECT '步骤3验证:', 'app_batch_set', COUNT(*) FROM app_batch_set
UNION ALL
SELECT '步骤3验证:', 'app_batch_set_detail', COUNT(*) FROM app_batch_set_detail
UNION ALL
SELECT '步骤3验证:', 'activity_info', COUNT(*) FROM activity_info
UNION ALL
SELECT '步骤3验证:', 'activity_round_info', COUNT(*) FROM activity_round_info;

SELECT '步骤3完成 - 批次和活动相关表创建成功' AS result;
