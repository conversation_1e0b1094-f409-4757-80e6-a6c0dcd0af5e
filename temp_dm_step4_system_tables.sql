-- 达梦数据库转换 - 步骤4: 系统和管理相关表

-- 表14: app_manage_user
DROP TABLE IF EXISTS app_manage_user;
CREATE TABLE app_manage_user (
  id BIGINT NOT NULL,
  create_by VA<PERSON>HA<PERSON>(255) DEFAULT NULL,
  create_time D<PERSON>ETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  login_name VA<PERSON>HA<PERSON>(255) DEFAULT NULL,
  login_password VARCHAR(255) DEFAULT NULL,
  real_name VARCHAR(255) DEFAULT NULL,
  role_id BIGINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表15: app_manage_role
DROP TABLE IF EXISTS app_manage_role;
CREATE TABLE app_manage_role (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  role_name VARCHAR(255) DEFAULT NULL,
  role_remark VARCHAR(255) DEFAULT NULL,
  update_by <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表16: app_operate_log
DROP TABLE IF EXISTS app_operate_log;
CREATE TABLE app_operate_log (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  operate_content VARCHAR(255) DEFAULT NULL,
  operate_type VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表17: black_list
DROP TABLE IF EXISTS black_list;
CREATE TABLE black_list (
  id BIGINT NOT NULL,
  category VARCHAR(255) DEFAULT NULL,
  locking_date_time DATETIME DEFAULT NULL,
  list_name VARCHAR(255) DEFAULT NULL,
  list_status VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  unlocking_date_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表18: one_time_tasks
DROP TABLE IF EXISTS one_time_tasks;
CREATE TABLE one_time_tasks (
  id VARCHAR(255) NOT NULL,
  created_at DATETIME DEFAULT NULL,
  deleted INT DEFAULT NULL,
  execute_time DATETIME DEFAULT NULL,
  task_status VARCHAR(255) DEFAULT NULL,
  task_class VARCHAR(255) DEFAULT NULL,
  task_data VARCHAR(255) DEFAULT NULL,
  task_type VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表19: hibernate_sequence
DROP TABLE IF EXISTS hibernate_sequence;
CREATE TABLE hibernate_sequence (
  next_val BIGINT DEFAULT NULL
);

-- 测试插入数据
INSERT INTO app_manage_user (id, login_name, real_name) VALUES (1, 'admin', '管理员');
INSERT INTO app_manage_role (id, role_name) VALUES (1, '超级管理员');
INSERT INTO app_operate_log (id, operate_content, operate_type) VALUES (1, '测试操作', '系统测试');
INSERT INTO black_list (id, list_name, list_status) VALUES (1, '测试黑名单', '有效');
INSERT INTO one_time_tasks (id, task_type, task_status) VALUES ('TASK001', '测试任务', '待执行');
INSERT INTO hibernate_sequence (next_val) VALUES (1000);

-- 验证数据
SELECT '步骤4验证:' AS step, 'app_manage_user' AS table_name, COUNT(*) AS count FROM app_manage_user
UNION ALL
SELECT '步骤4验证:', 'app_manage_role', COUNT(*) FROM app_manage_role
UNION ALL
SELECT '步骤4验证:', 'app_operate_log', COUNT(*) FROM app_operate_log
UNION ALL
SELECT '步骤4验证:', 'black_list', COUNT(*) FROM black_list
UNION ALL
SELECT '步骤4验证:', 'one_time_tasks', COUNT(*) FROM one_time_tasks
UNION ALL
SELECT '步骤4验证:', 'hibernate_sequence', COUNT(*) FROM hibernate_sequence;

SELECT '步骤4完成 - 系统和管理相关表创建成功' AS result;
