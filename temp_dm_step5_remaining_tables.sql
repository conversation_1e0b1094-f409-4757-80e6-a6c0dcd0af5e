-- 达梦数据库转换 - 步骤5: 剩余表

-- 表20: app_comments
DROP TABLE IF EXISTS app_comments;
CREATE TABLE app_comments (
  id BIGINT NOT NULL,
  content VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  customer VARCHAR(255) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  purpose VARCHAR(255) DEFAULT NULL,
  comment_status VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  visit_time VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表21: app_customer_contacts
DROP TABLE IF EXISTS app_customer_contacts;
CREATE TABLE app_customer_contacts (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  customer_id BIGINT DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  idcard_category TINYINT DEFAULT NULL,
  idcard_no VARCHAR(255) DEFAULT NULL,
  real_name VARCHAR(255) DEFAULT NULL,
  phone VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表22: app_media
DROP TABLE IF EXISTS app_media;
CREATE TABLE app_media (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  media_category TINYINT DEFAULT NULL,
  media_name VARCHAR(255) DEFAULT NULL,
  media_path VARCHAR(255) DEFAULT NULL,
  media_remark VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表23: app_surrounding_goods
DROP TABLE IF EXISTS app_surrounding_goods;
CREATE TABLE app_surrounding_goods (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  goods_category TINYINT DEFAULT NULL,
  goods_name VARCHAR(255) DEFAULT NULL,
  goods_pic VARCHAR(255) DEFAULT NULL,
  goods_price DECIMAL(10,2) DEFAULT NULL,
  goods_remark VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表24: app_workday
DROP TABLE IF EXISTS app_workday;
CREATE TABLE app_workday (
  id BIGINT NOT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_time DATETIME DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_time DATETIME DEFAULT NULL,
  work_date DATE DEFAULT NULL,
  work_status TINYINT DEFAULT NULL,
  PRIMARY KEY (id)
);

-- 表25: exhibition_info
DROP TABLE IF EXISTS exhibition_info;
CREATE TABLE exhibition_info (
  id BIGINT NOT NULL,
  exhibition_id VARCHAR(255) DEFAULT NULL,
  exhibition_status VARCHAR(255) DEFAULT NULL,
  exhibition_type VARCHAR(255) DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
);

-- 测试插入数据
INSERT INTO app_comments (id, content, comment_status) VALUES (1, '测试评论', '已审核');
INSERT INTO app_customer_contacts (id, real_name, phone) VALUES (1, '联系人', '13800138000');
INSERT INTO app_media (id, media_name, media_category) VALUES (1, '测试媒体', 1);
INSERT INTO app_surrounding_goods (id, goods_name, goods_price) VALUES (1, '测试商品', 99.99);
INSERT INTO app_workday (id, work_date, work_status) VALUES (1, SYSDATE, 1);
INSERT INTO exhibition_info (id, exhibition_id, deleted) VALUES (1, 'EXH001', 0);

-- 验证数据
SELECT '步骤5验证:' AS step, 'app_comments' AS table_name, COUNT(*) AS count FROM app_comments
UNION ALL
SELECT '步骤5验证:', 'app_customer_contacts', COUNT(*) FROM app_customer_contacts
UNION ALL
SELECT '步骤5验证:', 'app_media', COUNT(*) FROM app_media
UNION ALL
SELECT '步骤5验证:', 'app_surrounding_goods', COUNT(*) FROM app_surrounding_goods
UNION ALL
SELECT '步骤5验证:', 'app_workday', COUNT(*) FROM app_workday
UNION ALL
SELECT '步骤5验证:', 'exhibition_info', COUNT(*) FROM exhibition_info;

SELECT '步骤5完成 - 剩余表创建成功' AS result;
