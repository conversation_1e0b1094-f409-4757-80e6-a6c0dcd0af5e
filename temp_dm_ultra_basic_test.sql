-- 达梦数据库超基础测试

-- 测试1: 最简单的查询
SELECT 1 AS test_number;

-- 测试2: 当前时间
SELECT SYSDATE AS current_time;

-- 测试3: 创建最简单的表
CREATE TABLE test_table (
    id INT,
    name VARCHAR(50)
);

-- 测试4: 插入数据
INSERT INTO test_table VALUES (1, 'test');

-- 测试5: 查询数据
SELECT * FROM test_table;

-- 测试6: 删除表
DROP TABLE test_table;

-- 测试7: 测试section保留字
CREATE TABLE test_section (
    id INT,
    "section" VARCHAR(100)
);

INSERT INTO test_section VALUES (1, 'section_test');
SELECT * FROM test_section;
DROP TABLE test_section;

-- 成功消息
SELECT '超基础测试完成' AS result;
