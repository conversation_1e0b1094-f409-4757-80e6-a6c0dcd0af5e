--
-- Table structure for table activity_info
--

DROP TABLE IF EXISTS activity_info;
CREATE TABLE activity_info (
  id BIGINT NOT NULL,
  activity_id VARCHAR(255) DEFAULT NULL,
  activity_name VARCHAR(255) DEFAULT NULL,
  create_by VA<PERSON><PERSON><PERSON>(255) DEFAULT NULL,
  create_on TIMESTAMP(6) DEFAULT NULL,
  deleted INT NOT NULL,
  end_date_time TIMESTAMP(6) NOT NULL,
  introduction_info VARCHAR(255) NOT NULL,
  others_info VARCHAR(255) DEFAULT NULL,
  pic_url VARCHAR(255) DEFAULT NULL,
  start_date_time TIMESTAMP(6) NOT NULL,
  status VARCHAR(255) DEFAULT NULL,
  type VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
);

--
-- Table structure for table activity_round_info
--

DROP TABLE IF EXISTS activity_round_info;
CREATE TABLE activity_round_info (
  id BIGINT NOT NULL,
  act_round_end_date_time TIMESTAMP(6) NOT NULL,
  act_round_id VARCHAR(255) DEFAULT NULL,
  act_round_info VARCHAR(255) DEFAULT NULL,
  act_round_max_submit_num INT NOT NULL,
  act_round_start_date_time TIMESTAMP(6) NOT NULL,
  act_round_submit_end_date_time TIMESTAMP(6) NOT NULL,
  act_round_submit_number INT DEFAULT NULL,
  act_round_submit_start_date_time TIMESTAMP(6) NOT NULL,
  activity_id VARCHAR(255) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on TIMESTAMP(6) DEFAULT NULL,
  deleted INT NOT NULL,
  other_info VARCHAR(255) DEFAULT NULL,
  status VARCHAR(255) DEFAULT NULL,
  type VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
);

--
-- Table structure for table activity_submit_customer
--

DROP TABLE IF EXISTS activity_submit_customer;
CREATE TABLE activity_submit_customer (
  id BIGINT NOT NULL,
  act_round_id VARCHAR(255) DEFAULT NULL,
  age INT NOT NULL,
  check_in_date_time TIMESTAMP(6) DEFAULT NULL,
  create_by VARCHAR(255) DEFAULT NULL,
  create_on TIMESTAMP(6) DEFAULT NULL,
  gender INT NOT NULL,
  pass_string VARCHAR(255) DEFAULT NULL,
  pass_type VARCHAR(255) NOT NULL,
  phone_string VARCHAR(255) DEFAULT NULL,
  status VARCHAR(255) DEFAULT NULL,
  submit_id VARCHAR(255) DEFAULT NULL,
  type VARCHAR(255) DEFAULT NULL,
  unionid VARCHAR(255) DEFAULT NULL,
  update_by VARCHAR(255) DEFAULT NULL,
  update_on TIMESTAMP(6) DEFAULT NULL,
  username VARCHAR(255) DEFAULT NULL,
  deleted INT NOT NULL,
  PRIMARY KEY (id)
);

--
-- Table structure for table app_activity
--

DROP TABLE IF EXISTS app_activity;
CREATE TABLE app_activity (
  id BIGINT IDENTITY(1,1) NOT NULL,
  address VARCHAR(255) DEFAULT NULL, -- 活动地点
  content VARCHAR(255) DEFAULT NULL, -- 活动内容
  conver_picture VARCHAR(255) DEFAULT NULL, -- 活动封面图
  create_by VARCHAR(255) DEFAULT NULL,
  create_time TIMESTAMP(6) DEFAULT NULL,
  deleted TINYINT DEFAULT NULL,
  end_time TIMESTAMP(6) DEFAULT NULL, -- 活动结束时间
  sort INT DEFAULT NULL, -- 排序值
  start_time TIMESTAMP(6) DEFAULT NULL, -- 活动开始时间
  status INT DEFAULT NULL, -- 活动状态
  title VARCHAR(255) DEFAULT NULL, -- 活动标题
  type TINYINT DEFAULT NULL, -- 活动类型
  update_by VARCHAR(255) DEFAULT NULL,
  update_time TIMESTAMP(6) DEFAULT NULL,
  PRIMARY KEY (id)
);
