#!/usr/bin/env python3

import re
import sys
import os
import glob

def update_entity_class(file_path):
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if the file already has Lombok annotations
    has_lombok = re.search(r'import\s+lombok\.Getter', content) is not None
    
    # Replace wildcard imports with specific imports
    jpa_imports = [
        "import javax.persistence.Column;",
        "import javax.persistence.Entity;",
        "import javax.persistence.EntityListeners;",
        "import javax.persistence.GeneratedValue;",
        "import javax.persistence.GenerationType;",
        "import javax.persistence.Id;",
        "import javax.persistence.PrePersist;",
        "import javax.persistence.PreUpdate;",
        "import javax.persistence.Table;"
    ]
    
    # Replace javax.persistence.* with specific imports
    content = re.sub(
        r'import\s+javax\.persistence\.\*;',
        '\n'.join(jpa_imports),
        content
    )
    
    # Remove duplicate javax.persistence.Entity and javax.persistence.Table imports
    content = re.sub(
        r'import\s+javax\.persistence\.Entity;\s*\nimport\s+javax\.persistence\.Table;\s*\n',
        '',
        content
    )
    
    # Add Lombok imports if not already present
    if not has_lombok:
        lombok_imports = "\nimport lombok.Getter;\nimport lombok.Setter;"
        # Add after the last import
        content = re.sub(
            r'(import\s+[^;]+;\s*\n)(?!\s*import)',
            f'\\1{lombok_imports}\n',
            content
        )
    
    # Add Lombok annotations to the class if not already present
    if not has_lombok:
        content = re.sub(
            r'(@DynamicUpdate\s*\n@DynamicInsert\s*\n)public\s+class',
            '\\1@Getter\n@Setter\npublic class',
            content
        )
    
    # Remove getter and setter methods
    pattern = r'(\s+public\s+\w+\s+get\w+\(\)\s*\{[^}]*\}\s*\n\s*public\s+void\s+set\w+\([^)]*\)\s*\{[^}]*\}\s*\n)+'
    content = re.sub(pattern, '\n', content)
    
    # Fix extra blank lines
    content = re.sub(r'\n{3,}', '\n\n', content)
    
    # Write the updated content back to the file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Updated {file_path}")
    return True

def process_directory(directory_path):
    java_files = glob.glob(os.path.join(directory_path, "*.java"))
    for file_path in java_files:
        update_entity_class(file_path)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python update_entity_classes.py <directory_path>")
        sys.exit(1)
    
    directory_path = sys.argv[1]
    process_directory(directory_path)
