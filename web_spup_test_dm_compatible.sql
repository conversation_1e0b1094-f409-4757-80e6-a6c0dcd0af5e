-- MySQL dump 10.13  Distrib 8.0.42, for macos15.2 (arm64)
--
-- Host: localhost    Database: web_spup_test
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `activity_info`
--

DROP TABLE IF EXISTS `activity_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `activity_info` (
  `id` bigint NOT NULL,
  `activity_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `activity_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_on` datetime(6) DEFAULT NULL,
  `deleted` int NOT NULL,
  `end_date_time` datetime(6) NOT NULL,
  `introduction_info` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `others_info` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `pic_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `start_date_time` datetime(6) NOT NULL,
  `status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `type` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_on` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `activity_info`
--

LOCK TABLES `activity_info` WRITE;
/*!40000 ALTER TABLE `activity_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `activity_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `activity_round_info`
--

DROP TABLE IF EXISTS `activity_round_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `activity_round_info` (
  `id` bigint NOT NULL,
  `act_round_end_date_time` datetime(6) NOT NULL,
  `act_round_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `act_round_info` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `act_round_max_submit_num` int NOT NULL,
  `act_round_start_date_time` datetime(6) NOT NULL,
  `act_round_submit_end_date_time` datetime(6) NOT NULL,
  `act_round_submit_number` int DEFAULT NULL,
  `act_round_submit_start_date_time` datetime(6) NOT NULL,
  `activity_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_on` datetime(6) DEFAULT NULL,
  `deleted` int NOT NULL,
  `other_info` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `type` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_on` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `activity_round_info`
--

LOCK TABLES `activity_round_info` WRITE;
/*!40000 ALTER TABLE `activity_round_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `activity_round_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `activity_submit_customer`
--

DROP TABLE IF EXISTS `activity_submit_customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `activity_submit_customer` (
  `id` bigint NOT NULL,
  `act_round_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `age` int NOT NULL,
  `check_in_date_time` datetime(6) DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_on` datetime(6) DEFAULT NULL,
  `gender` int NOT NULL,
  `pass_string` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `pass_type` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `phone_string` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `submit_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `type` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_on` datetime(6) DEFAULT NULL,
  `username` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `deleted` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `activity_submit_customer`
--

LOCK TABLES `activity_submit_customer` WRITE;
/*!40000 ALTER TABLE `activity_submit_customer` DISABLE KEYS */;
/*!40000 ALTER TABLE `activity_submit_customer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_activity`
--

DROP TABLE IF EXISTS `app_activity`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_activity` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `address` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '活动地点',
  `content` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '活动内容',
  `conver_picture` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '活动封面图',
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `end_time` datetime(6) DEFAULT NULL COMMENT '活动结束时间',
  `sort` int DEFAULT NULL COMMENT '排序值',
  `start_time` datetime(6) DEFAULT NULL COMMENT '活动开始时间',
  `status` int DEFAULT NULL COMMENT '活动状态',
  `title` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '活动标题',
  `type` tinyint DEFAULT NULL COMMENT '活动类型',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_activity`
--

LOCK TABLES `app_activity` WRITE;
/*!40000 ALTER TABLE `app_activity` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_activity` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_activity_entry_rule`
--

DROP TABLE IF EXISTS `app_activity_entry_rule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_activity_entry_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `activity_id` bigint DEFAULT NULL COMMENT '活动id',
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `entry_end_time` datetime(6) DEFAULT NULL,
  `entry_limit` int DEFAULT NULL COMMENT '报名人数限制',
  `entry_start_time` datetime(6) DEFAULT NULL COMMENT '报名开始时间',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_activity_entry_rule`
--

LOCK TABLES `app_activity_entry_rule` WRITE;
/*!40000 ALTER TABLE `app_activity_entry_rule` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_activity_entry_rule` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_activity_entry_user`
--

DROP TABLE IF EXISTS `app_activity_entry_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_activity_entry_user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `activity_id` bigint DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '前端表，unionid',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `deleted` tinyint DEFAULT NULL,
  `ext_attr` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '扩展字段',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '前端表，unionid',
  `update_time` datetime(6) DEFAULT NULL COMMENT '修改时间',
  `user_gender` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户性别',
  `user_idcard` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户证件号',
  `user_idcard_type` tinyint DEFAULT NULL COMMENT '用户证件类型',
  `user_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户姓名',
  `user_phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户手机号',
  `user_unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户unionid',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_activity_entry_user`
--

LOCK TABLES `app_activity_entry_user` WRITE;
/*!40000 ALTER TABLE `app_activity_entry_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_activity_entry_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_appointment_analysis`
--

DROP TABLE IF EXISTS `app_appointment_analysis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_appointment_analysis` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `analysis_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `deleted` tinyint DEFAULT NULL COMMENT '逻辑删除，默认为0未删除，1已删除',
  `item_checkin_am` int DEFAULT NULL COMMENT '飞阅浦东上午核销数',
  `item_checkin_pm` int DEFAULT NULL COMMENT '飞阅浦东下午核销数',
  `item_checkin_total` int DEFAULT NULL COMMENT '飞阅浦东核销数',
  `item_reserve_am` int DEFAULT NULL COMMENT '飞阅浦东上午预约数',
  `item_reserve_pm` int DEFAULT NULL COMMENT '飞阅浦东下午预约数',
  `item_reserve_refund_active` int DEFAULT NULL COMMENT '飞阅浦东主动退票数',
  `item_reserve_refund_passive` int DEFAULT NULL COMMENT '飞阅浦东被动退票数',
  `item_reserve_total` int DEFAULT NULL COMMENT '飞阅浦东展现预约总数',
  `ticket_checkin_am` int DEFAULT NULL COMMENT '上午核销数',
  `ticket_checkin_pm` int DEFAULT NULL COMMENT '下午核销数',
  `ticket_checkin_total` int DEFAULT NULL COMMENT '门票核销总数',
  `ticket_reserve_am` int DEFAULT NULL COMMENT '上午预约数',
  `ticket_reserve_pm` int DEFAULT NULL COMMENT '下午预约数',
  `ticket_reserve_refund_active` int DEFAULT NULL COMMENT '主动退票数',
  `ticket_reserve_refund_passive` int DEFAULT NULL COMMENT '被动退票数',
  `ticket_reserve_total` int DEFAULT NULL COMMENT '门票预约总数',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者unionid',
  `update_time` datetime(6) DEFAULT NULL COMMENT '数据更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_appointment_analysis`
--

LOCK TABLES `app_appointment_analysis` WRITE;
/*!40000 ALTER TABLE `app_appointment_analysis` DISABLE KEYS */;
INSERT INTO `app_appointment_analysis` VALUES (1,'20250521','Object.prePersist','2025-05-21 15:59:59.896777',NULL,0,0,0,NULL,NULL,0,0,0,0,0,0,0,0,0,0,0,'Object.prePersist','2025-05-21 15:59:59.897266');
/*!40000 ALTER TABLE `app_appointment_analysis` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_appointment_instructions`
--

DROP TABLE IF EXISTS `app_appointment_instructions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_appointment_instructions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `admission_notice` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '入馆须知',
  `audience_notice` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '观众须知',
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid',
  `create_time` datetime(6) DEFAULT NULL COMMENT '生成时间',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者unionid',
  `update_time` datetime(6) DEFAULT NULL COMMENT '数据更新时间',
  `visiting_instructions` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参观须知',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_appointment_instructions`
--

LOCK TABLES `app_appointment_instructions` WRITE;
/*!40000 ALTER TABLE `app_appointment_instructions` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_appointment_instructions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_appointment_item_order`
--

DROP TABLE IF EXISTS `app_appointment_item_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_appointment_item_order` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `batch_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次日期',
  `batch_end_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次结束时间',
  `batch_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次编号',
  `batch_start_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次开始时间',
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid-可为用户、店员、店长、系统等',
  `create_time` datetime(6) DEFAULT NULL COMMENT '订单生成时间',
  `deleted` tinyint DEFAULT NULL COMMENT '逻辑删除，默认为0未删除，1已删除',
  `item` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `order_category` tinyint DEFAULT NULL COMMENT '订单分类，可能会有团体订单等其他的',
  `order_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单编号',
  `order_status` smallint DEFAULT NULL COMMENT '订单状态',
  `owner_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单所有者姓名',
  `owner_phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单所有者手机号',
  `owner_unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户小程序唯一id',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者unionid',
  `update_time` datetime(6) DEFAULT NULL COMMENT '数据更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_appointment_item_order`
--

LOCK TABLES `app_appointment_item_order` WRITE;
/*!40000 ALTER TABLE `app_appointment_item_order` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_appointment_item_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_appointment_item_suborder`
--

DROP TABLE IF EXISTS `app_appointment_item_suborder`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_appointment_item_suborder` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '子订单自增id',
  `batch_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_end_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_start_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `contacts_idcard_category` tinyint DEFAULT NULL,
  `contacts_idcard_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `contacts_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `contacts_phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid-可为用户、店员、店长、系统等',
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL COMMENT '逻辑删除，默认为0未删除，1已删除',
  `onwer_unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户小程序唯一id',
  `order_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单编号',
  `seat_no` tinyint DEFAULT NULL,
  `suborder_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子订单编号',
  `suborder_status` smallint DEFAULT NULL COMMENT '子订单状态-无符号int，状态可以为负数，表明异常状态；1：待支付，2: 待服务，4: 服务中，8: 已完成。 -1：售后中； -2: 已退款； -4: 已取消；',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid-可为用户、店员、店长、系统等',
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_appointment_item_suborder`
--

LOCK TABLES `app_appointment_item_suborder` WRITE;
/*!40000 ALTER TABLE `app_appointment_item_suborder` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_appointment_item_suborder` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_appointment_offline`
--

DROP TABLE IF EXISTS `app_appointment_offline`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_appointment_offline` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `appoint_batch_end_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `appoint_batch_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `appoint_batch_start_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `appoint_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `persons_num` int DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `visit_info` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_appointment_offline`
--

LOCK TABLES `app_appointment_offline` WRITE;
/*!40000 ALTER TABLE `app_appointment_offline` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_appointment_offline` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_appointment_order`
--

DROP TABLE IF EXISTS `app_appointment_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_appointment_order` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `batch_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次日期',
  `batch_end_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次结束时间',
  `batch_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次编号',
  `batch_start_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次开始时间',
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid-可为用户、店员、店长、系统等',
  `create_time` datetime(6) DEFAULT NULL COMMENT '订单生成时间',
  `deleted` tinyint DEFAULT NULL COMMENT '逻辑删除，默认为0未删除，1已删除',
  `order_category` tinyint DEFAULT NULL COMMENT '订单分类，可能会有团体订单等其他的',
  `order_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单编号',
  `order_remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `order_status` smallint DEFAULT NULL COMMENT '订单状态',
  `owner_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单所有者姓名',
  `owner_phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单所有者手机号',
  `owner_unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户小程序唯一id',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者unionid',
  `update_time` datetime(6) DEFAULT NULL COMMENT '数据更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_appointment_order`
--

LOCK TABLES `app_appointment_order` WRITE;
/*!40000 ALTER TABLE `app_appointment_order` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_appointment_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_appointment_order_temporary_exhibition`
--

DROP TABLE IF EXISTS `app_appointment_order_temporary_exhibition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_appointment_order_temporary_exhibition` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `batch_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_end_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_start_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `exhibition_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `exhibition_title` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `order_category` tinyint DEFAULT NULL,
  `order_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `order_status` smallint DEFAULT NULL,
  `owner_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `owner_phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `owner_unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_appointment_order_temporary_exhibition`
--

LOCK TABLES `app_appointment_order_temporary_exhibition` WRITE;
/*!40000 ALTER TABLE `app_appointment_order_temporary_exhibition` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_appointment_order_temporary_exhibition` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_appointment_personal_offline`
--

DROP TABLE IF EXISTS `app_appointment_personal_offline`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_appointment_personal_offline` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `person_num` int DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `visit_date` date DEFAULT NULL,
  `visit_fypd_batch` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_appointment_personal_offline`
--

LOCK TABLES `app_appointment_personal_offline` WRITE;
/*!40000 ALTER TABLE `app_appointment_personal_offline` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_appointment_personal_offline` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_appointment_suborder`
--

DROP TABLE IF EXISTS `app_appointment_suborder`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_appointment_suborder` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '子订单自增id',
  `batch_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_end_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_start_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `contacts_idcard_category` tinyint DEFAULT NULL,
  `contacts_idcard_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `contacts_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `contacts_phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid-可为用户、店员、店长、系统等',
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL COMMENT '逻辑删除，默认为0未删除，1已删除',
  `onwer_unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户小程序唯一id',
  `order_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单编号',
  `suborder_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子订单编号',
  `suborder_status` smallint DEFAULT NULL COMMENT '子订单状态-无符号int，状态可以为负数，表明异常状态；1：待支付，2: 待服务，4: 服务中，8: 已完成。 -1：售后中； -2: 已退款； -4: 已取消；',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid-可为用户、店员、店长、系统等',
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_appointment_suborder`
--

LOCK TABLES `app_appointment_suborder` WRITE;
/*!40000 ALTER TABLE `app_appointment_suborder` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_appointment_suborder` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_appointment_suborder_temporary_exhibition`
--

DROP TABLE IF EXISTS `app_appointment_suborder_temporary_exhibition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_appointment_suborder_temporary_exhibition` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `batch_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_end_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_start_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `contacts_idcard_category` tinyint DEFAULT NULL,
  `contacts_idcard_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `contacts_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `contacts_phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `onwer_unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `order_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `suborder_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `suborder_status` smallint DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_appointment_suborder_temporary_exhibition`
--

LOCK TABLES `app_appointment_suborder_temporary_exhibition` WRITE;
/*!40000 ALTER TABLE `app_appointment_suborder_temporary_exhibition` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_appointment_suborder_temporary_exhibition` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_appointment_team_offline`
--

DROP TABLE IF EXISTS `app_appointment_team_offline`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_appointment_team_offline` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `offline_info` tinyblob,
  `order_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ower_unit` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `visit_date` date DEFAULT NULL,
  `visitors_num` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_appointment_team_offline`
--

LOCK TABLES `app_appointment_team_offline` WRITE;
/*!40000 ALTER TABLE `app_appointment_team_offline` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_appointment_team_offline` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_appointment_team_order`
--

DROP TABLE IF EXISTS `app_appointment_team_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_appointment_team_order` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `batch_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次日期',
  `batch_end_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次结束时间',
  `batch_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次编号',
  `batch_start_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次开始时间',
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid-可为用户、店员、店长、系统等',
  `create_time` datetime(6) DEFAULT NULL COMMENT '订单生成时间',
  `deleted` tinyint DEFAULT NULL COMMENT '逻辑删除，默认为0未删除，1已删除',
  `exhibition_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `method` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `order_category` tinyint DEFAULT NULL COMMENT '订单分类，可能会有团体订单等其他的',
  `order_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单编号',
  `order_remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `order_status` smallint DEFAULT NULL COMMENT '订单状态',
  `ower_unit` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ower_unit_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `owner_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单所有者姓名',
  `owner_phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单所有者手机号',
  `owner_unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户小程序唯一id',
  `supply_info` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者unionid',
  `update_time` datetime(6) DEFAULT NULL COMMENT '数据更新时间',
  `visitors_num` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_appointment_team_order`
--

LOCK TABLES `app_appointment_team_order` WRITE;
/*!40000 ALTER TABLE `app_appointment_team_order` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_appointment_team_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_batch`
--

DROP TABLE IF EXISTS `app_batch`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_batch` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `batch_category` tinyint DEFAULT NULL COMMENT '场次分类',
  `batch_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次日期',
  `batch_end_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次结束时间',
  `batch_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次编号',
  `batch_remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次备注说明，便于后期管理',
  `batch_start_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场次开始时间',
  `batch_status` tinyint DEFAULT NULL COMMENT '场次状态',
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系统创建-给相关类名',
  `create_time` datetime(6) DEFAULT NULL COMMENT '记录创建时间',
  `deleted` tinyint DEFAULT NULL COMMENT '逻辑删除，默认为0未删除，1已删除',
  `ticket_remaining` int DEFAULT NULL COMMENT '剩余票数',
  `ticket_total` int DEFAULT NULL COMMENT '总计发放票数',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid-可为用户、管理员、系统等',
  `update_time` datetime(6) DEFAULT NULL COMMENT '记录修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_batch`
--

LOCK TABLES `app_batch` WRITE;
/*!40000 ALTER TABLE `app_batch` DISABLE KEYS */;
INSERT INTO `app_batch` VALUES (1,4,'20250518','1000','202505181000',NULL,'1000',0,'Object.prePersist','2025-05-17 13:48:57.055467',0,7,7,'Object.prePersist','2025-05-17 13:48:57.056240'),(2,4,'20250518','1100','202505181100',NULL,'1100',0,'Object.prePersist','2025-05-17 13:48:57.085623',0,7,7,'Object.prePersist','2025-05-17 13:48:57.085698'),(3,4,'20250518','1400','202505181400',NULL,'1400',0,'Object.prePersist','2025-05-17 13:48:57.087841',0,7,7,'Object.prePersist','2025-05-17 13:48:57.087882'),(4,4,'20250518','1500','202505181500',NULL,'1500',0,'Object.prePersist','2025-05-17 13:48:57.089879',0,7,7,'Object.prePersist','2025-05-17 13:48:57.089907'),(5,4,'20250521','1000','202505191000',NULL,'1000',0,'Object.prePersist','2025-05-18 00:04:59.930750',0,7,7,'Object.prePersist','2025-05-18 00:04:59.931763'),(6,4,'20250521','1100','202505191100',NULL,'1100',0,'Object.prePersist','2025-05-18 00:04:59.947261',0,7,7,'Object.prePersist','2025-05-18 00:04:59.947358'),(7,4,'20250521','1400','202505191400',NULL,'1400',0,'Object.prePersist','2025-05-18 00:04:59.949911',0,7,7,'Object.prePersist','2025-05-18 00:04:59.949964'),(8,4,'20250521','1500','202505191500',NULL,'1500',0,'Object.prePersist','2025-05-18 00:04:59.951981',0,7,7,'Object.preUpdate','2025-05-21 15:01:00.037234'),(9,4,'20250522','1000','202505221000',NULL,'1000',1,'Object.prePersist','2025-05-21 09:51:56.176804',0,7,7,'Object.prePersist','2025-05-21 09:51:56.177066'),(10,4,'20250522','1100','202505221100',NULL,'1100',1,'Object.prePersist','2025-05-21 09:51:56.180207',0,7,7,'Object.prePersist','2025-05-21 09:51:56.180400'),(11,4,'20250522','1400','202505221400',NULL,'1400',0,'Object.prePersist','2025-05-21 09:51:56.182381',0,7,7,'Object.prePersist','2025-05-21 09:51:56.182419'),(12,4,'20250522','1500','202505221500',NULL,'1500',0,'Object.prePersist','2025-05-21 09:51:56.184445',0,7,7,'Object.prePersist','2025-05-21 09:51:56.184497'),(13,32,'20250520','0900','202505200900',NULL,'0900',1,'Object.prePersist','2025-05-21 09:56:25.752364',0,7,7,'Object.prePersist','2025-05-21 09:56:25.752745'),(14,32,'20250526','0900','202505260900',NULL,'0900',1,'Object.prePersist','2025-05-21 09:56:33.113081',0,7,7,'Object.prePersist','2025-05-21 09:56:33.113585'),(15,4,'20250525','1000','202505251000',NULL,'1000',0,'Object.prePersist','2025-05-21 11:14:26.522516',0,7,7,'Object.prePersist','2025-05-21 11:14:26.522764'),(16,4,'20250525','1010','202505251010',NULL,'1010',0,'Object.prePersist','2025-05-21 11:14:26.534865',0,7,7,'Object.prePersist','2025-05-21 11:14:26.534941'),(17,4,'20250525','1020','202505251020',NULL,'1020',0,'Object.prePersist','2025-05-21 11:14:26.536338',0,7,7,'Object.prePersist','2025-05-21 11:14:26.536386'),(18,4,'20250525','1030','202505251030',NULL,'1030',0,'Object.prePersist','2025-05-21 11:14:26.537812',0,7,7,'Object.prePersist','2025-05-21 11:14:26.537846'),(19,4,'20250525','1100','202505251100',NULL,'1100',0,'Object.prePersist','2025-05-21 11:14:26.539115',0,7,7,'Object.prePersist','2025-05-21 11:14:26.539150'),(20,4,'20250525','1110','202505251110',NULL,'1110',0,'Object.prePersist','2025-05-21 11:14:26.540595',0,7,7,'Object.prePersist','2025-05-21 11:14:26.540627'),(21,4,'20250525','1120','202505251120',NULL,'1120',0,'Object.prePersist','2025-05-21 11:14:26.542436',0,7,7,'Object.prePersist','2025-05-21 11:14:26.542476'),(22,4,'20250525','1130','202505251130',NULL,'1130',0,'Object.prePersist','2025-05-21 11:14:26.543737',0,7,7,'Object.prePersist','2025-05-21 11:14:26.543768'),(23,4,'20250525','1200','202505251200',NULL,'1200',0,'Object.prePersist','2025-05-21 11:14:26.545716',0,7,7,'Object.prePersist','2025-05-21 11:14:26.545745'),(24,4,'20250525','1210','202505251210',NULL,'1210',0,'Object.prePersist','2025-05-21 11:14:26.547384',0,7,7,'Object.prePersist','2025-05-21 11:14:26.547421'),(25,4,'20250525','1220','202505251220',NULL,'1220',0,'Object.prePersist','2025-05-21 11:14:26.548844',0,7,7,'Object.prePersist','2025-05-21 11:14:26.548873'),(26,4,'20250525','1230','202505251230',NULL,'1230',0,'Object.prePersist','2025-05-21 11:14:26.550325',0,7,7,'Object.prePersist','2025-05-21 11:14:26.550353'),(27,4,'20250525','1300','202505251300',NULL,'1300',0,'Object.prePersist','2025-05-21 11:14:26.551471',0,7,7,'Object.prePersist','2025-05-21 11:14:26.551500'),(28,4,'20250525','1310','202505251310',NULL,'1310',0,'Object.prePersist','2025-05-21 11:14:26.553022',0,7,7,'Object.prePersist','2025-05-21 11:14:26.553058'),(29,4,'20250525','1320','202505251320',NULL,'1320',0,'Object.prePersist','2025-05-21 11:14:26.554358',0,7,7,'Object.prePersist','2025-05-21 11:14:26.554391'),(30,4,'20250525','1330','202505251330',NULL,'1330',0,'Object.prePersist','2025-05-21 11:14:26.556051',0,7,7,'Object.prePersist','2025-05-21 11:14:26.556081'),(31,4,'20250525','1400','202505251400',NULL,'1400',0,'Object.prePersist','2025-05-21 11:14:26.557241',0,7,7,'Object.prePersist','2025-05-21 11:14:26.557270'),(32,4,'20250525','1410','202505251410',NULL,'1410',0,'Object.prePersist','2025-05-21 11:14:26.558671',0,7,7,'Object.prePersist','2025-05-21 11:14:26.558701'),(33,4,'20250525','1420','202505251420',NULL,'1420',0,'Object.prePersist','2025-05-21 11:14:26.560269',0,7,7,'Object.prePersist','2025-05-21 11:14:26.560296'),(34,4,'20250525','1430','202505251430',NULL,'1430',0,'Object.prePersist','2025-05-21 11:14:26.562206',0,7,7,'Object.prePersist','2025-05-21 11:14:26.562234'),(35,4,'20250525','1500','202505251500',NULL,'1500',0,'Object.prePersist','2025-05-21 11:14:26.563885',0,7,7,'Object.prePersist','2025-05-21 11:14:26.563919'),(36,4,'20250525','1510','202505251510',NULL,'1510',0,'Object.prePersist','2025-05-21 11:14:26.565511',0,7,7,'Object.prePersist','2025-05-21 11:14:26.565542'),(37,4,'20250525','1520','202505251520',NULL,'1520',0,'Object.prePersist','2025-05-21 11:14:26.566939',0,7,7,'Object.prePersist','2025-05-21 11:14:26.566966'),(38,4,'20250525','1530','202505251530',NULL,'1530',0,'Object.prePersist','2025-05-21 11:14:26.568506',0,7,7,'Object.prePersist','2025-05-21 11:14:26.568533'),(39,4,'20250523','1000','202505231000',NULL,'1000',0,'Object.prePersist','2025-05-21 11:15:55.956929',0,7,7,'Object.prePersist','2025-05-21 11:15:55.957459'),(40,4,'20250523','1100','202505231100',NULL,'1100',0,'Object.prePersist','2025-05-21 11:15:55.962640',0,7,7,'Object.prePersist','2025-05-21 11:15:55.962729'),(41,4,'20250523','1400','202505231400',NULL,'1400',0,'Object.prePersist','2025-05-21 11:15:55.964273',0,7,7,'Object.prePersist','2025-05-21 11:15:55.964315'),(42,4,'20250523','1500','202505231500',NULL,'1500',0,'Object.prePersist','2025-05-21 11:15:55.966178',0,7,7,'Object.prePersist','2025-05-21 11:15:55.966268'),(43,4,'20250526','1000','202505261000',NULL,'1000',0,'Object.prePersist','2025-05-21 11:16:44.160764',0,7,7,'Object.prePersist','2025-05-21 11:16:44.161336'),(44,4,'20250526','1010','202505261010',NULL,'1010',0,'Object.prePersist','2025-05-21 11:16:44.167347',0,7,7,'Object.prePersist','2025-05-21 11:16:44.167539'),(45,4,'20250526','1020','202505261020',NULL,'1020',0,'Object.prePersist','2025-05-21 11:16:44.171017',0,7,7,'Object.prePersist','2025-05-21 11:16:44.171091'),(46,4,'20250526','1030','202505261030',NULL,'1030',0,'Object.prePersist','2025-05-21 11:16:44.173198',0,7,7,'Object.prePersist','2025-05-21 11:16:44.173231'),(47,4,'20250526','1100','202505261100',NULL,'1100',0,'Object.prePersist','2025-05-21 11:16:44.175116',0,7,7,'Object.prePersist','2025-05-21 11:16:44.175158'),(48,4,'20250526','1110','202505261110',NULL,'1110',0,'Object.prePersist','2025-05-21 11:16:44.176893',0,7,7,'Object.prePersist','2025-05-21 11:16:44.176922'),(49,4,'20250526','1120','202505261120',NULL,'1120',0,'Object.prePersist','2025-05-21 11:16:44.178636',0,7,7,'Object.prePersist','2025-05-21 11:16:44.178664'),(50,4,'20250526','1130','202505261130',NULL,'1130',0,'Object.prePersist','2025-05-21 11:16:44.180228',0,7,7,'Object.prePersist','2025-05-21 11:16:44.180272'),(51,4,'20250526','1200','202505261200',NULL,'1200',0,'Object.prePersist','2025-05-21 11:16:44.181725',0,7,7,'Object.prePersist','2025-05-21 11:16:44.181791'),(52,4,'20250526','1210','202505261210',NULL,'1210',0,'Object.prePersist','2025-05-21 11:16:44.183361',0,7,7,'Object.prePersist','2025-05-21 11:16:44.183413'),(53,4,'20250526','1220','202505261220',NULL,'1220',0,'Object.prePersist','2025-05-21 11:16:44.185330',0,7,7,'Object.prePersist','2025-05-21 11:16:44.185375'),(54,4,'20250526','1230','202505261230',NULL,'1230',0,'Object.prePersist','2025-05-21 11:16:44.187076',0,7,7,'Object.prePersist','2025-05-21 11:16:44.187111'),(55,4,'20250526','1300','202505261300',NULL,'1300',0,'Object.prePersist','2025-05-21 11:16:44.188851',0,7,7,'Object.prePersist','2025-05-21 11:16:44.188890'),(56,4,'20250526','1310','202505261310',NULL,'1310',0,'Object.prePersist','2025-05-21 11:16:44.189916',0,7,7,'Object.prePersist','2025-05-21 11:16:44.189945'),(57,4,'20250526','1320','202505261320',NULL,'1320',0,'Object.prePersist','2025-05-21 11:16:44.191415',0,7,7,'Object.prePersist','2025-05-21 11:16:44.191447'),(58,4,'20250526','1330','202505261330',NULL,'1330',0,'Object.prePersist','2025-05-21 11:16:44.192479',0,7,7,'Object.prePersist','2025-05-21 11:16:44.192506'),(59,4,'20250526','1400','202505261400',NULL,'1400',0,'Object.prePersist','2025-05-21 11:16:44.193860',0,7,7,'Object.prePersist','2025-05-21 11:16:44.193891'),(60,4,'20250526','1410','202505261410',NULL,'1410',0,'Object.prePersist','2025-05-21 11:16:44.195052',0,7,7,'Object.prePersist','2025-05-21 11:16:44.195101'),(61,4,'20250526','1420','202505261420',NULL,'1420',0,'Object.prePersist','2025-05-21 11:16:44.196109',0,7,7,'Object.prePersist','2025-05-21 11:16:44.196148'),(62,4,'20250526','1430','202505261430',NULL,'1430',0,'Object.prePersist','2025-05-21 11:16:44.197459',0,7,7,'Object.prePersist','2025-05-21 11:16:44.197489'),(63,4,'20250526','1500','202505261500',NULL,'1500',0,'Object.prePersist','2025-05-21 11:16:44.198687',0,7,7,'Object.prePersist','2025-05-21 11:16:44.198736'),(64,4,'20250526','1510','202505261510',NULL,'1510',0,'Object.prePersist','2025-05-21 11:16:44.201175',0,7,7,'Object.prePersist','2025-05-21 11:16:44.201243'),(65,4,'20250526','1520','202505261520',NULL,'1520',0,'Object.prePersist','2025-05-21 11:16:44.202840',0,7,7,'Object.prePersist','2025-05-21 11:16:44.202879'),(66,4,'20250526','1530','202505261530',NULL,'1530',0,'Object.prePersist','2025-05-21 11:16:44.203899',0,7,7,'Object.prePersist','2025-05-21 11:16:44.203941'),(67,4,'20250524','1000','202505241000',NULL,'1000',0,'Object.prePersist','2025-05-21 11:17:12.729999',0,7,7,'Object.prePersist','2025-05-21 11:17:12.730360'),(68,4,'20250524','1100','202505241100',NULL,'1100',0,'Object.prePersist','2025-05-21 11:17:12.733998',0,7,7,'Object.prePersist','2025-05-21 11:17:12.734089'),(69,4,'20250524','1400','202505241400',NULL,'1400',0,'Object.prePersist','2025-05-21 11:17:12.735293',0,7,7,'Object.prePersist','2025-05-21 11:17:12.735327'),(70,4,'20250524','1500','202505241500',NULL,'1500',0,'Object.prePersist','2025-05-21 11:17:12.736746',0,7,7,'Object.prePersist','2025-05-21 11:17:12.736781');
/*!40000 ALTER TABLE `app_batch` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_batch_set`
--

DROP TABLE IF EXISTS `app_batch_set`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_batch_set` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `batch_category` tinyint DEFAULT NULL COMMENT '场次类型',
  `batch_effect_end_time` datetime(6) DEFAULT NULL COMMENT '场次生效结束日期（空表示永久）',
  `batch_effect_start_time` datetime(6) DEFAULT NULL COMMENT '场次生效开始日期',
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `status` tinyint DEFAULT NULL COMMENT '规则当前状态',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_batch_set`
--

LOCK TABLES `app_batch_set` WRITE;
/*!40000 ALTER TABLE `app_batch_set` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_batch_set` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_batch_set_detail`
--

DROP TABLE IF EXISTS `app_batch_set_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_batch_set_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `batch_end_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_set_id` bigint DEFAULT NULL,
  `batch_start_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `batch_ticket_total` int DEFAULT NULL,
  `batch_type` tinyint DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `range_weeks` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_batch_set_detail`
--

LOCK TABLES `app_batch_set_detail` WRITE;
/*!40000 ALTER TABLE `app_batch_set_detail` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_batch_set_detail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_comments`
--

DROP TABLE IF EXISTS `app_comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_comments` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `content` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid-可为用户、店员、店长、系统等',
  `create_time` datetime(6) DEFAULT NULL COMMENT '订单生成时间',
  `customer` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL COMMENT '逻辑删除，默认为0未删除，1已删除',
  `purpose` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者unionid',
  `update_time` datetime(6) DEFAULT NULL COMMENT '数据更新时间',
  `visit_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_comments`
--

LOCK TABLES `app_comments` WRITE;
/*!40000 ALTER TABLE `app_comments` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_comments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_config`
--

DROP TABLE IF EXISTS `app_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_config` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `group_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `rule_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `rule_value` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_config`
--

LOCK TABLES `app_config` WRITE;
/*!40000 ALTER TABLE `app_config` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_customer`
--

DROP TABLE IF EXISTS `app_customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_customer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `breaked_num` int DEFAULT NULL,
  `breaked_total_num` int DEFAULT NULL,
  `card_category` tinyint DEFAULT NULL,
  `card_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系统创建-给相关类名',
  `create_time` datetime(6) DEFAULT NULL COMMENT '记录创建时间',
  `customer_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL COMMENT '逻辑删除，默认为0未删除，1已删除',
  `job` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `mini_openid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '小程序openid',
  `phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `real_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信唯一识别码',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid-可为用户、管理员、系统等',
  `update_time` datetime(6) DEFAULT NULL COMMENT '记录修改时间',
  `user_avatar_src` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户本地头像，可供用户替换头像；2作为备用',
  `user_birthdate` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户生日',
  `user_gender` tinyint DEFAULT NULL COMMENT '用户性别： 1， 男， 2 女， 0 未知',
  `user_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户姓名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_customer`
--

LOCK TABLES `app_customer` WRITE;
/*!40000 ALTER TABLE `app_customer` DISABLE KEYS */;
INSERT INTO `app_customer` VALUES (46,NULL,NULL,NULL,'310000888888888888','db','2023-01-17 12:46:24.000000','',0,'2','ouHc26KEbNy6EzNOTMv-RdR1SrnA','18521568005','沈吉','ojqzL0-hOlek3HMyLjhvKjTfDnnA','ojqzL07qjyrcO7orDG14Q4GFqBtE','2023-08-21 07:08:12.000000','https://thirdwx.qlogo.cn/mmopen/vi_32/DYAIOgq83eoFrEp6LwD0XbnHAeMPEsm1CicjHX3rNclGdofpwk72ibeYiaeibDvrbSnqpia6icjB9rTRUabEX5ZyJ2NQ/132','',0,'吉');
/*!40000 ALTER TABLE `app_customer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_customer_contacts`
--

DROP TABLE IF EXISTS `app_customer_contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_customer_contacts` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系统创建-给相关类名',
  `create_time` datetime(6) DEFAULT NULL COMMENT '记录创建时间',
  `deleted` tinyint DEFAULT NULL COMMENT '逻辑删除，默认为0未删除，1已删除',
  `idcard_category` tinyint DEFAULT NULL,
  `idcard_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ower_unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid-可为用户、管理员、系统等',
  `update_time` datetime(6) DEFAULT NULL COMMENT '记录修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_customer_contacts`
--

LOCK TABLES `app_customer_contacts` WRITE;
/*!40000 ALTER TABLE `app_customer_contacts` DISABLE KEYS */;
INSERT INTO `app_customer_contacts` VALUES (1,'Object.prePersist','2025-05-21 14:07:21.298775',NULL,1,'211302197807191217','安然','ojqzL0-hOlek3HMyLjhvKjTfDnnA','13651895278','sneaker','Object.prePersist','2025-05-21 14:07:21.299190'),(2,'Object.prePersist','2025-05-21 14:07:25.426653',NULL,1,'211302197807191217','安然','ojqzL0-hOlek3HMyLjhvKjTfDnnA','13651895278','sneaker','Object.prePersist','2025-05-21 14:07:25.426773'),(3,'Object.prePersist','2025-05-21 14:07:53.727123',NULL,1,'211302197807191217','安然','ojqzL0-hOlek3HMyLjhvKjTfDnnA','13651895278','煌豆','Object.prePersist','2025-05-21 14:07:53.727534'),(4,'Object.prePersist','2025-05-21 14:10:11.847432',NULL,1,'211302197807191217','安然','ojqzL0-hOlek3HMyLjhvKjTfDnnA','13651895278','煌哦度','Object.prePersist','2025-05-21 14:10:11.848019'),(5,'Object.prePersist','2025-05-21 14:11:08.611129',NULL,1,'211302197807191217','安然','ojqzL0-hOlek3HMyLjhvKjTfDnnA','13651895278','煌哦度','Object.prePersist','2025-05-21 14:11:08.611331'),(6,'Object.prePersist','2025-05-21 14:18:07.028603',NULL,1,'211302197807191217','安然','ojqzL0-hOlek3HMyLjhvKjTfDnnA','13651895278','煌哦度','Object.prePersist','2025-05-21 14:18:07.029423');
/*!40000 ALTER TABLE `app_customer_contacts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_manage_role`
--

DROP TABLE IF EXISTS `app_manage_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_manage_role` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者open_id',
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL COMMENT '逻辑删除，默认为0未删除，1已删除',
  `menu_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `role_code` int DEFAULT NULL,
  `role_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人open_id',
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_manage_role`
--

LOCK TABLES `app_manage_role` WRITE;
/*!40000 ALTER TABLE `app_manage_role` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_manage_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_manage_user`
--

DROP TABLE IF EXISTS `app_manage_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_manage_user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系统创建-给相关类名',
  `create_time` datetime(6) DEFAULT NULL COMMENT '记录创建时间',
  `deleted` tinyint DEFAULT NULL,
  `menu_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '菜单，以逗号分隔',
  `mobile` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
  `name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户昵称',
  `open_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录用户open_id',
  `remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `role_code` bigint DEFAULT NULL COMMENT '角色，可配置多个角色，采用位运算',
  `status` tinyint DEFAULT NULL COMMENT '状态：1启用；2禁用；4其他',
  `unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录用户统一识别码',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者unionid-可为用户、管理员、系统等',
  `update_time` datetime(6) DEFAULT NULL COMMENT '记录修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_manage_user`
--

LOCK TABLES `app_manage_user` WRITE;
/*!40000 ALTER TABLE `app_manage_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_manage_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_media`
--

DROP TABLE IF EXISTS `app_media`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_media` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '媒体内容自增id',
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(6) DEFAULT NULL COMMENT '记录创建时间',
  `deleted` tinyint DEFAULT NULL COMMENT '逻辑删除，默认为0未删除，1已删除',
  `md5_sum` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件校验，避免重复存储。',
  `media_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '媒体文件名（不带后缀名）',
  `media_showname` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '显示名称，相同文件可显示名称不同',
  `media_size` bigint DEFAULT NULL COMMENT '文件大小',
  `media_src` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件存储路径',
  `media_status` tinyint DEFAULT NULL COMMENT '已删除，正常，已隐藏',
  `media_type` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '媒体文件类型',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime(6) DEFAULT NULL COMMENT '记录修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_media`
--

LOCK TABLES `app_media` WRITE;
/*!40000 ALTER TABLE `app_media` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_media` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_operate_log`
--

DROP TABLE IF EXISTS `app_operate_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_operate_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `operate_params` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `operate_time` datetime(6) DEFAULT NULL,
  `operate_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `operator` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `operator_browser` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `operator_ip` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=255 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_operate_log`
--

LOCK TABLES `app_operate_log` WRITE;
/*!40000 ALTER TABLE `app_operate_log` DISABLE KEYS */;
INSERT INTO `app_operate_log` VALUES (1,'','2025-05-16 20:14:18.112171','/spup/app/activityRound/mySubmitted','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(2,'','2025-05-16 20:14:54.303823','/spup/app/activityRound/mySubmitted','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(3,'','2025-05-17 20:30:31.023244','/spup/error','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(4,'','2025-05-17 20:31:41.099398','/spup/error','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(5,'','2025-05-17 20:33:30.882148','/spup/error','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(6,'','2025-05-17 20:34:55.085016','/spup/error','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(7,'','2025-05-17 20:35:06.209697','/spup/error','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(8,'','2025-05-17 21:07:08.881605','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36','0:0:0:0:0:0:0:1'),(9,'','2025-05-20 11:29:14.441351','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 15_3_2) AppleWebKit/537.36 (KHTML, like Gecko) QtWebEngine/5.12.10 Chrome/69.0.3497.128 Safari/537.36 HBuilderX','0:0:0:0:0:0:0:1'),(10,'','2025-05-20 11:29:16.412203','/spup/instructions/get','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 15_3_2) AppleWebKit/537.36 (KHTML, like Gecko) QtWebEngine/5.12.10 Chrome/69.0.3497.128 Safari/537.36 HBuilderX','0:0:0:0:0:0:0:1'),(11,'','2025-05-20 11:29:16.424182','/spup/instructions/get','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 15_3_2) AppleWebKit/537.36 (KHTML, like Gecko) QtWebEngine/5.12.10 Chrome/69.0.3497.128 Safari/537.36 HBuilderX','0:0:0:0:0:0:0:1'),(12,'','2025-05-20 11:29:35.009335','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(13,'','2025-05-20 11:36:45.920555','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(14,'','2025-05-20 11:36:46.515355','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(15,'','2025-05-20 11:40:21.115575','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(16,'','2025-05-20 11:40:22.969865','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(17,'','2025-05-20 11:41:19.893911','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(18,'','2025-05-20 11:41:21.649661','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(19,'','2025-05-20 12:05:48.503728','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(20,'','2025-05-20 12:05:50.563939','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(21,'','2025-05-20 12:06:32.095690','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(22,'','2025-05-20 12:06:38.233020','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(23,'','2025-05-20 12:06:39.593896','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(24,'','2025-05-20 12:06:41.446250','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(25,'','2025-05-20 12:06:42.575464','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(26,'','2025-05-20 12:06:45.561442','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(27,'','2025-05-20 12:06:46.827419','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(28,'','2025-05-20 12:09:38.040351','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(29,'','2025-05-20 12:09:53.182633','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(30,'','2025-05-20 12:17:47.070038','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(31,'','2025-05-20 12:18:44.446104','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(32,'','2025-05-20 12:18:45.251364','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(33,'','2025-05-20 12:20:07.027160','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(34,'','2025-05-20 12:20:17.697512','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(35,'','2025-05-20 12:21:14.526670','/spup/customer/saveCustomer','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(36,'','2025-05-20 12:21:14.538080','/spup/customer/saveCustomer','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(37,'','2025-05-20 12:22:15.639434','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(38,'','2025-05-20 12:22:34.932098','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(39,'','2025-05-20 12:24:12.730124','/spup/customer/saveCustomer','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(40,'','2025-05-20 12:24:12.748183','/spup/customer/saveCustomer','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(41,'','2025-05-20 12:29:45.201592','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(42,'','2025-05-20 12:30:23.100872','/spup/instructions/get','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(43,'','2025-05-20 12:30:23.107621','/spup/instructions/get','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(44,'','2025-05-20 12:33:56.130921','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(45,'','2025-05-20 12:33:57.249815','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(46,'','2025-05-20 12:33:57.915705','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(47,'','2025-05-21 09:48:01.903360','/spup/appointment/initMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(48,'','2025-05-21 09:48:01.955615','/spup/error','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(49,'param=start','2025-05-21 09:48:40.411731','/spup/appointment/initMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(50,'param=start','2025-05-21 09:48:40.423933','/spup/error','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(51,'param=start','2025-05-21 09:51:24.716039','/spup/appointment/initMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(52,'param=start','2025-05-21 09:51:24.756002','/spup/error','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(53,'param=start','2025-05-21 09:51:56.192795','/spup/appointment/initItem','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(54,'param=start','2025-05-21 09:51:56.195457','/spup/appointment/initItem','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(55,'param=start','2025-05-21 09:53:37.006982','/spup/appointment/getDetailByDate/4/fypd/20250525','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(56,'param=start','2025-05-21 09:53:43.943174','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(57,'param=start','2025-05-21 09:53:48.468264','/spup/appointment/getDetailByDate/4/fypd/20250522','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(58,'param=start','2025-05-21 09:53:54.696784','/spup/appointment/getDetailByDate/4/fypd/20250523','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(59,'param=start','2025-05-21 09:55:36.983659','/spup/appointment/getDetailByDate/4/fypd/20250522','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(60,'param=start','2025-05-21 09:56:25.764456','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(61,'param=start','2025-05-21 09:56:33.119199','/spup/appointment/getDetailByDate/4/fypd/20250526','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(62,'param=start','2025-05-21 09:56:47.811905','/spup/appointment/getDetailByDate/4/fypd/20250522','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(63,'param=start','2025-05-21 09:57:25.929808','/spup/appointment/getDetailByDate/4/fypd/20250518','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(64,'param=start','2025-05-21 09:57:29.527041','/spup/appointment/getDetailByDate/4/fypd/20250519','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(65,'param=start','2025-05-21 10:20:39.038793','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(66,'param=start','2025-05-21 10:20:45.430495','/spup/appointment/getDetailByDate/4/fypd/20250526','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(67,'param=start','2025-05-21 10:21:25.611687','/spup/appointment/getDetailByDate/4/fypd/20250526','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(68,'param=start','2025-05-21 10:21:39.114497','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(69,'param=start','2025-05-21 10:22:33.667511','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(70,'param=start','2025-05-21 10:23:13.544405','/spup/appointment/getDetailByDate/4/fypd/20250522','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(71,'param=start','2025-05-21 10:23:49.188591','/spup/appointment/getDetailByDate/4/fypd/20250522','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(72,'param=start','2025-05-21 10:24:30.579439','/spup/appointment/getDetailByDate/4/fypd/20250522','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(73,'param=start','2025-05-21 10:24:34.806461','/spup/appointment/getDetailByDate/4/fypd/20250520','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(74,'param=start','2025-05-21 10:24:39.569499','/spup/appointment/getDetailByDate/4/fypd/20250526','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(75,'param=start','2025-05-21 10:40:40.576842','/spup/appointment/getDetailByDate/4/fypd/20250526','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(76,'param=start','2025-05-21 10:42:20.503142','/spup/appointment/getDetailByDate/4/fypd/20250522','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(77,'param=start','2025-05-21 10:52:52.459317','/spup/appointment/getDetailByDate/4/fypd/20250522','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(78,'param=start','2025-05-21 10:56:07.640015','/spup/appointment/getDetailByDate/4/fypd/20250522','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(79,'dateStr=20250525','2025-05-21 11:10:23.033649','/spup/appointment/initItem','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(80,'dateStr=20250525','2025-05-21 11:10:23.058962','/spup/appointment/initItem','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(81,'dateStr=20250525','2025-05-21 11:14:26.581208','/spup/appointment/initItem','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(82,'dateStr=20250525','2025-05-21 11:14:26.590974','/spup/appointment/initItem','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(83,'dateStr=20250523','2025-05-21 11:15:55.972425','/spup/appointment/initItem','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(84,'dateStr=20250523','2025-05-21 11:15:55.975374','/spup/appointment/initItem','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(85,'dateStr=20250526','2025-05-21 11:16:44.208566','/spup/appointment/initItem','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(86,'dateStr=20250526','2025-05-21 11:16:44.210101','/spup/appointment/initItem','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(87,'dateStr=20250524','2025-05-21 11:17:12.740587','/spup/appointment/initItem','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(88,'dateStr=20250524','2025-05-21 11:17:12.742900','/spup/appointment/initItem','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(89,'param=start','2025-05-21 11:17:42.021354','/spup/appointment/getDetailByDate/4/fypd/20250524','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(90,'param=start','2025-05-21 11:17:50.144685','/spup/appointment/getDetailByDate/4/fypd/20250525','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(91,'param=start','2025-05-21 11:18:00.258997','/spup/appointment/getDetailByDate/4/fypd/20250522','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(92,'param=start','2025-05-21 11:44:41.490301','/spup/appointment/getDetailByDate/4/fypd/20250525','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(93,'param=start','2025-05-21 11:45:47.427005','/spup/appointment/getDetailByDate/4/fypd/20250525','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(94,'param=start','2025-05-21 11:47:37.056235','/spup/appointment/getDetailByDate/4/fypd/20250525','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(95,'param=start','2025-05-21 11:50:02.354061','/spup/appointment/getDetailByDate/4/fypd/20250525','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(96,'','2025-05-21 11:51:26.358909','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(97,'','2025-05-21 11:51:26.473393','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(98,'','2025-05-21 11:53:46.976931','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(99,'','2025-05-21 11:53:47.307517','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(100,'','2025-05-21 11:54:30.339959','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(101,'','2025-05-21 11:54:30.673904','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(102,'','2025-05-21 12:23:07.810402','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(103,'param=start','2025-05-21 12:49:38.277355','/spup/appointment/getDetailByDate/4/fypd/20250525','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(104,'','2025-05-21 13:36:01.766780','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(105,'','2025-05-21 13:36:38.851044','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(106,'','2025-05-21 13:36:40.708793','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(107,'','2025-05-21 13:38:58.674049','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(108,'','2025-05-21 13:43:54.603188','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(109,'','2025-05-21 13:44:21.360951','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(110,'','2025-05-21 13:44:26.771439','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(111,'','2025-05-21 13:44:35.901322','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(112,'','2025-05-21 13:51:46.199984','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(113,'','2025-05-21 13:51:46.204678','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(114,'','2025-05-21 13:53:39.096577','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(115,'','2025-05-21 13:56:59.853298','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(116,'','2025-05-21 13:57:36.507536','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(117,'','2025-05-21 14:01:29.124920','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(118,'','2025-05-21 14:01:36.959705','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(119,'','2025-05-21 14:02:55.006580','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(120,'','2025-05-21 14:02:55.012663','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(121,'','2025-05-21 14:03:01.124104','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(122,'','2025-05-21 14:03:11.288018','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(123,'','2025-05-21 14:03:16.401125','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(124,'','2025-05-21 14:03:28.271962','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(125,'','2025-05-21 14:05:54.824054','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(126,'','2025-05-21 14:06:01.105594','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(127,'','2025-05-21 14:06:57.229990','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(128,'','2025-05-21 14:07:21.305203','/spup/customer/addContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(129,'','2025-05-21 14:07:25.429417','/spup/customer/addContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(130,'','2025-05-21 14:07:53.735696','/spup/customer/addContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(131,'','2025-05-21 14:09:05.465856','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(132,'','2025-05-21 14:09:19.765517','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(133,'','2025-05-21 14:09:38.551767','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(134,'','2025-05-21 14:09:38.561467','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(135,'','2025-05-21 14:09:44.795193','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(136,'','2025-05-21 14:10:11.854490','/spup/customer/addContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(137,'','2025-05-21 14:11:08.615573','/spup/customer/addContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(138,'','2025-05-21 14:18:07.039173','/spup/customer/addContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(139,'','2025-05-21 14:19:28.089879','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(140,'','2025-05-21 14:19:31.088901','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(141,'','2025-05-21 14:19:38.817533','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(142,'','2025-05-21 14:19:49.785675','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(143,'','2025-05-21 14:20:33.710597','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(144,'','2025-05-21 14:22:48.125555','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(145,'','2025-05-21 14:24:25.386205','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(146,'','2025-05-21 14:25:03.908978','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(147,'','2025-05-21 14:25:07.207526','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(148,'','2025-05-21 14:25:15.156525','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(149,'','2025-05-21 14:25:42.373562','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(150,'','2025-05-21 14:25:44.398498','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(151,'','2025-05-21 14:25:55.690409','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(152,'','2025-05-21 14:43:15.784885','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(153,'','2025-05-21 14:43:23.565243','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(154,'','2025-05-21 14:43:27.389119','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(155,'','2025-05-21 14:48:21.258132','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(156,'','2025-05-21 14:49:38.783987','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(157,'','2025-05-21 14:49:51.462728','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(158,'','2025-05-21 14:51:29.315244','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(159,'','2025-05-21 14:51:35.859774','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(160,'','2025-05-21 14:55:38.960790','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(161,'','2025-05-21 15:05:35.484442','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(162,'','2025-05-21 15:05:35.496215','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(163,'','2025-05-21 15:05:50.788744','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(164,'','2025-05-21 15:05:50.803375','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(165,'','2025-05-21 15:05:54.216713','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(166,'','2025-05-21 15:05:54.224973','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(167,'','2025-05-21 15:06:00.768353','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(168,'','2025-05-21 15:06:00.778616','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(169,'','2025-05-21 15:06:46.784255','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(170,'','2025-05-21 15:06:46.792429','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(171,'','2025-05-21 15:08:28.211929','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(172,'','2025-05-21 15:08:43.066072','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(173,'','2025-05-21 15:09:04.686111','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(174,'','2025-05-21 15:10:41.715408','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(175,'','2025-05-21 15:10:50.186034','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(176,'','2025-05-21 15:11:13.870769','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(177,'','2025-05-21 15:11:18.014069','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(178,'','2025-05-21 15:11:18.369755','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(179,'','2025-05-21 15:11:30.274627','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(180,'','2025-05-21 15:12:03.264207','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(181,'','2025-05-21 15:13:07.306959','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(182,'','2025-05-21 15:13:13.133022','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(183,'','2025-05-21 15:31:51.608021','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(184,'','2025-05-21 15:31:51.599729','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(185,'','2025-05-21 15:31:55.197889','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(186,'','2025-05-21 15:31:55.206082','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(187,'','2025-05-21 15:32:06.814428','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(188,'','2025-05-21 15:32:32.476075','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(189,'','2025-05-21 15:32:33.296982','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(190,'','2025-05-21 15:32:34.985580','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(191,'','2025-05-21 15:32:37.482095','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(192,'','2025-05-21 15:32:51.518406','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(193,'','2025-05-21 15:32:57.707750','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(194,'','2025-05-21 15:33:01.388294','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(195,'','2025-05-21 15:35:40.367039','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(196,'','2025-05-21 15:35:44.601674','/spup/instructions/get','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(197,'','2025-05-21 15:35:44.608405','/spup/instructions/get','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(198,'','2025-05-21 15:36:30.206706','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(199,'','2025-05-21 15:38:33.885215','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(200,'','2025-05-21 15:39:31.580795','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(201,'','2025-05-21 15:39:38.273543','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(202,'','2025-05-21 15:39:45.498139','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(203,'','2025-05-21 15:41:04.568268','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(204,'','2025-05-21 15:41:37.437379','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(205,'','2025-05-21 15:41:54.688046','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(206,'','2025-05-21 15:42:34.858345','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(207,'','2025-05-21 15:43:14.565507','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(208,'','2025-05-21 15:50:42.164414','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(209,'','2025-05-21 15:50:49.682762','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(210,'','2025-05-21 15:52:21.681424','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(211,'','2025-05-21 15:52:32.619670','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(212,'','2025-05-21 15:54:04.152241','/spup/exhibition/list','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(213,'','2025-05-21 15:54:32.279199','/spup/customer/getContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(214,'','2025-05-21 15:54:41.134230','/spup/appointment/getDetailByDate/4/fypd/20250521','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(215,'','2025-05-21 15:54:44.525685','/spup/customer/getAvaildContacts','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 HBuilderX','0:0:0:0:0:0:0:1'),(216,'param=start','2025-05-21 16:37:33.283708','/spup/anniversary/activity-map','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(217,'param=start','2025-05-21 16:37:33.327478','/spup/error','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(218,'param=start','2025-05-21 16:38:04.270604','/spup/anniversary/activity-map','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(219,'param=start','2025-05-21 16:38:04.279225','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(220,'param=start','2025-05-21 16:38:04.282327','/spup/error','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(221,'param=start','2025-05-21 16:41:01.549367','/spup/anniversary/activity-map','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(222,'param=start','2025-05-21 16:41:01.559390','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(223,'param=start','2025-05-21 16:41:01.567649','/spup/error','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(224,'param=start','2025-05-21 16:42:32.564190','/spup/anniversary/activity-map','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(225,'param=start','2025-05-21 16:42:32.574104','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(226,'param=start','2025-05-21 16:42:32.582175','/spup/error','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(227,'param=start','2025-05-21 16:50:06.420860','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Apidog/1.0.0 (https://apidog.com)','0:0:0:0:0:0:0:1'),(228,'','2025-05-21 16:50:18.705324','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(229,'','2025-05-21 16:50:18.727297','/spup/images/activity-map.jpg;jsessionid=E09BAEE8C6EF38772A1DC051A35AC8F8','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(230,'','2025-05-21 16:50:18.840520','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(231,'','2025-05-21 16:50:18.846560','/spup/images/activity-map.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(232,'','2025-05-21 16:59:38.895624','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(233,'','2025-05-21 16:59:38.911174','/spup/images/activity-map.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(234,'','2025-05-21 16:59:38.917416','/spup/images/activity-map.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(235,'','2025-05-21 17:00:26.402373','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(236,'','2025-05-21 17:00:43.810146','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(237,'','2025-05-21 17:00:43.820262','/spup/images/activity-map.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(238,'','2025-05-21 17:00:43.820259','/spup/images/activity-map.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(239,'','2025-05-21 17:00:43.823692','/spup/images/activity-map.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(240,'','2025-05-21 17:00:43.824286','/spup/images/activity-map.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(241,'','2025-05-21 17:01:21.742280','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(242,'','2025-05-21 17:01:53.879308','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(243,'','2025-05-21 17:01:53.889811','/spup/images/schedule-table.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(244,'','2025-05-21 17:01:53.883867','/spup/images/activity-map.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(245,'','2025-05-21 17:01:53.901459','/spup/images/activity-map.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(246,'','2025-05-21 17:02:57.603705','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(247,'','2025-05-21 17:02:57.614847','/spup/images/schedule-table.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(248,'','2025-05-21 17:02:57.617520','/spup/images/schedule-table.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15','0:0:0:0:0:0:0:1'),(249,'','2025-05-21 17:03:22.487699','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36','0:0:0:0:0:0:0:1'),(250,'','2025-05-21 17:03:22.508675','/spup/images/activity-map.jpg;jsessionid=2460BD57B7BCD50F8A143ACACE09F1CF','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36','0:0:0:0:0:0:0:1'),(251,'','2025-05-21 17:03:22.508715','/spup/images/schedule-table.jpg;jsessionid=2460BD57B7BCD50F8A143ACACE09F1CF','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36','0:0:0:0:0:0:0:1'),(252,'','2025-05-21 17:05:40.825302','/spup/anniversary/activityMap','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36','0:0:0:0:0:0:0:1'),(253,'','2025-05-21 17:05:40.848167','/spup/images/activity-map.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36','0:0:0:0:0:0:0:1'),(254,'','2025-05-21 17:05:40.848676','/spup/images/schedule-table.jpg','ojqzL0-hOlek3HMyLjhvKjTfDnnA','Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36','0:0:0:0:0:0:0:1');
/*!40000 ALTER TABLE `app_operate_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_surrounding_goods`
--

DROP TABLE IF EXISTS `app_surrounding_goods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_surrounding_goods` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `goods_category` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品分类',
  `goods_conver_picture` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `goods_introduce` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品介绍',
  `goods_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',
  `goods_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品编号',
  `goods_price` double DEFAULT NULL COMMENT '商品价格',
  `goods_status` tinyint DEFAULT NULL COMMENT '商品状态：1上架；2下架；4其他',
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_surrounding_goods`
--

LOCK TABLES `app_surrounding_goods` WRITE;
/*!40000 ALTER TABLE `app_surrounding_goods` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_surrounding_goods` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_temporary_exhibition`
--

DROP TABLE IF EXISTS `app_temporary_exhibition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_temporary_exhibition` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `detail_banner_pic` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `exhibition_address` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `exhibition_content` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `exhibition_end_date` date DEFAULT NULL,
  `exhibition_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `exhibition_start_date` date DEFAULT NULL,
  `exhibition_title` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `thumbnail_pic` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_temporary_exhibition`
--

LOCK TABLES `app_temporary_exhibition` WRITE;
/*!40000 ALTER TABLE `app_temporary_exhibition` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_temporary_exhibition` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_visit_guide`
--

DROP TABLE IF EXISTS `app_visit_guide`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_visit_guide` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `page_views` int DEFAULT NULL,
  `show_imgs` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `show_voices` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sort_code` int DEFAULT NULL COMMENT '排序值',
  `spot_area` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所在区域（楼层）',
  `spot_content` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '展项介绍',
  `spot_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '展项名称',
  `tips` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_visit_guide`
--

LOCK TABLES `app_visit_guide` WRITE;
/*!40000 ALTER TABLE `app_visit_guide` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_visit_guide` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_workday`
--

DROP TABLE IF EXISTS `app_workday`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_workday` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `config` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作日配置',
  `day` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `day_remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `is_workday` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_workday`
--

LOCK TABLES `app_workday` WRITE;
/*!40000 ALTER TABLE `app_workday` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_workday` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_workday_temporary_exhibition`
--

DROP TABLE IF EXISTS `app_workday_temporary_exhibition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `app_workday_temporary_exhibition` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `day` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `day_remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `is_workday` tinyint DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_workday_temporary_exhibition`
--

LOCK TABLES `app_workday_temporary_exhibition` WRITE;
/*!40000 ALTER TABLE `app_workday_temporary_exhibition` DISABLE KEYS */;
/*!40000 ALTER TABLE `app_workday_temporary_exhibition` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `art_center_info`
--

DROP TABLE IF EXISTS `art_center_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `art_center_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `address` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `introduction` varchar(2000) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `metro` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `open_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `pic_info` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `section` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `traffic` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `art_center_info`
--

LOCK TABLES `art_center_info` WRITE;
/*!40000 ALTER TABLE `art_center_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `art_center_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `black_list`
--

DROP TABLE IF EXISTS `black_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `black_list` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `category` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `locking_date_time` datetime(6) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `unlocking_date_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `black_list`
--

LOCK TABLES `black_list` WRITE;
/*!40000 ALTER TABLE `black_list` DISABLE KEYS */;
/*!40000 ALTER TABLE `black_list` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `comm_questionnaire`
--

DROP TABLE IF EXISTS `comm_questionnaire`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `comm_questionnaire` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `content` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `options` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `title` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `valid_end_date` datetime(6) DEFAULT NULL,
  `valid_start_date` datetime(6) DEFAULT NULL,
  `deleted` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `comm_questionnaire`
--

LOCK TABLES `comm_questionnaire` WRITE;
/*!40000 ALTER TABLE `comm_questionnaire` DISABLE KEYS */;
/*!40000 ALTER TABLE `comm_questionnaire` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `comm_questionnaire_answer`
--

DROP TABLE IF EXISTS `comm_questionnaire_answer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `comm_questionnaire_answer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `answer` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `questionnaire_id` bigint DEFAULT NULL,
  `unionid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `comm_questionnaire_answer`
--

LOCK TABLES `comm_questionnaire_answer` WRITE;
/*!40000 ALTER TABLE `comm_questionnaire_answer` DISABLE KEYS */;
/*!40000 ALTER TABLE `comm_questionnaire_answer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `exhibition_info`
--

DROP TABLE IF EXISTS `exhibition_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exhibition_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `exhibition_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `exhibition_status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `exhibition_type` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `deleted` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `exhibition_info`
--

LOCK TABLES `exhibition_info` WRITE;
/*!40000 ALTER TABLE `exhibition_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `exhibition_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `hibernate_sequence`
--

DROP TABLE IF EXISTS `hibernate_sequence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `hibernate_sequence` (
  `next_val` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `hibernate_sequence`
--

LOCK TABLES `hibernate_sequence` WRITE;
/*!40000 ALTER TABLE `hibernate_sequence` DISABLE KEYS */;
INSERT INTO `hibernate_sequence` VALUES (1);
/*!40000 ALTER TABLE `hibernate_sequence` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mp_datacube_everday`
--

DROP TABLE IF EXISTS `mp_datacube_everday`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mp_datacube_everday` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `analysis_date` date DEFAULT NULL,
  `article_read_total` int DEFAULT NULL,
  `article_summary_json` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文章阅读总数',
  `article_total` int DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_on` datetime(6) DEFAULT NULL,
  `deleted` tinyint DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_on` datetime(6) DEFAULT NULL,
  `user_cumulate` int DEFAULT NULL,
  `user_cumulate_json` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户总数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mp_datacube_everday`
--

LOCK TABLES `mp_datacube_everday` WRITE;
/*!40000 ALTER TABLE `mp_datacube_everday` DISABLE KEYS */;
/*!40000 ALTER TABLE `mp_datacube_everday` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mp_datacube_everday_article_summary`
--

DROP TABLE IF EXISTS `mp_datacube_everday_article_summary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mp_datacube_everday_article_summary` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `add_to_fav_count` int DEFAULT NULL,
  `add_to_fav_user` int DEFAULT NULL,
  `analysis_date` date DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_on` datetime(6) DEFAULT NULL,
  `msg_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `send_date` date DEFAULT NULL,
  `title` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `total_read_count` int DEFAULT NULL,
  `total_read_user` int DEFAULT NULL,
  `total_share_count` int DEFAULT NULL,
  `total_share_user` int DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_on` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mp_datacube_everday_article_summary`
--

LOCK TABLES `mp_datacube_everday_article_summary` WRITE;
/*!40000 ALTER TABLE `mp_datacube_everday_article_summary` DISABLE KEYS */;
/*!40000 ALTER TABLE `mp_datacube_everday_article_summary` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mp_datacube_everday_user_summary`
--

DROP TABLE IF EXISTS `mp_datacube_everday_user_summary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mp_datacube_everday_user_summary` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `analysis_date` date DEFAULT NULL,
  `article_summary_json` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文章阅读总数',
  `cancel_user` int DEFAULT NULL,
  `create_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_on` datetime(6) DEFAULT NULL,
  `new_user` int DEFAULT NULL,
  `total_visits` bigint DEFAULT NULL,
  `update_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_on` datetime(6) DEFAULT NULL,
  `user_cumulate` int DEFAULT NULL,
  `visits` bigint DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mp_datacube_everday_user_summary`
--

LOCK TABLES `mp_datacube_everday_user_summary` WRITE;
/*!40000 ALTER TABLE `mp_datacube_everday_user_summary` DISABLE KEYS */;
/*!40000 ALTER TABLE `mp_datacube_everday_user_summary` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `one_time_tasks`
--

DROP TABLE IF EXISTS `one_time_tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `one_time_tasks` (
  `id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `deleted` int DEFAULT NULL,
  `execute_time` datetime(6) DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `task_class` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `task_data` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `task_type` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `one_time_tasks`
--

LOCK TABLES `one_time_tasks` WRITE;
/*!40000 ALTER TABLE `one_time_tasks` DISABLE KEYS */;
INSERT INTO `one_time_tasks` VALUES ('4cbf1ea7-b7a9-41d8-8fb4-8a55cd7ccc00','2025-05-15 21:34:45.074000',0,'2025-05-16 05:27:00.000000','PENDING','com.spup.task.BatchOverwriteTask','[{\"batchNo\":\"20240515001\",\"batchDate\":\"20240515\",\"batchStartTime\":\"0900\",\"batchEndTime\":\"1700\",\"ticketTotal\":200,\"ticketRemaining\":200,\"batchStatus\":1,\"batchCategory\":4,\"batchRemark\":\"Overwrite test batch\"}]','OVERWRITE_ITEM_BATCH');
/*!40000 ALTER TABLE `one_time_tasks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `round_config`
--

DROP TABLE IF EXISTS `round_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `round_config` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `end_time` time DEFAULT NULL,
  `exhibition_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `round_date` date DEFAULT NULL,
  `round_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `round_status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `deleted` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `round_config`
--

LOCK TABLES `round_config` WRITE;
/*!40000 ALTER TABLE `round_config` DISABLE KEYS */;
/*!40000 ALTER TABLE `round_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `volunteer_info`
--

DROP TABLE IF EXISTS `volunteer_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `volunteer_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `age` int DEFAULT NULL,
  `area` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `category` int DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `fixed_tel` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gender` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `service_duration` double DEFAULT NULL,
  `skill` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `uid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `unit` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `volunteer_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `deleted` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `volunteer_info`
--

LOCK TABLES `volunteer_info` WRITE;
/*!40000 ALTER TABLE `volunteer_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `volunteer_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'web_spup_test'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-22 23:01:43
